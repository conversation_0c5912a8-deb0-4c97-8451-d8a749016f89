﻿using Alsi.Tab.Kit.Core.Services.Capl.Core;
using ArxmlModel = AUTOSAR;

namespace Alsi.Tab.Kit.Core.Services.Capl.Params
{
    internal class CarConfigFrameIdLoader : ArxmlLoaderBase
    {
        public CarConfigFrameIdLoader(ArxmlLoaderContext context)
            : base(context)
        {
        }

        public override void Load()
        {
            var ecuInstances = arxmlModel.EcuInstances;
            foreach (var ecuInstance in ecuInstances)
            {
                var ecuNode = ecuInstance.SHORTNAME.Value;
                {
                    const string targetSystemGroupName = "VehCfgPrm";
                    if (!arxmlModel.TryGetSignalGroupName(targetSystemGroupName, out var signalGroupNames))
                    {
                        return;
                    }
                    foreach (var signalGroupName in signalGroupNames)
                    {
                        if (!arxmlModel.TryGetFrameIdBySignalGroupName(signalGroupName, out var frameId))
                        {
                            return;
                        }
                        var value = frameId.ToString();
                        var key = CaplParamConsts.CARCONFIG_FRAMEID;
                        collection.AddValue(ecuNode, key, value, source, order);
                    }
                }

                {
                    const string targetSystemGroupName = "VehCfgPrmExt";
                    if (!arxmlModel.TryGetSignalGroupName(targetSystemGroupName, out var signalGroupNames))
                    {
                        return;
                    }
                    foreach (var signalGroupName in signalGroupNames)
                    {
                        if (!arxmlModel.TryGetFrameIdBySignalGroupName(signalGroupName, out var frameId))
                        {
                            return;
                        }
                        var value = frameId.ToString();
                        var key = CaplParamConsts.CARCONFIGEXTEND_FRAMEID;
                        collection.AddValue(ecuNode, key, value, source, order);
                    }
                }
            }
        }
    }
}
