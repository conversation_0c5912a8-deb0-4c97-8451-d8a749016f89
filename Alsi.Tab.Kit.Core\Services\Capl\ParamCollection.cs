﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using static Alsi.Tab.Kit.Core.Services.Capl.Params.ParamCollection.RegularParamValueSource;

namespace Alsi.Tab.Kit.Core.Services.Capl.Params
{
    public class ParamCollection : IParamCollection
    {
        public List<Ecu> Ecus { get; } = new List<Ecu>();

        private List<List<string>> EcuAliasGroups = new List<List<string>>();

        public void AddEcuAlias(string ecuNameA, string ecuNameB)
        {
            var group = EcuAliasGroups.FirstOrDefault(g => g.Any(x => x == ecuNameA) || g.Any(x => x == ecuNameB));
            if (group == null)
            {
                EcuAliasGroups.Add(new List<string> { ecuNameA, ecuNameB });
                return;
            }

            if (!group.Contains(ecuNameA))
            {
                group.Add(ecuNameA);
            }

            if (!group.Contains(ecuNameB))
            {
                group.Add(ecuNameB);
            }
        }

        public string[] GetEcuAliases(string ecuName)
        {
            var group = EcuAliasGroups.FirstOrDefault(g => g.Any(x => x == ecuName));
            if (group != null)
            {
                return group.ToArray();
            }
            return new[] { ecuName };
        }


        public void AddValue(string ecuNode, string key, string value, string source, int order)
        {
            var ecuAliases = GetEcuAliases(ecuNode);
            foreach (var ecuAlias in ecuAliases)
            {
                var ecu = Ecus.FirstOrDefault(x => x.EcuNode == ecuAlias);
                if (ecu == null)
                {
                    continue;
                }

                var regularParamValueSource = ecu.RegularParamValueSources.FirstOrDefault(x => x.RegularParamKey == key);
                if (regularParamValueSource == null)
                {
                    regularParamValueSource = new RegularParamValueSource(key);
                    ecu.RegularParamValueSources.Add(regularParamValueSource);
                }

                regularParamValueSource.AddValueSource(new ValueSource(value, source, order));
            }
        }
        public Ecu[] GetEcuNodes()
        {
            return Ecus.ToArray();
        }

        public string GetValue(string ecuNode, string regularParamKey)
        {
            return GetValueSources(ecuNode, regularParamKey).FirstOrDefault()?.Value ?? string.Empty;
        }

        private ValueSource[] GetValueSources(string ecuNode, string regularParamKey)
        {
            var ecu = Ecus.FirstOrDefault(x => x.EcuNode == ecuNode);
            if (ecu == null)
            {
                return Array.Empty<ValueSource>();
            }

            var regularParamValueSource = ecu.RegularParamValueSources.FirstOrDefault(x => x.RegularParamKey == regularParamKey);
            if (regularParamValueSource == null)
            {
                return Array.Empty<ValueSource>();
            }

            return regularParamValueSource.GetValueSources();
        }

        public Ecu GetOrAddEcu(string ecuNode)
        {
            var ecu = Ecus.FirstOrDefault(x => x.EcuNode == ecuNode);
            if (ecu == null)
            {
                ecu = new Ecu(ecuNode);
                Ecus.Add(ecu);
            }
            return ecu;
        }

        public class Ecu
        {
            public Ecu(string ecuNode)
            {
                EcuNode = ecuNode;
            }

            public string EcuNode { get; }

            public bool IsTestEcuNode { get; set; }

            public List<RegularParamValueSource> RegularParamValueSources { get; } = new List<RegularParamValueSource>();
        }

        public class RegularParamValueSource
        {
            public string RegularParamKey { get; }

            public RegularParamValueSource(string regularParamKey)
            {
                RegularParamKey = regularParamKey;
            }

            private List<ValueSource> valueSources = new List<ValueSource>();

            public ValueSource[] GetValueSources()
            {
                return valueSources.ToArray();
            }

            public void AddValueSource(ValueSource valueSource)
            {
                valueSources.Add(valueSource);
                valueSources = valueSources.OrderBy(x => x.Order).ThenBy(x => x.Source).ToList();
            }

            public override string ToString()
            {
                var stringBuilder = new StringBuilder();
                stringBuilder.Append(RegularParamKey);
                stringBuilder.Append(": " + string.Join("|", GetValueSources().Select(x => x.ToString())));
                return stringBuilder.ToString();
            }

            public class ValueSource
            {
                public ValueSource(string value, string source, int order)
                {
                    Value = value;
                    Source = source;
                    Order = order;
                }

                public string Value { get; }
                public string Source { get; }
                public int Order { get; }

                public override string ToString()
                {
                    return $"({Order}) {Value} - {Source}";
                }
            }

        }
    }
}
