using System;
using System.Collections.Generic;

namespace Alsi.Tab.Kit.Core.Models
{
    /// <summary>
    /// 源文件类型枚举
    /// </summary>
    public enum SourceFileType
    {
        Arxml,
        Sddb,
        Ldf
    }

    /// <summary>
    /// 源文件状态枚举
    /// </summary>
    public enum SourceFileStatus
    {
        Pending,    // 待解析
        Parsing,    // 解析中
        Parsed,     // 已解析
        Error       // 解析错误
    }

    /// <summary>
    /// 参数类型枚举
    /// </summary>
    public enum ParamType
    {
        String,
        Integer,
        Double,
        Boolean,
        Json,       // 复杂参数
        Array,
        Object
    }

    /// <summary>
    /// CIN 模板信息模型
    /// </summary>
    public class CinTemplate
    {
        /// <summary>
        /// 模板唯一标识（运行时计算）
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 模板文件相对路径（相对于 cin_templates 文件夹）
        /// </summary>
        public string Path { get; set; } = string.Empty;

        /// <summary>
        /// 模板名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 模板类别
        /// </summary>
        public string Category { get; set; } = string.Empty;

        /// <summary>
        /// 模板描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 模板文件完整路径（运行时计算）
        /// </summary>
        public string FullPath { get; set; } = string.Empty;

        /// <summary>
        /// 模板文件是否存在
        /// </summary>
        public bool FileExists { get; set; }
    }

    /// <summary>
    /// CIN 模板索引文件
    /// </summary>
    public class CinTemplatesIndex
    {
        public CinTemplate[] Templates { get; set; } = Array.Empty<CinTemplate>();
    }

    /// <summary>
    /// 源文件信息模型
    /// </summary>
    public class SourceFile
    {
        /// <summary>
        /// 文件唯一标识
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 文件路径
        /// </summary>
        public string Path { get; set; } = string.Empty;

        /// <summary>
        /// 文件名
        /// </summary>
        public string FileName { get; set; } = string.Empty;

        /// <summary>
        /// 文件类型
        /// </summary>
        public SourceFileType FileType { get; set; }

        /// <summary>
        /// 文件状态
        /// </summary>
        public SourceFileStatus Status { get; set; }

        /// <summary>
        /// 解析出的参数列表
        /// </summary>
        public List<ParsedParam> ParsedParams { get; set; } = new List<ParsedParam>();

        /// <summary>
        /// 添加时间
        /// </summary>
        public DateTime AddTime { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; } = string.Empty;
    }

    /// <summary>
    /// 解析出的参数信息
    /// </summary>
    public class ParsedParam
    {
        /// <summary>
        /// ECU 名
        /// </summary>
        public string EcuName { get; set; } = string.Empty;

        /// <summary>
        /// 参数名
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 参数值
        /// </summary>
        public object Value { get; set; }

        /// <summary>
        /// 参数类型
        /// </summary>
        public ParamType ParamType { get; set; }

        /// <summary>
        /// 来源文件名
        /// </summary>
        public string Source { get; set; } = string.Empty;

        /// <summary>
        /// 参数描述
        /// </summary>
        public string Description { get; set; } = string.Empty;
    }
}
