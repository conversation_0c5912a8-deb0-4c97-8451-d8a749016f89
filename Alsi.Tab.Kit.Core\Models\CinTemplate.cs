using System;
using System.Collections.Generic;

namespace Alsi.Tab.Kit.Core.Models
{
    /// <summary>
    /// 源文件类型枚举
    /// </summary>
    public enum SourceFileType
    {
        Arxml,
        Sddb,
        Ldf
    }

    /// <summary>
    /// 源文件状态枚举
    /// </summary>
    public enum SourceFileStatus
    {
        Pending,    // 待解析
        Parsing,    // 解析中
        Parsed,     // 已解析
        Error       // 解析错误
    }

    public enum ParamType
    {
        String,
        Integer,
        Double,
        Boolean,
        Json,       // 复杂参数
        Array,
        Object
    }

    /// <summary>
    /// CIN 模板信息模型
    /// </summary>
    public class CinTemplate
    {
        /// <summary>
        /// 模板唯一标识（运行时计算）
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 模板文件相对路径（相对于 cin_templates 文件夹）
        /// </summary>
        public string Path { get; set; } = string.Empty;

        /// <summary>
        /// 模板名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 模板类别
        /// </summary>
        public string Category { get; set; } = string.Empty;

        /// <summary>
        /// 模板描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 模板文件完整路径（运行时计算）
        /// </summary>
        public string FullPath { get; set; } = string.Empty;

        /// <summary>
        /// 模板文件是否存在
        /// </summary>
        public bool FileExists { get; set; }
    }

    /// <summary>
    /// CIN 模板索引文件
    /// </summary>
    public class CinTemplatesIndex
    {
        public CinTemplate[] Templates { get; set; } = Array.Empty<CinTemplate>();
    }

    public class SourceFile
    {
        public Guid Id { get; set; }
        public string Path { get; set; } = string.Empty;
        public string FileName { get; set; } = string.Empty;
        public SourceFileType FileType { get; set; }
        public SourceFileStatus Status { get; set; }
        public List<ParsedParam> ParsedParams { get; set; } = new List<ParsedParam>();
        public DateTime AddTime { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
    }

    public class ParsedParam
    {
        public string EcuName { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Value { get; set; }
        public string ParamType { get; set; }
        public string Source { get; set; } = string.Empty;
    }
}
