﻿using Alsi.Common.Parsers.Arxml.Models;
using Alsi.Common.Utils;
using Alsi.Common.Utils.Linq;
using Alsi.Tab.Kit.Core.Services.Capl.Core;
using System;
using System.Linq;

namespace Alsi.Tab.Kit.Core.Services.Capl.Params
{
    internal class BusTypeMapLoader : ArxmlLoaderBase
    {
        public BusTypeMapLoader(ArxmlLoaderContext context)
            : base(context)
        {
        }

        public override void Load()
        {
            var clusterBustypeMap = arxmlModel.ARPACKAGES.Where(x => x.SHORTNAME.Value == "CommunicationClusters")
                                 .SelectManyEx(x => x.ELEMENTS)
                                 .Where(x => x is CANCLUSTER || x is LINCLUSTER)
                                 .Select(x =>
                                 {
                                     if (x is CANCLUSTER canCluster)
                                     {
                                         if (canCluster.CANCLUSTERVARIANTS.FirstOrDefault()?.CANFDBAUDRATE?.Value != null)
                                         {
                                             return new ClusterBustypeMap(canCluster.SHORTNAME.Value, ClusterBusType.CanFd);
                                         }
                                         else
                                         {
                                             return new ClusterBustypeMap(canCluster.SHORTNAME.Value, ClusterBusType.Can);
                                         }
                                     }
                                     else if (x is LINCLUSTER linCluster)
                                     {
                                         return new ClusterBustypeMap(linCluster.SHORTNAME.Value, ClusterBusType.Lin);
                                     }
                                     throw new NotImplementedException();
                                 }).ToArray();

            foreach (var ecuInstance in arxmlModel.EcuInstances)
            {
                var node = ecuInstance.SHORTNAME.Value;

                var value = JsonUtils.Serialize(clusterBustypeMap);
                var key = CaplParamConsts.ClusterBustypeMaps;
                collection.AddValue(node, key, value, source, order);
            }

        }
    }
}
