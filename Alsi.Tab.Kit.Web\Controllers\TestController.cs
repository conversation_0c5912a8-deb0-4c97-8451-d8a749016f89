﻿using Alsi.Tab.Kit.Web.Dto;
using System;
using System.Web.Http;

namespace Alsi.Tab.Kit.Web.Controllers
{
    public class TestMode
    {
        public string Name { get; set; }

        public TestMode(string name)
        {
            Name = name;
        }
    }

    public class TestController : WebControllerBase
    {
        [HttpGet]
        [ActionName("model")]
        public IHttpActionResult GetTestModel()
        {
            return Ok(_mapper.Map<TestMode, TestModeDto>(new TestMode($"Hello, {DateTime.Now}")));
        }

        [HttpGet]
        [ActionName("progress")]
        public IHttpActionResult Progress(string taskId)
        {
            return Ok("sdf" + taskId);
        }
    }
}
