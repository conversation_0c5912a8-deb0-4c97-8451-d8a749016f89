﻿using Alsi.Common.Parsers.Arxml.Models;
using Alsi.Common.Parsers.Arxml.ModelsParsers;
using Alsi.Common.Utils;
using Alsi.Tab.Kit.Core.Services.Capl.Core;
using System.Collections.Generic;
using System.Linq;

namespace Alsi.Tab.Kit.Core.Services.Capl.Params
{
    internal class SignalRoutingLoader : ArxmlLoaderBase
    {
        public SignalRoutingLoader(ArxmlLoaderContext context)
            : base(context)
        {
        }

        public override void Load()
        {
            var clusters = arxmlModel.GetCommunicationClusters();
            List<SignalRouting> signalRoutings = new List<SignalRouting>();
            List<SignalToRoute> signalsToRoute = new List<SignalToRoute>();
            foreach (var channel in clusters)
            {
                var channelName = channel.Name;
                var busType = channel.BusType;
                foreach (var frame in channel.Frames.Where(x => x.Pdus != null))
                {
                    var frameName = frame.Name;
                    var id = frame.Id;
                    var dir = frame.Dir;
                    foreach (var pdu in frame.Pdus)
                    {
                        if (pdu == null) continue;
                        var pduName = pdu.Name;
                        foreach (var signal in pdu.Signals)
                        {
                            var signalName = signal.Name;
                            var startPosition = signal.StartPosition;
                            var length = signal.Length;
                            var updateBit = signal.UpdateBit;
                            var systemSignalName = signal.SystemSignalName;
                            var signalGroupName = "";//暂时不解析signalGroup

                            SignalToRoute signalToRoute = new SignalToRoute(signalName, channelName, busType, frameName, id,
                                dir, pduName, systemSignalName, signalGroupName, startPosition, length, updateBit);
                            signalsToRoute.Add(signalToRoute);

                        }
                    }
                }
            }
            Dictionary<string, List<SignalToRoute>> systemSignalMap = new Dictionary<string, List<SignalToRoute>>();
            foreach (var signal in signalsToRoute)
            {
                if (!systemSignalMap.TryGetValue(signal.SystemSignalName, out var signalList))
                {
                    signalList = new List<SignalToRoute>();
                    systemSignalMap[signal.SystemSignalName] = signalList;
                }
                signalList.Add(signal);
            }

            foreach (var kvp in systemSignalMap)
            {
                if (kvp.Value.Count >= 2)
                {
                    var signalList = kvp.Value;
                    var sourceSignal = signalList.Find(x => x.SignalName.EndsWith("_0") && x.Dir == Direction.Rx);
                    if (sourceSignal == null)
                    {
                        continue;
                    }
                    signalList.Remove(sourceSignal);
                    foreach (var signal in signalList)
                    {
                        if (signal.Dir == Direction.Tx)
                        {
                            SignalRouting signalRouting = new SignalRouting(sourceSignal, signal);
                            signalRoutings.Add(signalRouting);
                        }
                    }
                }
            }
            foreach (var ecuNode in arxmlEcuNodes)
            {
                var key = CaplParamConsts.SignalRouting;
                collection.AddValue(ecuNode, key, JsonUtils.Serialize(signalRoutings), source, order);
            }
        }
    }
}
