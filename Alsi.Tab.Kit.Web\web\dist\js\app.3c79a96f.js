(function(){"use strict";var e={1021:function(e,n,t){t.d(n,{GQ:function(){return g},KK:function(){return r},Mo:function(){return o},O0:function(){return a},yJ:function(){return s}});var o,r,s,a,l,c=t(4373);(function(e){e[e["Unknown"]=0]="Unknown",e[e["Asc"]=1]="Asc",e[e["Blf"]=2]="Blf"})(o||(o={})),function(e){e["Pending"]="Pending",e["Processing"]="Processing",e["Completed"]="Completed",e["Failed"]="Failed",e["Cancelled"]="Cancelled"}(r||(r={})),function(e){e["Arxml"]="Arxml",e["Sddb"]="Sddb",e["Ldf"]="Ldf"}(s||(s={})),function(e){e["Pending"]="Pending",e["Parsing"]="Parsing",e["Parsed"]="Parsed",e["Error"]="Error"}(a||(a={})),function(e){e["String"]="String",e["Integer"]="Integer",e["Double"]="Double",e["Boolean"]="Boolean",e["Json"]="Json",e["Array"]="Array",e["Object"]="Object"}(l||(l={}));const i="/api/app",u="/api/DataLogConvert",d="/api/explorer",p="/api/ExternalApps",f="/api/CinParameter",g={getAppInfo(){return c.A.get(`${i}/appInfo`)},logError:e=>c.A.post(`${i}/logError`,e),exit:()=>c.A.post(`${i}/exit`),getTestModel(){return c.A.get("api/test/model")},dataLogConvert:{selectFile(){return c.A.post(`${u}/select-file`)},startProcess(e){return c.A.post(`${u}/start`,e)},getProgress(e){return c.A.get(`${u}/progress?taskId=${e}`)},cancelProcess(e){return c.A.post(`${u}/cancel`,null,{params:{taskId:e}})}},explorer:{selectFolder(){return c.A.get(`${d}/select-folder`)},openExplorer(e){return c.A.get(`${d}/open-explorer`,{params:{path:e}})},startProcess(e){return c.A.get(`${d}/start-process`,{params:{path:e}})}},externalApps:{getList(){return c.A.get(`${p}/list`)},launch(e){return c.A.post(`${p}/launch`,e)},getIconUrl(e){return`${p}/icon?appId=${e}`}},cinParameter:{getTemplates(){return c.A.get(`${f}/templates`)},selectFile(){return c.A.post(`${f}/select-file`)},parseFile(e){return c.A.post(`${f}/parse`,e)},processFile(e){return c.A.post(`${f}/process`,e)},selectSourceFiles(){return c.A.post(`${f}/select-source-files`)},addSourceFiles(e){return c.A.post(`${f}/add-source-files`,e)},getSourceFiles(){return c.A.get(`${f}/source-files`)},parseSourceFile(e){return c.A.post(`${f}/parse-source-file`,e)},removeSourceFile(e){return c.A.post(`${f}/remove-source-file`,e)},clearSourceFiles(){return c.A.post(`${f}/clear-source-files`)},getFileHistory(){return c.A.get(`${f}/file-history`)},clearFileHistory(){return c.A.post(`${f}/clear-file-history`)}}}},3935:function(e,n,t){e.exports=t.p+"img/logo.36be161e.svg"},8347:function(e,n,t){var o=t(5130),r=t(6768),s=t(4232),a=t(3935);const l={id:"app"},c={class:"menu-header"},i={key:0,class:"logo-container"},u={key:1,class:"logo-container-collapsed"},d={class:"menu-items"},p={key:0,class:"menu-text"},f={key:0,class:"menu-text"},g={key:0,class:"menu-text"},m={class:"menu-bottom"},v={key:0,class:"menu-text"},h={class:"menu-toggle-section"};function k(e,n,t,o,k,b){const y=(0,r.g2)("router-link"),A=(0,r.g2)("font-awesome-icon"),C=(0,r.g2)("router-view");return(0,r.uX)(),(0,r.CE)("div",l,[(0,r.Lk)("div",{class:(0,s.C4)(["sidebar",{collapsed:e.isMenuCollapsed}])},[(0,r.Lk)("div",c,[e.isMenuCollapsed?((0,r.uX)(),(0,r.CE)("div",u,[(0,r.bF)(y,{to:"/"},{default:(0,r.k6)(()=>n[3]||(n[3]=[(0,r.Lk)("img",{src:a,alt:"Logo",class:"logo-small"},null,-1)])),_:1,__:[3]})])):((0,r.uX)(),(0,r.CE)("div",i,[(0,r.bF)(y,{to:"/"},{default:(0,r.k6)(()=>n[1]||(n[1]=[(0,r.Lk)("img",{src:a,alt:"Logo",class:"logo"},null,-1)])),_:1,__:[1]}),n[2]||(n[2]=(0,r.Lk)("span",{class:"app-name"},"TabKit",-1))]))]),(0,r.Lk)("div",d,[(0,r.bF)(y,{to:"/",class:"menu-item","active-class":"active"},{default:(0,r.k6)(()=>[(0,r.bF)(A,{icon:"home",class:"menu-icon"}),e.isMenuCollapsed?(0,r.Q3)("",!0):((0,r.uX)(),(0,r.CE)("span",p,"主页"))]),_:1}),(0,r.bF)(y,{to:"/log-converter",class:"menu-item","active-class":"active"},{default:(0,r.k6)(()=>[(0,r.bF)(A,{icon:"exchange-alt",class:"menu-icon"}),e.isMenuCollapsed?(0,r.Q3)("",!0):((0,r.uX)(),(0,r.CE)("span",f,"Log 转换工具"))]),_:1}),(0,r.bF)(y,{to:"/parameter-tool",class:"menu-item","active-class":"active"},{default:(0,r.k6)(()=>[(0,r.bF)(A,{icon:"book",class:"menu-icon"}),e.isMenuCollapsed?(0,r.Q3)("",!0):((0,r.uX)(),(0,r.CE)("span",g,"参数工具"))]),_:1})]),(0,r.Lk)("div",m,[(0,r.bF)(y,{to:"/about",class:"menu-item","active-class":"active"},{default:(0,r.k6)(()=>[(0,r.bF)(A,{icon:"info-circle",class:"menu-icon"}),e.isMenuCollapsed?(0,r.Q3)("",!0):((0,r.uX)(),(0,r.CE)("span",v,"关于"))]),_:1}),(0,r.Lk)("div",h,[(0,r.Lk)("div",{class:"menu-toggle",onClick:n[0]||(n[0]=(...n)=>e.toggleMenu&&e.toggleMenu(...n))},[(0,r.bF)(A,{icon:e.isMenuCollapsed?"chevron-right":"chevron-left"},null,8,["icon"])])])])],2),(0,r.Lk)("div",{class:(0,s.C4)(["main-content",{expanded:e.isMenuCollapsed}])},[(0,r.bF)(C)],2)])}var b=t(144),y=t(292),A=(0,r.pM)({name:"App",components:{FontAwesomeIcon:y.gc},setup(){const e=(0,b.KR)(!1),n=()=>{e.value=!e.value};return{isMenuCollapsed:e,toggleMenu:n}}}),C=t(1241);const _=(0,C.A)(A,[["render",k]]);var E=_,L=t(1387);const w={class:"home"},F={key:0,class:"loading-container"},x={key:1,class:"tools-grid"},$={class:"tool-icon"},P={class:"tool-features"},j={class:"tool-icon"},M={class:"tool-features"},T={key:0,class:"external-app-badge"},S={class:"tool-icon"},O=["src","alt"],I={key:1,class:"tool-features"},X={class:"tool-features"};function U(e,n,t,o,l,c){const i=(0,r.g2)("el-skeleton"),u=(0,r.g2)("font-awesome-icon"),d=(0,r.g2)("el-tag"),p=(0,r.g2)("el-card");return(0,r.uX)(),(0,r.CE)("div",w,[n[16]||(n[16]=(0,r.Lk)("div",{class:"page-header"},[(0,r.Lk)("h1",null,"TabKit"),(0,r.Lk)("p",null,"TabKit 中集成了多种实用工具")],-1)),e.loading?((0,r.uX)(),(0,r.CE)("div",F,[(0,r.bF)(i,{rows:3,animated:""})])):((0,r.uX)(),(0,r.CE)("div",x,[(0,r.bF)(p,{class:"tool-card",shadow:"hover",onClick:n[0]||(n[0]=n=>e.navigateToTool("/log-converter"))},{default:(0,r.k6)(()=>[(0,r.Lk)("div",$,[(0,r.bF)(u,{icon:"exchange-alt"})]),n[5]||(n[5]=(0,r.Lk)("h3",null,"Log 转换工具",-1)),n[6]||(n[6]=(0,r.Lk)("p",null,"支持 ASC、BLF 等日志格式转换，提供文件分割功能。",-1)),(0,r.Lk)("div",P,[(0,r.bF)(d,{size:"small"},{default:(0,r.k6)(()=>n[3]||(n[3]=[(0,r.eW)("格式转换")])),_:1,__:[3]}),(0,r.bF)(d,{size:"small",type:"success"},{default:(0,r.k6)(()=>n[4]||(n[4]=[(0,r.eW)("文件分割")])),_:1,__:[4]})])]),_:1,__:[5,6]}),(0,r.bF)(p,{class:"tool-card",shadow:"hover",onClick:n[1]||(n[1]=n=>e.navigateToTool("/parameter-tool"))},{default:(0,r.k6)(()=>[(0,r.Lk)("div",j,[(0,r.bF)(u,{icon:"cogs"})]),n[9]||(n[9]=(0,r.Lk)("h3",null,"参数工具",-1)),n[10]||(n[10]=(0,r.Lk)("p",null,"处理 CAPL 的 .cin 文件，支持模板选择和参数替换功能。",-1)),(0,r.Lk)("div",M,[(0,r.bF)(d,{size:"small"},{default:(0,r.k6)(()=>n[7]||(n[7]=[(0,r.eW)("参数替换")])),_:1,__:[7]}),(0,r.bF)(d,{size:"small",type:"success"},{default:(0,r.k6)(()=>n[8]||(n[8]=[(0,r.eW)("模板管理")])),_:1,__:[8]})])]),_:1,__:[9,10]}),((0,r.uX)(!0),(0,r.CE)(r.FK,null,(0,r.pI)(e.externalApps,t=>((0,r.uX)(),(0,r.Wv)(p,{key:t.id,class:(0,s.C4)(["tool-card external-app-card",{"app-unavailable":!t.exeExists}]),shadow:"hover",onClick:n=>e.launchExternalApp(t)},{default:(0,r.k6)(()=>[t.exeExists?(0,r.Q3)("",!0):((0,r.uX)(),(0,r.CE)("div",T,[(0,r.bF)(d,{size:"small",type:"danger"},{default:(0,r.k6)(()=>n[11]||(n[11]=[(0,r.eW)("不可用")])),_:1,__:[11]})])),(0,r.Lk)("div",S,[t.iconExists?((0,r.uX)(),(0,r.CE)("img",{key:0,src:e.getAppIconUrl(t.id),alt:t.name,class:"app-icon",onError:n[2]||(n[2]=n=>e.handleIconError(n))},null,40,O)):((0,r.uX)(),(0,r.Wv)(u,{key:1,icon:"cube",class:"default-app-icon"}))]),(0,r.Lk)("h3",null,(0,s.v_)(t.name||"Unknown App"),1),(0,r.Lk)("p",null,(0,s.v_)(t.description||"无描述信息"),1),t.tags&&t.tags.length>0?((0,r.uX)(),(0,r.CE)("div",I,[((0,r.uX)(!0),(0,r.CE)(r.FK,null,(0,r.pI)(t.tags,n=>((0,r.uX)(),(0,r.Wv)(d,{key:n,size:"small",type:e.getTagColor(n)},{default:(0,r.k6)(()=>[(0,r.eW)((0,s.v_)(n),1)]),_:2},1032,["type"]))),128))])):(0,r.Q3)("",!0)]),_:2},1032,["class","onClick"]))),128)),(0,r.bF)(p,{class:"tool-card coming-soon",shadow:"hover"},{default:(0,r.k6)(()=>[n[13]||(n[13]=(0,r.Lk)("div",{class:"tool-icon"},[(0,r.Lk)("img",{src:a,alt:"Logo",class:"logo-icon"})],-1)),n[14]||(n[14]=(0,r.Lk)("h3",null,"更多工具",-1)),n[15]||(n[15]=(0,r.Lk)("p",null,"更多工具开发中，敬请期待...",-1)),(0,r.Lk)("div",X,[(0,r.bF)(d,{size:"small",type:"info"},{default:(0,r.k6)(()=>n[12]||(n[12]=[(0,r.eW)("即将推出")])),_:1,__:[12]})])]),_:1,__:[13,14,15]})]))])}t(4114);var K=t(1021),Q=t(1219),B=(0,r.pM)({name:"HomeView",components:{FontAwesomeIcon:y.gc},setup(){const e=(0,L.rd)(),n=(0,b.KR)([]),t=(0,b.KR)(!1),o=n=>{e.push(n)},s=e=>{const n=["","success","warning","info"];let t=0;for(let o=0;o<e.length;o++)t=e.charCodeAt(o)+((t<<5)-t);return n[Math.abs(t)%n.length]},a=e=>K.GQ.externalApps.getIconUrl(e),l=e=>{const n=e.target;n.style.display="none"},c=async()=>{try{t.value=!0;const e=await K.GQ.externalApps.getList();n.value=e.data}catch(e){console.error("加载外部应用程序失败:",e)}finally{t.value=!1}},i=async e=>{if(e.exeExists)try{await K.GQ.externalApps.launch({appId:e.id}),Q.nk.success(`已启动 "${e.name}"`)}catch(n){console.error("启动外部应用程序失败:",n),Q.nk.error(n.response?.data?.message||`启动 "${e.name}" 失败`)}else Q.nk.error(`应用程序 "${e.name}" 的可执行文件不存在`)};return(0,r.sV)(()=>{c()}),{navigateToTool:o,externalApps:n,loading:t,getTagColor:s,launchExternalApp:i,getAppIconUrl:a,handleIconError:l}}});const W=(0,C.A)(B,[["render",U],["__scopeId","data-v-be6d43a8"]]);var z=W;const G=[{path:"/",name:"home",component:z},{path:"/log-converter",name:"log-converter",component:()=>t.e(488).then(t.bind(t,9936))},{path:"/parameter-tool",name:"parameter-tool",component:()=>t.e(771).then(t.bind(t,9312))},{path:"/about",name:"about",component:()=>t.e(594).then(t.bind(t,2278))}],N=(0,L.aE)({history:(0,L.LA)("/"),routes:G});var D=N,H=t(782),q=(0,H.y$)({state:{},getters:{},mutations:{},actions:{},modules:{}}),V=t(4373),R=t(2933);const J=e=>{if(!e.response||!e.response.data)return e.message||"Unknown error";const n=e.response.data,t=[];n.exceptionMessage&&t.push(n.exceptionMessage);let o=n.innerException;while(o)o.exceptionMessage&&t.push(o.exceptionMessage),o=o.innerException;return 0===t.length?n.message||"An error occurred":t.join("<br>")},Y=e=>{if(!e.response||!e.response.data)return void Q.nk.error(e.message||"Unknown error");const n=J(e);R.s.alert(n,"Error",{confirmButtonText:"OK",dangerouslyUseHTMLString:!0,closeOnClickModal:!0,closeOnPressEscape:!0,showClose:!0})},Z=e=>{if(e.response&&e.response.data){if("UserCanceled"===e.response.data)return!0;if("UserCanceled"===e.response.data.message)return!0;if("UserCanceled"===e.response.data.errorCode)return!0}return!1},ee=()=>{V.A.interceptors.response.use(e=>e,e=>Z(e)?(Q.nk.info("Operation cancelled by user"),Promise.reject(e)):(Y(e),Promise.reject(e)))};var ne=t(7854),te=(t(4188),t(2721)),oe=t(7477),re=t(8950),se=t(2353),ae=t(4996);re.Yv.add(se.Ubc,se.Uj9,se.QLR,se.h8M,se.LBj,se.Int,se.sjs,se.fny,se.a$,se.ao0,se.$Fj,se.qFF,se.Yj9,se.LqK,se.tdl,se.GF6,se.oZK,se.gr3,se.skf,se.DOu,se.v02,se._qq,se.iW_,se.Wzs,se.XkK,se.pS3,se.ijD,se.APi,se.Vpu,se.MjD,se.cbP,se.yLS,se.jTw,se.y_8,se.Bwz,ae.Vz1,ae.VGT,se.wXH,se.br3);const le=(0,o.Ef)(E);le.component("font-awesome-icon",y.gc);for(const[ce,ie]of Object.entries(oe))le.component(ce,ie);ee(),le.use(q).use(D).use(ne.A,{locale:te.A,size:"default"}).mount("#app"),le.config.errorHandler=(e,n,t)=>{console.error("Vue 全局错误:",e);const o={message:e instanceof Error?e.message:String(e),stack:e instanceof Error?e.stack:"无堆栈信息",vueHookInfo:t,url:window.location.href};K.GQ.logError(o).catch(e=>{console.error("发送错误到服务器失败:",e)})},window.addEventListener("unhandledrejection",e=>{const n={message:e.reason instanceof Error?e.reason.message:"未处理的Promise异常",stack:e.reason instanceof Error?e.reason.stack:"无堆栈信息",url:window.location.href,type:"unhandledrejection"};K.GQ.logError(n).catch(e=>{console.error("发送Promise错误到服务器失败:",e)})}),window.addEventListener("error",e=>{if(e.message){const n={message:e.message,codeInfo:`${e.filename}:${e.lineno}:${e.colno}`,url:window.location.href,type:"global-error"};K.GQ.logError(n).catch(e=>{console.error("发送全局错误到服务器失败:",e)})}})}},n={};function t(o){var r=n[o];if(void 0!==r)return r.exports;var s=n[o]={exports:{}};return e[o].call(s.exports,s,s.exports,t),s.exports}t.m=e,function(){var e=[];t.O=function(n,o,r,s){if(!o){var a=1/0;for(u=0;u<e.length;u++){o=e[u][0],r=e[u][1],s=e[u][2];for(var l=!0,c=0;c<o.length;c++)(!1&s||a>=s)&&Object.keys(t.O).every(function(e){return t.O[e](o[c])})?o.splice(c--,1):(l=!1,s<a&&(a=s));if(l){e.splice(u--,1);var i=r();void 0!==i&&(n=i)}}return n}s=s||0;for(var u=e.length;u>0&&e[u-1][2]>s;u--)e[u]=e[u-1];e[u]=[o,r,s]}}(),function(){t.n=function(e){var n=e&&e.__esModule?function(){return e["default"]}:function(){return e};return t.d(n,{a:n}),n}}(),function(){t.d=function(e,n){for(var o in n)t.o(n,o)&&!t.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:n[o]})}}(),function(){t.f={},t.e=function(e){return Promise.all(Object.keys(t.f).reduce(function(n,o){return t.f[o](e,n),n},[]))}}(),function(){t.u=function(e){return"js/"+{488:"log-converter",594:"about",771:"parameter-tool"}[e]+"."+{488:"52d7489c",594:"11da553c",771:"7b92856f"}[e]+".js"}}(),function(){t.miniCssF=function(e){return"css/"+{488:"log-converter",594:"about",771:"parameter-tool"}[e]+"."+{488:"17cf53fa",594:"1c52d824",771:"1cf8e03c"}[e]+".css"}}(),function(){t.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}()}(),function(){t.o=function(e,n){return Object.prototype.hasOwnProperty.call(e,n)}}(),function(){var e={},n="tab-kit-web:";t.l=function(o,r,s,a){if(e[o])e[o].push(r);else{var l,c;if(void 0!==s)for(var i=document.getElementsByTagName("script"),u=0;u<i.length;u++){var d=i[u];if(d.getAttribute("src")==o||d.getAttribute("data-webpack")==n+s){l=d;break}}l||(c=!0,l=document.createElement("script"),l.charset="utf-8",l.timeout=120,t.nc&&l.setAttribute("nonce",t.nc),l.setAttribute("data-webpack",n+s),l.src=o),e[o]=[r];var p=function(n,t){l.onerror=l.onload=null,clearTimeout(f);var r=e[o];if(delete e[o],l.parentNode&&l.parentNode.removeChild(l),r&&r.forEach(function(e){return e(t)}),n)return n(t)},f=setTimeout(p.bind(null,void 0,{type:"timeout",target:l}),12e4);l.onerror=p.bind(null,l.onerror),l.onload=p.bind(null,l.onload),c&&document.head.appendChild(l)}}}(),function(){t.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}}(),function(){t.p="/"}(),function(){if("undefined"!==typeof document){var e=function(e,n,o,r,s){var a=document.createElement("link");a.rel="stylesheet",a.type="text/css",t.nc&&(a.nonce=t.nc);var l=function(t){if(a.onerror=a.onload=null,"load"===t.type)r();else{var o=t&&t.type,l=t&&t.target&&t.target.href||n,c=new Error("Loading CSS chunk "+e+" failed.\n("+o+": "+l+")");c.name="ChunkLoadError",c.code="CSS_CHUNK_LOAD_FAILED",c.type=o,c.request=l,a.parentNode&&a.parentNode.removeChild(a),s(c)}};return a.onerror=a.onload=l,a.href=n,o?o.parentNode.insertBefore(a,o.nextSibling):document.head.appendChild(a),a},n=function(e,n){for(var t=document.getElementsByTagName("link"),o=0;o<t.length;o++){var r=t[o],s=r.getAttribute("data-href")||r.getAttribute("href");if("stylesheet"===r.rel&&(s===e||s===n))return r}var a=document.getElementsByTagName("style");for(o=0;o<a.length;o++){r=a[o],s=r.getAttribute("data-href");if(s===e||s===n)return r}},o=function(o){return new Promise(function(r,s){var a=t.miniCssF(o),l=t.p+a;if(n(a,l))return r();e(o,l,null,r,s)})},r={524:0};t.f.miniCss=function(e,n){var t={488:1,594:1,771:1};r[e]?n.push(r[e]):0!==r[e]&&t[e]&&n.push(r[e]=o(e).then(function(){r[e]=0},function(n){throw delete r[e],n}))}}}(),function(){var e={524:0};t.f.j=function(n,o){var r=t.o(e,n)?e[n]:void 0;if(0!==r)if(r)o.push(r[2]);else{var s=new Promise(function(t,o){r=e[n]=[t,o]});o.push(r[2]=s);var a=t.p+t.u(n),l=new Error,c=function(o){if(t.o(e,n)&&(r=e[n],0!==r&&(e[n]=void 0),r)){var s=o&&("load"===o.type?"missing":o.type),a=o&&o.target&&o.target.src;l.message="Loading chunk "+n+" failed.\n("+s+": "+a+")",l.name="ChunkLoadError",l.type=s,l.request=a,r[1](l)}};t.l(a,c,"chunk-"+n,n)}},t.O.j=function(n){return 0===e[n]};var n=function(n,o){var r,s,a=o[0],l=o[1],c=o[2],i=0;if(a.some(function(n){return 0!==e[n]})){for(r in l)t.o(l,r)&&(t.m[r]=l[r]);if(c)var u=c(t)}for(n&&n(o);i<a.length;i++)s=a[i],t.o(e,s)&&e[s]&&e[s][0](),e[s]=0;return t.O(u)},o=self["webpackChunktab_kit_web"]=self["webpackChunktab_kit_web"]||[];o.forEach(n.bind(null,0)),o.push=n.bind(null,o.push.bind(o))}();var o=t.O(void 0,[504],function(){return t(8347)});o=t.O(o)})();
//# sourceMappingURL=app.3c79a96f.js.map