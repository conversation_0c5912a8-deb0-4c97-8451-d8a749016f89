using Alsi.App;
using Alsi.Common.Utils;
using Alsi.Tab.Kit.Core.Models;
using System;
using System.Diagnostics;
using System.IO;
using System.Linq;

namespace Alsi.Tab.Kit.Core.Services
{
    public class ExternalAppsService
    {
        private const string EXTERNAL_APPS_FOLDER = "external_apps";
        private const string INDEX_FILE_NAME = "external_apps.json";

        private static ExternalApp[] cache = null;
        private static object cacheLock = new object();
        private static DateTime? cacheTime = null;

        public ExternalApp[] GetExternalApps()
        {
            lock (cacheLock)
            {
                var modificationTime = GetModificationTime();
                if (cache == null || cacheTime == null || cacheTime < modificationTime)
                {
                    cache = LoadExternalApps();
                    cacheTime = modificationTime;
                }

                return cache;
            }
        }

        private DateTime? GetModificationTime()
        {
            var indexFilePath = GetIndexFilePath();
            if (!File.Exists(indexFilePath))
            {
                return null;
            }
            return new FileInfo(indexFilePath).LastWriteTime;
        }

        private ExternalApp[] LoadExternalApps()
        {
            var indexFilePath = GetIndexFilePath();
            if (!File.Exists(indexFilePath))
            {
                return Array.Empty<ExternalApp>();
            }

            var jsonContent = File.ReadAllText(indexFilePath);
            var appsIndex = JsonUtils.Deserialize<ExternalAppsIndex>(jsonContent);

            if (appsIndex?.Apps == null)
            {
                return Array.Empty<ExternalApp>();
            }

            var externalAppsFolder = GetExternalAppsFolder();

            foreach (var app in appsIndex.Apps)
            {
                app.Id = Guid.NewGuid();
                ProcessAppPaths(app, externalAppsFolder);
            }

            return appsIndex.Apps;
        }

        public void LaunchExternalApp(Guid appId)
        {
            var apps = GetExternalApps();
            var app = apps.FirstOrDefault(x => x.Id == appId);

            if (app == null)
            {
                throw new AppException($"找不到 ID 为 '{appId}' 的外部应用程序");
            }

            if (!app.ExeExists)
            {
                throw new AppException($"应用程序 '{app.Name ?? app.Id.ToString()}' 的可执行文件不存在: {app.FullExePath}");
            }

            var startInfo = new ProcessStartInfo
            {
                FileName = app.FullExePath,
                WorkingDirectory = app.WorkingDirectory,
                UseShellExecute = true
            };

            AppEnv.Logger.Info($"Start external APP: Name={app.Name}; ID={app.Id}; FullExePath={app.FullExePath}; WorkingDirectory={app.WorkingDirectory}");
            Process.Start(startInfo);
        }

        private void ProcessAppPaths(ExternalApp app, string externalAppsFolder)
        {
            if (!string.IsNullOrWhiteSpace(app.ExePath))
            {
                app.FullExePath = Path.Combine(externalAppsFolder, app.ExePath);
                app.ExeExists = File.Exists(app.FullExePath);

                if (app.ExeExists)
                {
                    app.WorkingDirectory = Path.GetDirectoryName(app.FullExePath);
                }
            }
            else
            {
                app.ExeExists = false;
            }

            if (!string.IsNullOrWhiteSpace(app.Icon))
            {
                app.FullIconPath = Path.Combine(externalAppsFolder, app.Icon);
                app.IconExists = File.Exists(app.FullIconPath);
            }
            else
            {
                app.IconExists = false;
            }

            if (string.IsNullOrWhiteSpace(app.Name) && !string.IsNullOrWhiteSpace(app.ExePath))
            {
                app.Name = Path.GetFileNameWithoutExtension(app.ExePath);
            }

            if (app.Tags == null)
            {
                app.Tags = Array.Empty<string>();
            }

            var commonTagName = "独立应用";
            if (!app.Tags.Contains(commonTagName))
            {
                var tagList = app.Tags.ToList();
                tagList.Insert(0, commonTagName);
                app.Tags = tagList.ToArray();
            }
        }

        private string GetExternalAppsFolder()
        {
            return Path.Combine(Directory.GetParent(GetType().Assembly.Location).FullName, EXTERNAL_APPS_FOLDER);
        }

        private string GetIndexFilePath()
        {
            return Path.Combine(GetExternalAppsFolder(), INDEX_FILE_NAME);
        }
    }
}
