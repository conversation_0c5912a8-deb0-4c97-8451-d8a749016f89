﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;

namespace Alsi.Tab.Kit.Core.Services.Capl
{
    /// <summary>
    /// CAPL内容类型枚举
    /// </summary>
    public enum CaplContentType
    {
        Comment,        // 注释
        Include,        // 包含语句
        Variables,      // 变量定义块
        VariableDeclaration, // 变量声明
        Function,       // 函数定义
        Other          // 其他内容
    }

    /// <summary>
    /// CAPL变量信息
    /// </summary>
    public class CaplVariable
    {
        public string Name { get; set; }        // 变量名
        public string Type { get; set; }        // 变量类型（如 char, int, dword, struct等）
        public bool IsConst { get; set; }       // 是否为常量
        public bool IsArray { get; set; }       // 是否为数组
        public string ArraySize { get; set; }   // 数组大小（如果是数组）
        public string Value { get; set; }       // 变量值
        public string OriginalDeclaration { get; set; } // 原始声明
        public int LineNumber { get; set; }     // 在原文件中的行号

        public override string ToString()
        {
            return $"{nameof(Name)}={Name} {nameof(Type)}={Type} {nameof(IsConst)}={IsConst}";
        }
    }

    /// <summary>
    /// CAPL代码块结构
    /// </summary>
    public class CaplCodeBlock
    {
        public int StartLine { get; set; }      // 起始行号（1-based）
        public int EndLine { get; set; }        // 终止行号（1-based）
        public string Content { get; set; }     // 内容
        public CaplContentType ContentType { get; set; } // 内容类型
        public List<string> Variables { get; set; } // 如果是Variables块，包含的变量定义列表（原始字符串）
        public List<CaplVariable> ParsedVariables { get; set; } // 解析后的变量信息

        public CaplCodeBlock()
        {
            Variables = new List<string>();
            ParsedVariables = new List<CaplVariable>();
        }
    }

    /// <summary>
    /// CAPL解析结果
    /// </summary>
    public class CaplParseResult
    {
        public bool Success { get; set; }
        public string ErrorMessage { get; set; }
        public List<CaplCodeBlock> CodeBlocks { get; set; }
        public string ProcessedContent { get; set; } // 去除注释后的内容

        public CaplParseResult()
        {
            CodeBlocks = new List<CaplCodeBlock>();
        }
    }

    public static class CaplUtils
    {
        public static void Initialize()
        {
            Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
        }

        public static CaplParseResult ParseFile(string path)
        {
            if (!File.Exists(path))
            {
                throw new Exception($"Can't find file: {path}");
            }

            var lines = File.ReadAllLines(path, Encoding.GetEncoding("GBK"));
            return InternalParse(lines);
        }

        public static CaplParseResult ParseContent(string content)
        {
            var lines = content.Replace("\r\n", "\n").Split('\n');
            return InternalParse(lines);
        }

        private static CaplParseResult InternalParse(string[] lines)
        {
            var result = new CaplParseResult();

            try
            {
                // 1. 以GB2312编码读取文件内容

                // 2. 移除注释但保持行号结构
                var processedLines = RemoveComments(lines);
                result.ProcessedContent = string.Join("\n", processedLines);

                // 3. 识别代码块
                result.CodeBlocks = IdentifyCodeBlocks(processedLines);

                // 4. 特殊处理variables{}代码块
                ProcessVariablesBlocks(result.CodeBlocks, processedLines);

                result.Success = true;
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"解析失败: {ex.Message}";
            }

            return result;
        }

        /// <summary>
        /// 移除注释但保持行号结构
        /// </summary>
        /// <param name="lines">原始行</param>
        /// <returns>处理后的行</returns>
        private static string[] RemoveComments(string[] lines)
        {
            var processedLines = new string[lines.Length];
            bool inBlockComment = false;

            for (int i = 0; i < lines.Length; i++)
            {
                string line = lines[i];
                string processedLine = "";

                for (int j = 0; j < line.Length; j++)
                {
                    if (!inBlockComment)
                    {
                        // 检查行注释 //
                        if (j < line.Length - 1 && line[j] == '/' && line[j + 1] == '/')
                        {
                            // 剩余部分用空格填充以保持列位置
                            processedLine += new string(' ', line.Length - j);
                            break;
                        }
                        // 检查块注释开始 /*
                        else if (j < line.Length - 1 && line[j] == '/' && line[j + 1] == '*')
                        {
                            inBlockComment = true;
                            processedLine += "  "; // 用空格替换
                            j++; // 跳过下一个字符
                        }
                        else
                        {
                            processedLine += line[j];
                        }
                    }
                    else
                    {
                        // 在块注释中，检查结束 */
                        if (j < line.Length - 1 && line[j] == '*' && line[j + 1] == '/')
                        {
                            inBlockComment = false;
                            processedLine += "  "; // 用空格替换
                            j++; // 跳过下一个字符
                        }
                        else
                        {
                            processedLine += ' '; // 用空格替换注释内容
                        }
                    }
                }

                processedLines[i] = processedLine;
            }

            return processedLines;
        }

        /// <summary>
        /// 识别代码块
        /// </summary>
        /// <param name="lines">处理后的行</param>
        /// <returns>代码块列表</returns>
        private static List<CaplCodeBlock> IdentifyCodeBlocks(string[] lines)
        {
            var blocks = new List<CaplCodeBlock>();

            for (int i = 0; i < lines.Length; i++)
            {
                string trimmedLine = lines[i].Trim();

                if (string.IsNullOrWhiteSpace(trimmedLine))
                    continue;

                // 识别includes块
                if (trimmedLine.StartsWith("includes"))
                {
                    var block = ParseBlock(lines, i, "includes");
                    if (block != null)
                    {
                        block.ContentType = CaplContentType.Include;
                        blocks.Add(block);
                        i = block.EndLine - 1; // 跳过已处理的行
                    }
                }
                // 识别variables块
                else if (trimmedLine.StartsWith("variables"))
                {
                    var block = ParseBlock(lines, i, "variables");
                    if (block != null)
                    {
                        block.ContentType = CaplContentType.Variables;
                        blocks.Add(block);
                        i = block.EndLine - 1; // 跳过已处理的行
                    }
                }
                // 识别其他可能的代码块或单行语句
                else
                {
                    var block = new CaplCodeBlock
                    {
                        StartLine = i + 1,
                        EndLine = i + 1,
                        Content = lines[i],
                        ContentType = CaplContentType.Other
                    };
                    blocks.Add(block);
                }
            }

            return blocks;
        }

        /// <summary>
        /// 解析代码块（如includes{}, variables{}）
        /// </summary>
        /// <param name="lines">文件行</param>
        /// <param name="startIndex">开始行索引</param>
        /// <param name="blockName">块名称</param>
        /// <returns>代码块</returns>
        private static CaplCodeBlock ParseBlock(string[] lines, int startIndex, string blockName)
        {
            int braceCount = 0;
            int startLine = startIndex + 1; // 1-based行号
            var content = new StringBuilder();

            for (int i = startIndex; i < lines.Length; i++)
            {
                string line = lines[i];
                content.AppendLine(line);

                // 计算大括号
                for (int j = 0; j < line.Length; j++)
                {
                    if (line[j] == '{')
                        braceCount++;
                    else if (line[j] == '}')
                        braceCount--;
                }

                // 如果大括号平衡，说明块结束
                if (braceCount == 0 && line.Contains('}'))
                {
                    return new CaplCodeBlock
                    {
                        StartLine = startLine,
                        EndLine = i + 1, // 1-based行号
                        Content = content.ToString().Trim(),
                        ContentType = CaplContentType.Other // 临时设置，后续会更新
                    };
                }
            }

            return null; // 没有找到匹配的结束大括号
        }

        /// <summary>
        /// 特殊处理variables{}代码块，提取变量定义
        /// </summary>
        /// <param name="blocks">代码块列表</param>
        /// <param name="lines">处理后的行</param>
        private static void ProcessVariablesBlocks(List<CaplCodeBlock> blocks, string[] lines)
        {
            foreach (var block in blocks.Where(b => b.ContentType == CaplContentType.Variables))
            {
                // 提取大括号内的内容
                var match = Regex.Match(block.Content, @"variables\s*\{(.*)\}", RegexOptions.Singleline | RegexOptions.IgnoreCase);
                if (match.Success)
                {
                    string variablesContent = match.Groups[1].Value;

                    // 按分号分割变量定义
                    var variableDeclarations = SplitBysemicolon(variablesContent);

                    foreach (var declaration in variableDeclarations)
                    {
                        var trimmed = declaration.Trim();
                        var trimmedOneLine = trimmed.Replace('\r', ' ').Replace('\n', ' ');
                        if (Regex.Match(trimmedOneLine, @"\s*struct\s+([\w|_]+)\s*\{.*").Success)
                        {
                            // 是结构体类型定义
                            continue;
                        }

                        if (Regex.Match(trimmedOneLine, @"\s*enum\s+([\w|_]+)\s*\{.*").Success)
                        {
                            // 是枚举类型定义
                            continue;
                        }

                        if (!string.IsNullOrWhiteSpace(trimmed))
                        {
                            block.Variables.Add(trimmed);

                            // 检查是否为多变量声明（一行声明多个变量）
                            var expandedDeclarations = ExpandMultiVariableDeclaration(trimmed);

                            foreach (var expandedDeclaration in expandedDeclarations)
                            {
                                // 解析变量详细信息
                                var parsedVar = ParseVariableDeclaration(expandedDeclaration);
                                if (parsedVar != null)
                                {
                                    block.ParsedVariables.Add(parsedVar);
                                }
                            }
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 展开多变量声明为单个变量声明列表
        /// 例如: "byte ubFlag,carFlag,useFlag,eleFlag" -> ["byte ubFlag", "byte carFlag", "byte useFlag", "byte eleFlag"]
        /// 例如: "byte gUseMode=0,gCarMode=0,gElPowerLevel=0" -> ["byte gUseMode=0", "byte gCarMode=0", "byte gElPowerLevel=0"]
        /// </summary>
        /// <param name="declaration">原始变量声明</param>
        /// <returns>展开后的单个变量声明列表</returns>
        private static List<string> ExpandMultiVariableDeclaration(string declaration)
        {
            var result = new List<string>();

            if (string.IsNullOrWhiteSpace(declaration))
            {
                return result;
            }

            var trimmed = declaration.Trim();
            var trimmedOneLine = trimmed.Replace('\r', ' ').Replace('\n', ' ');

            // 检查是否包含逗号（可能是多变量声明）
            if (!trimmedOneLine.Contains(','))
            {
                // 单变量声明，直接返回
                result.Add(trimmed);
                return result;
            }

            // 检查是否为const声明
            bool isConst = trimmedOneLine.StartsWith("const ");
            string workingDeclaration = isConst ? trimmedOneLine.Substring(6).Trim() : trimmedOneLine;

            // 使用正则表达式匹配多变量声明模式
            // 模式1: type var1,var2,var3,var4 (无初始值)
            var noValuePattern = @"^(\w+(?:\s+\w+)*)\s+(\w+(?:\s*,\s*\w+)+)\s*$";
            var noValueMatch = Regex.Match(workingDeclaration, noValuePattern);

            if (noValueMatch.Success)
            {
                string type = noValueMatch.Groups[1].Value.Trim();
                string variableList = noValueMatch.Groups[2].Value;

                var variableNames = variableList.Split(',').Select(v => v.Trim()).Where(v => !string.IsNullOrEmpty(v));

                foreach (var varName in variableNames)
                {
                    string fullDeclaration = isConst ? $"const {type} {varName}" : $"{type} {varName}";
                    result.Add(fullDeclaration);
                }

                return result;
            }

            // 模式2: type var1=value1,var2=value2,var3=value3 (有初始值)
            var withValuePattern = @"^(\w+(?:\s+\w+)*)\s+(.+)$";
            var withValueMatch = Regex.Match(workingDeclaration, withValuePattern);

            if (withValueMatch.Success)
            {
                string type = withValueMatch.Groups[1].Value.Trim();
                string variableAssignments = withValueMatch.Groups[2].Value;

                // 解析变量赋值列表，需要考虑值中可能包含逗号的情况
                var assignments = ParseVariableAssignments(variableAssignments);

                foreach (var assignment in assignments)
                {
                    string fullDeclaration = isConst ? $"const {type} {assignment}" : $"{type} {assignment}";
                    result.Add(fullDeclaration);
                }

                return result;
            }

            // 如果无法解析为多变量声明，则作为单变量处理
            result.Add(trimmed);
            return result;
        }

        /// <summary>
        /// 解析变量赋值列表，处理值中可能包含逗号的情况
        /// 例如: "var1=0,var2=0,var3=0" -> ["var1=0", "var2=0", "var3=0"]
        /// </summary>
        /// <param name="assignmentList">赋值列表字符串</param>
        /// <returns>单个赋值字符串列表</returns>
        private static List<string> ParseVariableAssignments(string assignmentList)
        {
            var result = new List<string>();
            var current = new StringBuilder();
            bool inString = false;
            bool inArray = false;
            int braceLevel = 0;
            int parenLevel = 0;

            for (int i = 0; i < assignmentList.Length; i++)
            {
                char c = assignmentList[i];

                switch (c)
                {
                    case '"':
                        inString = !inString;
                        current.Append(c);
                        break;
                    case '{':
                        if (!inString)
                        {
                            braceLevel++;
                            inArray = true;
                        }
                        current.Append(c);
                        break;
                    case '}':
                        if (!inString)
                        {
                            braceLevel--;
                            if (braceLevel == 0)
                                inArray = false;
                        }
                        current.Append(c);
                        break;
                    case '(':
                        if (!inString)
                            parenLevel++;
                        current.Append(c);
                        break;
                    case ')':
                        if (!inString)
                            parenLevel--;
                        current.Append(c);
                        break;
                    case ',':
                        if (!inString && !inArray && parenLevel == 0)
                        {
                            // 这是一个真正的变量分隔符
                            var assignment = current.ToString().Trim();
                            if (!string.IsNullOrEmpty(assignment))
                            {
                                result.Add(assignment);
                            }
                            current.Clear();
                        }
                        else
                        {
                            current.Append(c);
                        }
                        break;
                    default:
                        current.Append(c);
                        break;
                }
            }

            // 添加最后一部分
            if (current.Length > 0)
            {
                var assignment = current.ToString().Trim();
                if (!string.IsNullOrEmpty(assignment))
                {
                    result.Add(assignment);
                }
            }

            return result;
        }

        /// <summary>
        /// 解析单个变量声明
        /// </summary>
        /// <param name="declaration">变量声明字符串</param>
        /// <returns>解析后的变量信息</returns>
        private static CaplVariable ParseVariableDeclaration(string declaration)
        {
            if (string.IsNullOrWhiteSpace(declaration))
            {
                return null;
            }

            var variable = new CaplVariable
            {
                OriginalDeclaration = declaration.Trim()
            };

            // 移除前后空白并处理多行情况，但保留结构体数组的换行和缩进
            var cleanDeclaration = declaration.Trim();

            // 如果是多行声明（比如结构体数组），需要特殊处理
            if (cleanDeclaration.Contains('\n') || cleanDeclaration.Contains('\r'))
            {
                // 保留原始格式，只是清理多余的空白
                cleanDeclaration = Regex.Replace(cleanDeclaration, @"[ \t]+", " ");
                cleanDeclaration = Regex.Replace(cleanDeclaration, @"\r?\n\s*", " ");
            }
            else
            {
                cleanDeclaration = Regex.Replace(cleanDeclaration, @"\s+", " ").Trim();
            }

            // 检查是否为const
            variable.IsConst = cleanDeclaration.StartsWith("const ");
            if (variable.IsConst)
            {
                cleanDeclaration = cleanDeclaration.Substring(6).Trim(); // 移除"const "
            }

            // 正则表达式匹配不同类型的变量声明
            // 1. 结构体数组: struct STRUCT_NAME varName[size] = {...}
            var structArrayMatch = Regex.Match(cleanDeclaration,
                @"struct\s+(\w+)\s+(\w+)\s*\[([^\]]+)\]\s*=\s*(.+)",
                RegexOptions.IgnoreCase | RegexOptions.Singleline);

            if (structArrayMatch.Success)
            {
                variable.Type = $"struct {structArrayMatch.Groups[1].Value}";
                variable.Name = structArrayMatch.Groups[2].Value;
                variable.IsArray = true;
                variable.ArraySize = structArrayMatch.Groups[3].Value.Trim();
                variable.Value = ExtractComplexValue(structArrayMatch.Groups[4].Value);
                return variable;
            }

            // 2. 普通数组: type varName[size] = {...}
            var arrayMatch = Regex.Match(cleanDeclaration,
                @"(\w+)\s+(\w+)\s*\[([^\]]+)\]\s*=\s*(.+)",
                RegexOptions.IgnoreCase | RegexOptions.Singleline);

            if (arrayMatch.Success)
            {
                variable.Type = arrayMatch.Groups[1].Value;
                variable.Name = arrayMatch.Groups[2].Value;
                variable.IsArray = true;
                variable.ArraySize = arrayMatch.Groups[3].Value.Trim();
                variable.Value = ExtractComplexValue(arrayMatch.Groups[4].Value);
                return variable;
            }

            // 3. 简单变量: type varName = value
            var simpleMatch = Regex.Match(cleanDeclaration,
                @"(\w+)\s+(\w+)\s*=\s*(.+)",
                RegexOptions.IgnoreCase);

            if (simpleMatch.Success)
            {
                variable.Type = simpleMatch.Groups[1].Value;
                variable.Name = simpleMatch.Groups[2].Value;
                variable.IsArray = false;
                variable.Value = simpleMatch.Groups[3].Value.Trim();
                return variable;
            }

            // 4. 无初始值的声明: type varName 或 type varName[size]
            var noValueMatch = Regex.Match(cleanDeclaration,
                @"(\w+)\s+(\w+)(?:\s*\[([^\]]+)\])?\s*",
                RegexOptions.IgnoreCase);

            if (noValueMatch.Success)
            {
                variable.Type = noValueMatch.Groups[1].Value;
                variable.Name = noValueMatch.Groups[2].Value;
                variable.IsArray = !string.IsNullOrEmpty(noValueMatch.Groups[3].Value);
                if (variable.IsArray)
                    variable.ArraySize = noValueMatch.Groups[3].Value.Trim();
                variable.Value = "";
                return variable;
            }

            return null; // 无法解析
        }

        /// <summary>
        /// 提取复杂值（如数组值、结构体值）
        /// </summary>
        private static string ExtractComplexValue(string rawValue)
        {
            if (string.IsNullOrWhiteSpace(rawValue))
                return "";

            // 如果值包含大括号，需要提取完整的块
            rawValue = rawValue.Trim();

            if (rawValue.StartsWith("{"))
            {
                // 找到匹配的结束大括号
                int braceCount = 0;
                int endIndex = -1;

                for (int i = 0; i < rawValue.Length; i++)
                {
                    if (rawValue[i] == '{')
                        braceCount++;
                    else if (rawValue[i] == '}')
                    {
                        braceCount--;
                        if (braceCount == 0)
                        {
                            endIndex = i;
                            break;
                        }
                    }
                }

                if (endIndex >= 0)
                {
                    return rawValue.Substring(0, endIndex + 1);
                }
            }

            return rawValue;
        }

        public static string ReplaceVariableValue(string caplContent, string variableName, string newValue)
        {
            if (string.IsNullOrWhiteSpace(caplContent))
            {
                return string.Empty;
            }

            // 解析文件以获取变量信息
            var parseResult = ParseContent(caplContent);
            if (!parseResult.Success)
                return null;

            // 找到目标变量
            CaplVariable targetVariable = null;
            CaplCodeBlock variablesBlock = null;

            foreach (var block in parseResult.CodeBlocks.Where(b => b.ContentType == CaplContentType.Variables))
            {
                targetVariable = block.ParsedVariables.FirstOrDefault(v => v.Name == variableName);
                if (targetVariable != null)
                {
                    variablesBlock = block;
                    break;
                }
            }

            if (targetVariable == null)
            {
                return caplContent;
            }

            // 执行替换
            return ReplaceVariableValueInContent(caplContent, targetVariable, newValue);
        }

        /// <summary>
        /// 在内容中替换变量值
        /// </summary>
        private static string ReplaceVariableValueInContent(string content, CaplVariable variable, string newValue)
        {
            // 构建变量声明的正则表达式模式
            string pattern = BuildVariablePattern(variable);

            if (string.IsNullOrEmpty(pattern))
                return null;

            // 构建替换字符串
            string replacement = BuildReplacementString(variable, newValue);

            // 执行替换
            var result = Regex.Replace(content, pattern, replacement, RegexOptions.Singleline | RegexOptions.IgnoreCase);
            return result;
        }

        /// <summary>
        /// 构建变量匹配模式
        /// </summary>
        private static string BuildVariablePattern(CaplVariable variable)
        {
            string constPrefix = variable.IsConst ? @"const\s+" : "";
            string namePattern = Regex.Escape(variable.Name);

            if (variable.IsArray)
            {
                if (variable.Type.StartsWith("struct"))
                {
                    // struct TypeName varName[size] = { ... };
                    string structType = variable.Type.Substring(7); // 移除"struct "
                    return $@"({constPrefix}struct\s+{Regex.Escape(structType)}\s+{namePattern}\s*\[[^\]]+\]\s*=\s*)([^;]+)(;)";
                }
                else
                {
                    // type varName[size] = { ... };
                    return $@"({constPrefix}{Regex.Escape(variable.Type)}\s+{namePattern}\s*\[[^\]]+\]\s*=\s*)([^;]+)(;)";
                }
            }
            else
            {
                // type varName = value;
                return $@"({constPrefix}{Regex.Escape(variable.Type)}\s+{namePattern}\s*=\s*)([^;]+)(;)";
            }
        }

        /// <summary>
        /// 构建替换字符串
        /// </summary>
        private static string BuildReplacementString(CaplVariable variable, string newValue)
        {
            // $1 是声明部分，$3 是分号，我们只替换中间的值部分
            return "${1}" + newValue + "${3}";
        }

        /// <summary>
        /// 按分号分割，但要考虑字符串和数组中的分号
        /// </summary>
        /// <param name="content">内容</param>
        /// <returns>分割后的部分</returns>
        private static List<string> SplitBysemicolon(string content)
        {
            var result = new List<string>();
            var current = new StringBuilder();
            bool inString = false;
            bool inArray = false;
            int braceLevel = 0;

            for (int i = 0; i < content.Length; i++)
            {
                char c = content[i];

                switch (c)
                {
                    case '"':
                        inString = !inString;
                        current.Append(c);
                        break;
                    case '{':
                        if (!inString)
                        {
                            braceLevel++;
                            inArray = true;
                        }
                        current.Append(c);
                        break;
                    case '}':
                        if (!inString)
                        {
                            braceLevel--;
                            if (braceLevel == 0)
                                inArray = false;
                        }
                        current.Append(c);
                        break;
                    case ';':
                        if (!inString && !inArray)
                        {
                            // 这是一个真正的语句结束符
                            result.Add(current.ToString().Trim());
                            current.Clear();
                        }
                        else
                        {
                            current.Append(c);
                        }
                        break;
                    default:
                        current.Append(c);
                        break;
                }
            }

            // 添加最后一部分（如果有的话）
            if (current.Length > 0)
            {
                result.Add(current.ToString().Trim());
            }

            return result;
        }
    }
}
