using Alsi.Tab.Kit.Core.Services.Capl.Cin;
using static Alsi.Tab.Kit.Core.Services.Capl.Cin.CinTypes;
using static NPOI.HSSF.Util.HSSFColor;
using STRU_DID = Alsi.Tab.Kit.Core.Services.Capl.Cin.CinTypes.STRU_DID;

namespace Alsi.Tab.Kit.UnitTests;

public class CinTypeUtilsTests
{
    #region 简单类型测试

    [Fact]
    public void ParseSimpleTypes_ShouldWork()
    {
        // 测试整数类型
        CinTypeUtils.ParseCinValue<int>("123").ShouldBe(123);
        CinTypeUtils.ParseCinValue<int>("0xFF").ShouldBe(255);
        CinTypeUtils.ParseCinValue<byte>("255").ShouldBe((byte)255);
        CinTypeUtils.ParseCinValue<long>("0x1000").ShouldBe(4096L);

        // 测试浮点类型
        CinTypeUtils.ParseCinValue<float>("3.14").ShouldBe(3.14f);
        CinTypeUtils.ParseCinValue<double>("2.718").ShouldBe(2.718);

        // 测试字符串类型
        CinTypeUtils.ParseCinValue<string>("hello").ShouldBe("hello");
        CinTypeUtils.ParseCinValue<string>("\"quoted\"").ShouldBe("quoted");

        // 测试布尔类型
        CinTypeUtils.ParseCinValue<bool>("true").ShouldBe(true);
        CinTypeUtils.ParseCinValue<bool>("false").ShouldBe(false);
        CinTypeUtils.ParseCinValue<bool>("1").ShouldBe(true);
    }

    [Fact]
    public void FormatSimpleTypes_ShouldWork()
    {
        // 测试整数格式化
        CinTypeUtils.ToCinValue(123).ShouldBe("123");
        CinTypeUtils.ToCinValue(255, isHex: true).ShouldBe("0xFF");

        // 测试浮点格式化
        CinTypeUtils.ToCinValue(3.14f).ShouldBe("3.14");
        CinTypeUtils.ToCinValue(2.718).ShouldBe("2.718");

        // 测试字符串格式化
        CinTypeUtils.ToCinValue("hello").ShouldBe("\"hello\"");

        // 测试布尔格式化
        CinTypeUtils.ToCinValue(true).ShouldBe("true");
        CinTypeUtils.ToCinValue(false).ShouldBe("false");
    }

    #endregion

    #region 数组类型测试

    [Fact]
    public void ParseArrayTypes_ShouldWork()
    {
        // 测试整数数组
        var intArray = CinTypeUtils.ParseCinValue<int[]>("{1, 2, 3, 4}");
        intArray.ShouldBe(new int[] { 1, 2, 3, 4 });

        // 测试十六进制数组
        var hexArray = CinTypeUtils.ParseCinValue<byte[]>("{0x10, 0x20, 0x30}");
        hexArray.ShouldBe(new byte[] { 0x10, 0x20, 0x30 });

        // 测试字符串数组
        var stringArray = CinTypeUtils.ParseCinValue<string[]>("{\"hello\", \"world\"}");
        stringArray.ShouldBe(new string[] { "hello", "world" });
    }

    [Fact]
    public void FormatArrayTypes_ShouldWork()
    {
        // 测试整数数组格式化
        var intArray = new int[] { 1, 2, 3, 4 };
        CinTypeUtils.ToCinValue(intArray).ShouldBe("{1, 2, 3, 4}");

        // 测试字节数组格式化
        var byteArray = new byte[] { 0x10, 0x20, 0x30 };
        CinTypeUtils.ToCinValue(byteArray).ShouldBe("{16, 32, 48}");

        // 测试字符串数组格式化
        var stringArray = new string[] { "hello", "world" };
        CinTypeUtils.ToCinValue(stringArray).ShouldBe("{\"hello\", \"world\"}");
    }

    #endregion

    #region 结构体类型测试

    [Fact]
    public void ParseStructTypes_ShouldWork()
    {
        // 测试STRU_DID结构体解析
        var struDid = CinTypeUtils.ParseCinValue<STRU_DID>("{5, 0x9007, 5}");
        struDid.sessionMask.ShouldBe((byte)5);
        struDid.did.ShouldBe(0x9007);
        struDid.valueLength.ShouldBe(5);
    }

    [Fact]
    public void FormatStructTypes_ShouldWork()
    {
        // 测试STRU_DID结构体格式化
        var struDid = new STRU_DID
        {
            sessionMask = 5,
            did = 0x9007,
            valueLength = 5
        };

        var result = CinTypeUtils.ToCinValue(struDid);
        result.ShouldBe("{5, 0x9007, 5, {}}");
    }

    #endregion

    #region 结构体数组测试

    [Fact]
    public void ParseStructArrayTypes_ShouldWork()
    {
        // 测试结构体数组解析
        var structArray = CinTypeUtils.ParseCinValue<STRU_SNAPSHOT_DID[]>("{{0x1001, 4}, {0x1002, 8}}");
        structArray.Length.ShouldBe(2);
        structArray[0].did.ShouldBe(0x1001);
        structArray[0].didLen.ShouldBe((byte)4);
        structArray[1].did.ShouldBe(0x1002);
        structArray[1].didLen.ShouldBe((byte)8);
    }

    [Fact]
    public void FormatStructArrayTypes_ShouldWork()
    {
        // 测试结构体数组格式化
        var structArray = new STRU_SNAPSHOT_DID[]
        {
            new STRU_SNAPSHOT_DID { did = 0x1001, didLen = 4 },
            new STRU_SNAPSHOT_DID { did = 0x1002, didLen = 8 }
        };

        var result = CinTypeUtils.ToCinValue(structArray);
        result.ShouldBe("{{4097, 4}, {4098, 8}}");
    }

    #endregion

    #region 嵌套结构体测试

    [Fact]
    public void ParseNestedStructTypes_ShouldWork()
    {
        // 测试包含数组的结构体
        var snapshot = CinTypeUtils.ParseCinValue<SNAPSHOT_INFO>("{1, 2, {{0x1001, 4}, {0x1002, 8}}}");
        snapshot.snapshotID.ShouldBe((byte)1);
        snapshot.snapshotNum.ShouldBe((byte)2);
        snapshot.snapshotdidList.Length.ShouldBe(2);
        snapshot.snapshotdidList[0].did.ShouldBe(0x1001);
        snapshot.snapshotdidList[0].didLen.ShouldBe((byte)4);
    }

    [Fact]
    public void FormatNestedStructTypes_ShouldWork()
    {
        // 测试包含数组的结构体格式化
        var snapshot = new SNAPSHOT_INFO
        {
            snapshotID = 1,
            snapshotNum = 2,
            snapshotdidList = new STRU_SNAPSHOT_DID[]
            {
                new STRU_SNAPSHOT_DID { did = 0x1001, didLen = 4 },
                new STRU_SNAPSHOT_DID { did = 0x1002, didLen = 8 }
            }
        };

        var result = CinTypeUtils.ToCinValue(snapshot);
        result.ShouldBe("{1, 2, {{4097, 4}, {4098, 8}}}");
    }

    #endregion

    #region 边界情况测试

    [Fact]
    public void ParseEmptyValues_ShouldWork()
    {
        // 测试空数组
        var emptyArray = CinTypeUtils.ParseCinValue<int[]>("{}");
        emptyArray.Length.ShouldBe(0);

        // 测试空字符串
        var emptyString = CinTypeUtils.ParseCinValue<string>("");
        emptyString.ShouldBe("");
    }

    [Fact]
    public void ParseInvalidValues_ShouldThrow()
    {
        // 测试无效的数字格式
        Should.Throw<Exception>(() => CinTypeUtils.ParseCinValue<int>("invalid"));

        // 测试无效的十六进制格式
        Should.Throw<Exception>(() => CinTypeUtils.ParseCinValue<int>("0xGGG"));

        // 测试无效的浮点格式
        Should.Throw<Exception>(() => CinTypeUtils.ParseCinValue<float>("not_a_float"));
    }

    [Fact]
    public void ParseWithWhitespace_ShouldWork()
    {
        // 测试包含空白字符的解析
        var result = CinTypeUtils.ParseCinValue<int[]>("{ 1 , 2 , 3 }");
        result.ShouldBe(new int[] { 1, 2, 3 });

        var structResult = CinTypeUtils.ParseCinValue<STRU_DID>("{ 5 , 0x9007 , 5 }");
        structResult.sessionMask.ShouldBe((byte)5);
        structResult.did.ShouldBe(0x9007);
        structResult.valueLength.ShouldBe(5);
    }

    #endregion

    #region 兼容性测试

    [Fact]
    public void BackwardCompatibility_ShouldWork()
    {
        // 确保原有的测试仍然通过
        var struDid = CinTypeUtils.ParseCinValue<STRU_DID>("{5,0x9007, 5}");
        ((int)struDid.sessionMask).ShouldBe(5);
        ((int)struDid.did).ShouldBe(0x9007);
        struDid.valueLength.ShouldBe(5);
    }

    [Fact]
    public void RoundTripSerialization_ShouldWork()
    {
        // 测试序列化和反序列化的往返
        var original = new STRU_DID
        {
            sessionMask = 10,
            did = 0x1234,
            valueLength = 20
        };

        var serialized = CinTypeUtils.ToCinValue(original);
        var deserialized = CinTypeUtils.ParseCinValue<STRU_DID>(serialized);

        deserialized.sessionMask.ShouldBe(original.sessionMask);
        deserialized.did.ShouldBe(original.did);
        deserialized.valueLength.ShouldBe(original.valueLength);
    }

    #endregion

    #region 枚举类型测试

    // 测试用枚举类型
    public enum SigType
    {
        CarMode = 1,
        UsageMode = 2,
        ElPowerLevel = 3,
        UB = 4
    }

    // 测试用结构体，包含枚举字段
    public struct E2EFrameElement
    {
        public int offset;
        public int length;
        public SigType sigType;
    }

    public struct FrameFormat
    {
        [CinProp(isHex: true)]
        public int frameID;
        public int cycle;
        public byte dataLen;

        public FrameFormat(int frameID, byte cycle, byte dataLen)
        {
            this.frameID = frameID;
            this.cycle = cycle;
            this.dataLen = dataLen;
        }
    }

    public struct E2EFrame
    {
        public FrameFormat frameFormat;
        public int dlc;
        public E2EFrameElement[] elements;
    }

    [Fact]
    public void ParseEnum_ShouldWork()
    {
        // 测试按名称解析枚举
        CinTypeUtils.ParseCinValue<SigType>("CarMode").ShouldBe(SigType.CarMode);
        CinTypeUtils.ParseCinValue<SigType>("UsageMode").ShouldBe(SigType.UsageMode);
        CinTypeUtils.ParseCinValue<SigType>("ElPowerLevel").ShouldBe(SigType.ElPowerLevel);
        CinTypeUtils.ParseCinValue<SigType>("UB").ShouldBe(SigType.UB);

        // 测试按数值解析枚举
        CinTypeUtils.ParseCinValue<SigType>("1").ShouldBe(SigType.CarMode);
        CinTypeUtils.ParseCinValue<SigType>("2").ShouldBe(SigType.UsageMode);
        CinTypeUtils.ParseCinValue<SigType>("3").ShouldBe(SigType.ElPowerLevel);
        CinTypeUtils.ParseCinValue<SigType>("4").ShouldBe(SigType.UB);

        // 测试大小写不敏感
        CinTypeUtils.ParseCinValue<SigType>("carmode").ShouldBe(SigType.CarMode);
        CinTypeUtils.ParseCinValue<SigType>("USAGEMODE").ShouldBe(SigType.UsageMode);
    }

    [Fact]
    public void FormatEnum_ShouldWork()
    {
        // 测试枚举格式化
        CinTypeUtils.ToCinValue(SigType.CarMode).ShouldBe("CarMode");
        CinTypeUtils.ToCinValue(SigType.UsageMode).ShouldBe("UsageMode");
        CinTypeUtils.ToCinValue(SigType.ElPowerLevel).ShouldBe("ElPowerLevel");
        CinTypeUtils.ToCinValue(SigType.UB).ShouldBe("UB");
    }

    [Fact]
    public void ParseStructWithEnum_ShouldWork()
    {
        // 测试包含枚举的结构体解析
        var cinValue = "{12, 4, UsageMode}";
        var result = CinTypeUtils.ParseCinValue<E2EFrameElement>(cinValue);

        result.offset.ShouldBe(12);
        result.length.ShouldBe(4);
        result.sigType.ShouldBe(SigType.UsageMode);
    }

    [Fact]
    public void FormatStructWithEnum_ShouldWork()
    {
        // 测试包含枚举的结构体格式化
        var element = new E2EFrameElement
        {
            offset = 24,
            length = 4,
            sigType = SigType.UsageMode
        };
        var result = CinTypeUtils.ToCinValue(element);

        result.ShouldBe("{24, 4, UsageMode}");
    }

    [Fact]
    public void ParseComplexStructWithEnumArray_ShouldWork()
    {
        // 测试复杂结构体，包含枚举数组
        var cinValue = "{{0x120, 65}, 116, {{12, 4}, {8, 4}, {20, 4}, {16, 4}, {28, 4}, {24, 4, UsageMode}, {37, 3, CarMode}, {34, 4}, {33, 1}, {0, 8}, {32, 1, UB}}}";
        var result = CinTypeUtils.ParseCinValue<E2EFrame>(cinValue);

        result.frameFormat.frameID.ShouldBe(0x120);
        result.frameFormat.cycle.ShouldBe(65);
        result.dlc.ShouldBe(116);
        result.elements.Length.ShouldBe(11);

        // 验证包含枚举的元素
        result.elements[5].offset.ShouldBe(24);
        result.elements[5].length.ShouldBe(4);
        result.elements[5].sigType.ShouldBe(SigType.UsageMode);

        result.elements[6].offset.ShouldBe(37);
        result.elements[6].length.ShouldBe(3);
        result.elements[6].sigType.ShouldBe(SigType.CarMode);

        result.elements[10].offset.ShouldBe(32);
        result.elements[10].length.ShouldBe(1);
        result.elements[10].sigType.ShouldBe(SigType.UB);
    }

    [Fact]
    public void FormatComplexStructWithEnumArray_ShouldWork()
    {
        // 测试复杂结构体格式化
        var frame = new E2EFrame
        {
            frameFormat = new FrameFormat(0x120, 65, 0),
            dlc = 116,
            elements = new E2EFrameElement[]
            {
                new E2EFrameElement { offset = 12, length = 4, sigType = SigType.CarMode },
                new E2EFrameElement { offset = 8, length = 4, sigType = SigType.CarMode },
                new E2EFrameElement { offset = 20, length = 4, sigType = SigType.CarMode },
                new E2EFrameElement { offset = 16, length = 4, sigType = SigType.CarMode },
                new E2EFrameElement { offset = 28, length = 4, sigType = SigType.CarMode },
                new E2EFrameElement { offset = 24, length = 4, sigType = SigType.UsageMode },
                new E2EFrameElement { offset = 37, length = 3, sigType = SigType.CarMode },
                new E2EFrameElement { offset = 34, length = 4, sigType = SigType.CarMode },
                new E2EFrameElement { offset = 33, length = 1, sigType = SigType.CarMode },
                new E2EFrameElement { offset = 0, length = 8, sigType = SigType.CarMode },
                new E2EFrameElement { offset = 32, length = 1, sigType = SigType.UB }
            }
        };

        var result = CinTypeUtils.ToCinValue(frame);

        // 验证结果包含正确的枚举名称
        result.ShouldContain("UsageMode");
        result.ShouldContain("CarMode");
        result.ShouldContain("UB");

        // 验证整体结构
        result.ShouldStartWith("{{0x120, 65, 0}, 116, {");
        result.ShouldEndWith("}}");
    }

    #endregion
}
