﻿using ArxmlModel = AUTOSAR;

namespace Alsi.Tab.Kit.Core.Services.Capl.Core
{
    internal abstract class ArxmlLoaderBase
    {
        private readonly ArxmlLoaderContext context;
        protected ArxmlModel arxmlModel => context.ArxmlModel;
        protected string source => context.Source;
        protected IParamCollection collection => context.Collection;
        protected int order => context.Order;
        protected string[] arxmlEcuNodes => context.ArxmlEcuNodes;

        public ArxmlLoaderBase(ArxmlLoaderContext context)
        {
            this.context = context;
        }

        public abstract void Load();
    }
}
