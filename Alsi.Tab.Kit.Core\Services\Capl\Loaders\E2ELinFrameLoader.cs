﻿using Alsi.Common.Parsers.Ldf.Model;
using Alsi.Common.Utils;
using System;
using System.Collections.Generic;
using System.Linq;
using ArxmlModel = AUTOSAR;


namespace Alsi.Tab.Kit.Core.Services.Capl.Params
{
    internal class E2ELinFrameLoader
    {
        public void Load(ArxmlModel arxmlModel, LdfModel ldfModel, string source, IParamCollection collection, int order)
        {
            foreach (var node in ldfModel.Nodes)
            {
                var txE2EFrames = new List<E2EFrame>();
                var rxE2EFrames = new List<E2EFrame>();
                foreach (var frame in ldfModel.Frames)
                {
                    var containsCntr = frame.Signals.Any(x => x.Name.IsCntr());
                    if (!containsCntr)
                    {
                        continue;
                    }

                    var containsChks = frame.Signals.Any(x => x.Name.IsChks());
                    if (!containsChks)
                    {
                        continue;
                    }

                    var chkSignal = frame.Signals.FirstOrDefault(x => x.Name.IsChks());
                    if (chkSignal == null
                        || !LoaderUtils.TryGetPrefixOfChksSignal(chkSignal.Name, out var e2eSignalPrefix))
                    {
                        continue;
                    }

                    var isTx = frame.NodeName.Equals(node.Name, StringComparison.OrdinalIgnoreCase);

                    var cycle = ldfModel.ScheduleTables
                        .FirstOrDefault(x => x.ScheduleTableFrames.Any(tableFrame => tableFrame.Id == frame.Id))?
                        .ScheduleTableFrames
                        .Sum(x => x.Delay)
                        ?? frame.Delay;

                    uint? dataId = null;
                    uint? updateIndicationBitPosition = null;

                    // LIN 的字节序，默认是 Intel
                    var e2eByteOrder = E2EByteOrder.Intel;
                    if (arxmlModel != null && chkSignal != null)
                    {
                        // DDM - WinSwtReqChks
                        var signalName = chkSignal.Name;
                        var systemSignalGroup = arxmlModel.SystemSignalGroups
                            .FirstOrDefault(x => x.SYSTEMSIGNALREFS.Any(systemSignalRef => systemSignalRef.Value.EndsWith($"/{signalName}")));
                        if (systemSignalGroup != null)
                        {
                            // DDM - WinSwtReq
                            var systemSignalGroupName = systemSignalGroup.SHORTNAME.Value;

                            // DDM - 1118
                            if (arxmlModel.TryGetDataId(systemSignalGroupName, out var dataIdValue))
                            {
                                dataId = dataIdValue;
                            }

                            // 对于 E2E 的 LIN 参数，不再获取 UpdateBit（LIN 没有 UB） 和 ByteOrder（从 ARXML 获取 ByteOrder 并不合理） 信息
                            //if (arxmlModel.TryGetUpdateBitAndByteOrder(systemSignalGroupName, out var updateBitValue, out var byteOrder))
                            //{
                            //    updateIndicationBitPosition = updateBitValue;
                            //    if (byteOrder >= (byte)E2EByteOrder.Motorola && byteOrder <= (byte)E2EByteOrder.Opaque)
                            //    {
                            //        e2eByteOrder = (E2EByteOrder)byteOrder;
                            //    }
                            //}
                        }
                    }

                    var e2eSignalList = new List<E2ESignal>();
                    var signals = frame.Signals
                        .Where(x => x.Name.StartsWith(e2eSignalPrefix, StringComparison.OrdinalIgnoreCase))
                        .OrderByDescending(x => x.Name.IsCntr()) // AliveCnt 排第一个
                        .ThenBy(x => x.Offset == updateIndicationBitPosition) // UB 排最后一个
                        .ThenBy(x => x.Name.IsChks()) // checksum 排倒数第二个
                        .ThenBy(x => x.Name)
                        .ToArray();
                    foreach (var signal in signals)
                    {
                        var startBit = (byte)signal.Offset;
                        var length = (byte)signal.Length;

                        // LIN 报文中没有 UB 信号，无需配置
                        //var sigType = startBit == updateIndicationBitPosition ? SigType.UB : 0;
                        var sigType = (SigType)0;

                        var e2eSignal = new E2ESignal(signal.Name, startBit, length, sigType);
                        e2eSignalList.Add(e2eSignal);
                    }

                    // LIN 报文中没有 UB 信号，无需配置
                    //if (updateIndicationBitPosition.HasValue
                    //    && !e2eSignalList.Any(x => x.startBit == updateIndicationBitPosition))
                    //{
                    //    var e2eSignal = new E2EsignalList(updateIndicationBitPosition.Value, 1, SigType.UB);
                    //    e2eSignalList.Add(e2eSignal);
                    //}

                    // 这里需要根据信号组中，是否有 Cntr 和 Chks 判断是否为 E2E 报文
                    // 如果不是，则 dataId 设置为 null，不用在 UI 中显示
                    var isE2E = frame.Signals.Any(x => x.Name.IsCntr() || x.Name.IsChks());
                    if (isE2E && !dataId.HasValue)
                    {
                        dataId = 0;
                    }

                    var groups = new[] { new E2ESignalGroup(dataId, e2eSignalList.ToArray()) };
                    var e2eFrame = new E2EFrame((uint)frame.Id, cycle, frame.Dlc, e2eByteOrder, groups);

                    if (isTx)
                    {
                        txE2EFrames.Add(e2eFrame);
                    }
                    else
                    {
                        rxE2EFrames.Add(e2eFrame);
                    }
                }

                var ecuNode = node.Name;
                var key = CaplParamConsts.E2ELinFrameTxList;
                var value = JsonUtils.Serialize(txE2EFrames);
                collection.AddValue(ecuNode, key, value, source, order);

                key = CaplParamConsts.E2ELinFrameRxList;
                value = JsonUtils.Serialize(rxE2EFrames);
                collection.AddValue(ecuNode, key, value, source, order);
            }
        }
    }
}
