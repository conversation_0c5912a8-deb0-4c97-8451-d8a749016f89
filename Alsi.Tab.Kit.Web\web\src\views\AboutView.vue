<template>
  <div class="about">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="app-logo-section">
        <img src="@/assets/logo.svg" alt="TabKit Logo" class="logo-icon" />
        <div class="app-info">
          <h1>TabKit</h1>
          <p class="version">版本 1.0.0</p>
        </div>
      </div>
    </div>

    <!-- 应用信息 -->
    <div class="app-info-cards">
      <el-card class="info-card">
        <template #header>
          <div class="card-header">
            <font-awesome-icon icon="info-circle" />
            <span>关于 TabKit</span>
          </div>
        </template>

        <div class="info-content">
          <p class="description">
            Alsi.Tab.Kit 是一个集成多种工具的桌面应用程序，旨在提高工作效率和简化日常任务。
          </p>
          <div class="contact-section">
            <span>使用过程中遇到问题或有任何建议，欢迎提出：</span>
            <div class="contact-methods">
              <div class="contact-item" @click="openTeams">
                <font-awesome-icon icon="comment-alt" class="contact-icon" />
                <span class="contact-link">Feedback via Teams</span>
              </div>
              <div class="contact-item">
                <font-awesome-icon icon="envelope" class="contact-icon" />
                <a href="mailto:<EMAIL>" class="contact-link"><EMAIL></a>
              </div>
            </div>
          </div>

        </div>
      </el-card>

      <!-- 技术信息 -->
      <el-card class="info-card">
        <template #header>
          <div class="card-header">
            <font-awesome-icon icon="code" />
            <span>技术信息</span>
          </div>
        </template>

        <div class="tech-info">
          <div class="tech-grid">
            <div class="tech-item">
              <span class="tech-label">前端框架:</span>
              <span class="tech-value">Vue 3 + TypeScript</span>
            </div>
            <div class="tech-item">
              <span class="tech-label">UI组件库:</span>
              <span class="tech-value">Element Plus</span>
            </div>
            <div class="tech-item">
              <span class="tech-label">图标库:</span>
              <span class="tech-value">FontAwesome</span>
            </div>
            <div class="tech-item">
              <span class="tech-label">后端框架:</span>
              <span class="tech-value">.NET Framework</span>
            </div>
            <div class="tech-item">
              <span class="tech-label">桌面框架:</span>
              <span class="tech-value">WPF + WebView2</span>
            </div>
            <div class="tech-item">
              <span class="tech-label">构建工具:</span>
              <span class="tech-value">Vue CLI + Webpack</span>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from "vue";
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import {
  appApi
} from "@/api/appApi";

export default defineComponent({
  name: "AboutView",
  components: {
    FontAwesomeIcon,
  },
  setup() {
    const openTeams = async () => {
      const path = 'https://teams.microsoft.com/l/chat/0/0?users=<EMAIL>';
      await appApi.explorer.startProcess(path);
    };

    return {
      openTeams
    };
  }
});
</script>

<style scoped>
.about {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 40px;
}

.app-logo-section {
  display: flex;
  align-items: center;
  gap: 20px;
  justify-content: center;
}

.logo-icon {
  width: 4rem;
  height: 4rem;
  flex-shrink: 0;
}

.app-info {
  text-align: left;
}

.app-info h1 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin: 0;
}

.version {
  color: #7f8c8d;
  font-size: 1.1rem;
  margin: 0;
}

.app-info-cards {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.info-card {
  border-radius: 8px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: bold;
  color: #2c3e50;
  font-size: 1.1rem;
}

.info-content {
  line-height: 1.6;
}

.description {
  color: #555;
  margin-bottom: 10px;
}

.features h3 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 1.2rem;
}

.features ul {
  list-style: none;
  padding: 0;
}

.features li {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.feature-icon {
  color: #3498db;
  margin-top: 2px;
  flex-shrink: 0;
}

.features li strong {
  color: #2c3e50;
}

.tech-info {
  padding: 10px 0;
}

.tech-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.tech-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.tech-label {
  color: #7f8c8d;
  font-weight: 500;
}

.tech-value {
  color: #2c3e50;
  font-weight: bold;
}

.contact-section {
  margin-top: 15px;
}

.contact-methods {
  display: flex;
  gap: 40px;
  margin-top: 10px;
  flex-wrap: wrap;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.contact-icon {
  color: var(--el-color-primary);
  width: 16px;
  flex-shrink: 0;
}

.contact-link {
  color: var(--el-color-primary);
  text-decoration: none;
  transition: color 0.3s ease;
  font-weight: 500;
  cursor: pointer;
}

.contact-link:hover {
  color: var(--el-color-primary-dark-1);
  text-decoration: underline;
}



@media (max-width: 768px) {
  .about {
    padding: 15px;
  }

  .app-logo-section {
    flex-direction: column;
    gap: 15px;
  }

  .app-info {
    text-align: center;
  }

  .app-info h1 {
    font-size: 2rem;
  }

  .logo-icon {
    width: 3rem;
    height: 3rem;
  }

  .tech-grid {
    grid-template-columns: 1fr;
  }

  .tech-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }

  .features li {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .contact-methods {
    flex-direction: column;
    gap: 12px;
  }
}
</style>
