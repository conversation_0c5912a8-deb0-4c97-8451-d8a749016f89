using System;

namespace Alsi.Tab.Kit.Web.Dto
{
    public class ExternalAppDto
    {
        public Guid Id { get; set; }

        public string Name { get; set; }

        public string Description { get; set; }

        public string IconPath { get; set; }

        public string[] Tags { get; set; } = Array.Empty<string>();

        public bool ExeExists { get; set; }

        public bool IconExists { get; set; }

        public string FullExePath { get; set; }

        public string WorkingDirectory { get; set; }
    }

    public class LaunchExternalAppRequest
    {
        public Guid AppId { get; set; }
    }
}
