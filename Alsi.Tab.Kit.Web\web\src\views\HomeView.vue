<template>
  <div class="home">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>TabKit</h1>
      <p>TabKit 中集成了多种实用工具</p>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="3" animated />
    </div>

    <!-- 功能卡片网格 -->
    <div v-else class="tools-grid">
      <!-- Log转换工具卡片 -->
      <el-card class="tool-card" shadow="hover" @click="navigateToTool('/log-converter')">
        <div class="tool-icon">
          <font-awesome-icon icon="exchange-alt" />
        </div>
        <h3>Log 转换工具</h3>
        <p>支持 ASC、BLF 等日志格式转换，提供文件分割功能。</p>
        <div class="tool-features">
          <el-tag size="small">格式转换</el-tag>
          <el-tag size="small" type="success">文件分割</el-tag>
        </div>
      </el-card>

      <!-- 参数工具卡片 -->
      <el-card class="tool-card" shadow="hover" @click="navigateToTool('/parameter-tool')">
        <div class="tool-icon">
          <font-awesome-icon icon="cogs" />
        </div>
        <h3>参数工具</h3>
        <p>处理 CAPL 的 .cin 文件，支持模板选择和参数替换功能。</p>
        <div class="tool-features">
          <el-tag size="small">参数替换</el-tag>
          <el-tag size="small" type="success">模板管理</el-tag>
        </div>
      </el-card>

      <!-- 外部应用程序卡片 -->
      <el-card
        v-for="app in externalApps"
        :key="app.id"
        class="tool-card external-app-card"
        :class="{ 'app-unavailable': !app.exeExists }"
        shadow="hover"
        @click="launchExternalApp(app)"
      >
        <!-- 不可用状态标识 -->
        <div v-if="!app.exeExists" class="external-app-badge">
          <el-tag size="small" type="danger">不可用</el-tag>
        </div>

        <!-- 应用图标 -->
        <div class="tool-icon">
          <img
            v-if="app.iconExists"
            :src="getAppIconUrl(app.id)"
            :alt="app.name"
            class="app-icon"
            @error="handleIconError($event)"
          />
          <font-awesome-icon v-else icon="cube" class="default-app-icon" />
        </div>

        <!-- 应用信息 -->
        <h3>{{ app.name || 'Unknown App' }}</h3>
        <p>{{ app.description || '无描述信息' }}</p>

        <!-- 应用标签 -->
        <div v-if="app.tags && app.tags.length > 0" class="tool-features">
          <el-tag
            v-for="tag in app.tags"
            :key="tag"
            size="small"
            :type="getTagColor(tag)"
          >
            {{ tag }}
          </el-tag>
        </div>
      </el-card>

      <!-- 更多工具卡片（预留） -->
      <el-card class="tool-card coming-soon" shadow="hover">
        <div class="tool-icon">
          <img src="@/assets/logo.svg" alt="Logo" class="logo-icon" />
        </div>
        <h3>更多工具</h3>
        <p>更多工具开发中，敬请期待...</p>
        <div class="tool-features">
          <el-tag size="small" type="info">即将推出</el-tag>
        </div>
      </el-card>
    </div>


  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted } from "vue";
import { useRouter } from "vue-router";
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import { appApi, ExternalApp } from "@/api/appApi";
import { ElMessage } from 'element-plus';

export default defineComponent({
  name: "HomeView",
  components: {
    FontAwesomeIcon,
  },
  setup() {
    const router = useRouter();
    const externalApps = ref<ExternalApp[]>([]);
    const loading = ref(false);

    const navigateToTool = (path: string) => {
      router.push(path);
    };

    // 生成标签颜色的哈希函数，映射到 Element Plus 预定义类型
    const getTagColor = (tag: string): string => {
      const colors = ['', 'success', 'warning', 'info'];
      let hash = 0;
      for (let i = 0; i < tag.length; i++) {
        hash = tag.charCodeAt(i) + ((hash << 5) - hash);
      }
      return colors[Math.abs(hash) % colors.length];
    };

    // 获取应用图标 URL
    const getAppIconUrl = (appId: string): string => {
      return appApi.externalApps.getIconUrl(appId);
    };

    // 处理图标加载错误
    const handleIconError = (event: Event) => {
      const target = event.target as HTMLImageElement;
      target.style.display = 'none';
      // 可以在这里添加显示默认图标的逻辑
    };

    // 加载外部应用程序列表
    const loadExternalApps = async () => {
      try {
        loading.value = true;
        const response = await appApi.externalApps.getList();
        externalApps.value = response.data;
      } catch (error) {
        console.error('加载外部应用程序失败:', error);
        // 静默处理错误，不显示错误消息，因为可能是没有配置外部应用
      } finally {
        loading.value = false;
      }
    };

    // 启动外部应用程序
    const launchExternalApp = async (app: ExternalApp) => {
      if (!app.exeExists) {
        ElMessage.error(`应用程序 "${app.name}" 的可执行文件不存在`);
        return;
      }

      try {
        await appApi.externalApps.launch({ appId: app.id });
        ElMessage.success(`已启动 "${app.name}"`);
      } catch (error: any) {
        console.error('启动外部应用程序失败:', error);
        ElMessage.error(error.response?.data?.message || `启动 "${app.name}" 失败`);
      }
    };

    onMounted(() => {
      loadExternalApps();
    });

    return {
      navigateToTool,
      externalApps,
      loading,
      getTagColor,
      launchExternalApp,
      getAppIconUrl,
      handleIconError,
    };
  },
});
</script>

<style scoped>
.home {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-top: 40px;
  margin-bottom: 40px;
}

.page-header h1 {
  font-size: 2.5rem;
  color: var(--el-text-color-primary);
  margin-bottom: 10px;
}

.page-header p {
  font-size: 1.1rem;
  color: var(--el-text-color-regular);
}

.tools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 16px;
  margin-bottom: 40px;
}

.tool-card {
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 12px;
  padding: 16px;
  text-align: center;
  min-height: 220px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.tool-card:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.tool-card.coming-soon {
  opacity: 0.7;
  cursor: not-allowed;
}

.tool-card.coming-soon:hover {
  transform: none;
  box-shadow: none;
}

.tool-icon {
  font-size: 2rem;
  color: var(--el-color-primary);
  margin-bottom: 16px;

  .logo-icon {
    width: 2rem;
    height: 2rem;
  }
}

.tool-card h3 {
  font-size: 1.3rem;
  color: var(--el-text-color-primary);
  margin-bottom: 12px;
}

.tool-card p {
  color: var(--el-text-color-regular);
  line-height: 1.5;
  margin-bottom: 16px;
  flex-grow: 1;
  font-size: 0.9rem;
}

.tool-features {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
}

/* External Apps 相关样式 */

.external-app-card {
  position: relative;
}

.external-app-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 1;
}

.external-app-card.app-unavailable {
  opacity: 0.6;
  cursor: not-allowed;
}

.external-app-card.app-unavailable:hover {
  transform: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.app-icon {
  width: 2rem;
  height: 2rem;
  object-fit: contain;
}

.default-app-icon {
  font-size: 2rem;
  color: var(--el-color-primary);
}

.app-status {
  margin-bottom: 10px;
}

/* 加载状态样式 */
.loading-container {
  padding: 20px;
}

@media (max-width: 768px) {
  .tools-grid {
    grid-template-columns: 1fr;
  }

  .page-header h1 {
    font-size: 2rem;
  }
}
</style>
