<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{D7D76682-65BB-461C-B1E9-AF038AAE0D22}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Alsi.Tab.Kit.Web</RootNamespace>
    <AssemblyName>Alsi.Tab.Kit.Web</AssemblyName>
    <TargetFrameworkVersion>v4.6.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\x64\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <OutputPath>bin\x64\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Numerics" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Controllers\CinParameterController.cs" />
    <Compile Include="Controllers\DataLogConvertController.cs" />
    <Compile Include="Controllers\ExternalAppsController.cs" />
    <Compile Include="Controllers\TestController.cs" />
    <Compile Include="Controllers\WebControllerBase.cs" />
    <Compile Include="Dto\CinParameterDto.cs" />
    <Compile Include="Dto\ExternalAppDto.cs" />
    <Compile Include="Dto\TestModeDto.cs" />
    <Compile Include="MapperEnv.cs" />
    <Compile Include="Mapping\MappingProfile.cs" />
    <Compile Include="TabKitWebAssembly.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\Alsi.Common\Alsi.App.Desktop\Alsi.App.Desktop.csproj">
      <Project>{47a11109-4f55-41ce-9bd3-c74687d7a212}</Project>
      <Name>Alsi.App.Desktop</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Alsi.Common\Alsi.App\Alsi.App.csproj">
      <Project>{2bf46d86-9704-494a-8998-a478b601df80}</Project>
      <Name>Alsi.App</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Alsi.Common\Alsi.Common.Parsers\Alsi.Common.Parsers.csproj">
      <Project>{2306abe4-156f-48bf-a890-c67ef1c28c67}</Project>
      <Name>Alsi.Common.Parsers</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Alsi.Common\Alsi.Common.Utils\Alsi.Common.Utils.csproj">
      <Project>{d45049c2-92f3-a823-ad5f-89ee4256d61b}</Project>
      <Name>Alsi.Common.Utils</Name>
    </ProjectReference>
    <ProjectReference Include="..\Alsi.Tab.Kit.Core\Alsi.Tab.Kit.Core.csproj">
      <Project>{eefba350-c69e-4e95-aff2-f5637e80de37}</Project>
      <Name>Alsi.Tab.Kit.Core</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="build-web.ps1" />
    <EmbeddedResource Include="web.zip" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="AutoMapper">
      <Version>8.1.1</Version>
    </PackageReference>
    <PackageReference Include="Microsoft.AspNet.WebApi.Owin">
      <Version>5.3.0</Version>
    </PackageReference>
    <PackageReference Include="Newtonsoft.Json">
      <Version>13.0.3</Version>
    </PackageReference>
    <PackageReference Include="System.Runtime.CompilerServices.Unsafe">
      <Version>6.1.1</Version>
    </PackageReference>
  </ItemGroup>
  <Target Name="MyPreBuildTask" BeforeTargets="PreBuildEvent">
    <Exec Command="powershell.exe -ExecutionPolicy Bypass -File &quot;$(ProjectDir)\build-web.ps1&quot;" />
  </Target>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>