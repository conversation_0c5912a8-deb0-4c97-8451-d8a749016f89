using Alsi.App.Desktop.Utils;
using Alsi.Common.Utils;
using Alsi.Tab.Kit.Core.Models;
using Alsi.Tab.Kit.Core.Services;
using Alsi.Tab.Kit.Web.Dto;
using System;
using System.Linq;
using System.Threading.Tasks;
using System.Web.Http;

namespace Alsi.Tab.Kit.Web.Controllers
{
    public class CinParameterController : WebControllerBase
    {
        private readonly CinTemplatesService _cinTemplatesService;
        private readonly CinParameterService _cinParameterService;
        private readonly SourceFileService _sourceFileService;

        public CinParameterController()
        {
            _cinTemplatesService = new CinTemplatesService();
            _cinParameterService = new CinParameterService();
            _sourceFileService = new SourceFileService();
        }

        [HttpGet]
        [ActionName("templates")]
        public IHttpActionResult GetCinTemplates()
        {
            var templates = _cinTemplatesService.GetCinTemplates();
            var templateDtos = templates.Select(template => _mapper.Map<CinTemplate, CinTemplateDto>(template)).ToList();
            return Ok(templateDtos);
        }

        [HttpPost]
        [ActionName("select-file")]
        public IHttpActionResult SelectFile()
        {
            UiUtils.SelectFile(out var filePath, "CIN File", "*.cin");
            return Ok(filePath);
        }

        [HttpPost]
        [ActionName("parse")]
        public IHttpActionResult ParseCinFile([FromBody] CinParameterParseRequest request)
        {
            var response = _cinParameterService.ParseCinFile(request.SourceType, request.TemplateId, request.FilePath);
            return Ok(response);
        }

        [HttpPost]
        [ActionName("process")]
        public IHttpActionResult ProcessCinFile([FromBody] CinParameterRequest request)
        {
            var response = _cinParameterService.ProcessCinFile(request);
            return Ok(response);
        }

        [HttpPost]
        [ActionName("select-source-files")]
        public IHttpActionResult SelectSourceFiles()
        {
            UiUtils.SelectMultipleFile(out var filePathArray, "Source Files", "*.arxml;*.sddb;*.ldf");
            return Ok(filePathArray);
        }

        [HttpPost]
        [ActionName("add-source-files")]
        public IHttpActionResult AddSourceFiles([FromBody] AddSourceFilesRequest request)
        {
            _sourceFileService.AddSourceFiles(request.FilePaths);
            return GetSourceFiles();
        }

        [HttpGet]
        [ActionName("source-files")]
        public IHttpActionResult GetSourceFiles()
        {
            var sourceFiles = _sourceFileService.GetAllSourceFiles();
            var fileDtos = sourceFiles.Select(file => _mapper.Map<SourceFile, SourceFileDto>(file)).ToList();
            return Ok(fileDtos);
        }

        [HttpPost]
        [ActionName("parse-source-file")]
        public async Task<IHttpActionResult> ParseSourceFileAsync([FromBody] ParseSourceFileRequest request)
        {
            try
            {
                var result = await _sourceFileService.ParseSourceFileAsync(request.FileId);
                var fileDto = _mapper.Map<SourceFile, SourceFileDto>(result);
                return Ok(fileDto);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpPost]
        [ActionName("remove-source-file")]
        public IHttpActionResult RemoveSourceFile([FromBody] RemoveSourceFileRequest request)
        {
            var success = _sourceFileService.RemoveSourceFile(request.FileId);
            return Ok(new { success });
        }

        [HttpPost]
        [ActionName("clear-source-files")]
        public IHttpActionResult ClearSourceFiles()
        {
            _sourceFileService.ClearAllSourceFiles();
            return Ok(new { success = true });
        }
    }
}
