﻿using Alsi.Common.Parsers.Sddb;
using Alsi.Common.Utils;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;


namespace Alsi.Tab.Kit.Core.Services.Capl.Params
{
    internal class VoltageRelatedDtcLoader
    {
        public void Load(string ecuNode, string source, SW[] sws, IParamCollection collection, int order)
        {
            var dtcInfos = new List<DTCInformation>();
            foreach (var sw in sws)
            {
                foreach (var service in sw.Services.Service)
                {
                    foreach (var dtc in service.DTCS.DTC)
                    {
                        var id = dtc.ID.Trim();
                        if (id.ToUpper().StartsWith("0X"))
                        {
                            id = id.Substring(2);
                        }

                        if (!uint.TryParse(id, NumberStyles.HexNumber, null, out var parsedDtcId))
                        {
                            continue;
                        }

                        int.TryParse(dtc.AgedDTCLimit, out var agedDTCLimit);
                        int.TryParse(dtc.ConfirmedDTCLimit, out var confirmedDTCLimit);
                        int.TryParse(dtc.UnconfirmedDTCLimit, out var unconfirmedDTCLimit);
                        int.TryParse(dtc.IncrementStepSize, out var stepUp);
                        int.TryParse(dtc.DecrementStepSize, out var stepDown);

                        var dtcInfo = new DTCInformation()
                        {
                            dtc = parsedDtcId,
                            agedDTCLimit = agedDTCLimit,
                            confirmedDTCLimit = confirmedDTCLimit,
                            unconfirmedDTCLimit = unconfirmedDTCLimit,
                            stepUp = stepUp,
                            stepDown = stepDown
                        };

                        dtcInfos.Add(dtcInfo);
                    }
                }
            }

            var dtcs = dtcInfos.Select(x => x.dtc).Distinct().OrderBy(x => x).ToArray();

            var dtcNames = new Dictionary<uint, string>();
            foreach (var sw in sws)
            {
                foreach (var service in sw.Services.Service)
                {
                    foreach (var subfunction in service.Subfunctions.Subfunction)
                    {
                        foreach (var responseItem in subfunction.ResponseItems.ResponseItem)
                        {
                            if (responseItem.CompareValue == null)
                            {
                                continue;
                            }

                            var id = responseItem.CompareValue.Trim();
                            if (id.ToUpper().StartsWith("=0X"))
                            {
                                id = id.Substring(3);
                            }

                            if (id.ToUpper().StartsWith("0X"))
                            {
                                id = id.Substring(2);
                            }

                            if (!uint.TryParse(id, NumberStyles.HexNumber, null, out var dtc))
                            {
                                continue;
                            }

                            if (!dtcs.Contains(dtc))
                            {
                                continue;
                            }

                            dtcNames[dtc] = responseItem.Name;
                        }
                    }
                }
            }

            if (TryLoadDtcInfo(dtcNames, dtcInfos, "ECU Supply Voltage Too High", out var overVoltageDtcValue))
            {
                collection.AddValue(ecuNode, CaplParamConsts.OverVoltageDtc, overVoltageDtcValue, source, order);
            }

            if (TryLoadDtcInfo(dtcNames, dtcInfos, "ECU Supply Voltage Too Low", out var underVoltageDtcValue))
            {
                collection.AddValue(ecuNode, CaplParamConsts.UnderVoltageDtc, underVoltageDtcValue, source, order);
            }

            if (TryLoadDtcInfo(dtcNames, dtcInfos, "Supply Voltage", out var supplyVoltageComparedFailureDtcValue))
            {
                collection.AddValue(ecuNode, CaplParamConsts.SupplyVoltageComparedFailureDtc,
                    supplyVoltageComparedFailureDtcValue, source, order);
            }
        }

        private bool TryLoadDtcInfo(Dictionary<uint, string> dtcNames, List<DTCInformation> dtcInfos,
            string name, out string value)
        {
            value = string.Empty;
            foreach (var dtc in dtcNames.Keys)
            {
                if (dtcNames[dtc].Trim().Equals(name, StringComparison.OrdinalIgnoreCase))
                {
                    var dtcInfo = dtcInfos.FirstOrDefault(x => x.dtc == dtc);
                    if (dtcInfo != null)
                    {
                        value = JsonUtils.Serialize(dtcInfo);
                        return true;
                    }
                }
            }

            return false;
        }
    }
}
