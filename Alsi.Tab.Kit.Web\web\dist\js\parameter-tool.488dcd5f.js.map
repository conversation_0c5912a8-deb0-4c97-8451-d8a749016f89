{"version": 3, "file": "js/parameter-tool.488dcd5f.js", "mappings": "0LAEA,MAAMA,EAAa,CCDZC,MAAM,kBDEPC,EAAa,CCAVD,MAAM,gBDCTE,EAAa,CCCRF,MAAM,0BDAXG,EAAa,CCONH,MAAM,0BDNbI,EAAa,CCOJJ,MAAM,iBDNfK,EAAa,CACjBC,IAAK,ECmBMN,MAAM,cDhBbO,EAAa,CCkBJP,MAAM,eDjBfQ,EAAa,CCuBNR,MAAM,oBDtBbS,EAAa,CC4BET,MAAM,oBD3BrBU,EAAc,CAClBJ,IAAK,ECsCwDN,MAAM,oBDnC/DW,EAAc,CAClBL,IAAK,ECwCsBN,MAAM,aDpC7B,SAAUY,EAAOC,EAAUC,EAAYC,EAAYC,EAAYC,EAAWC,GAC9E,MAAMC,GAAyBC,EAAAA,EAAAA,IAAkB,eAC3CC,GAAuBD,EAAAA,EAAAA,IAAkB,aACzCE,GAAwBF,EAAAA,EAAAA,IAAkB,cAC1CG,GAAsBH,EAAAA,EAAAA,IAAkB,YACxCI,GAA6BJ,EAAAA,EAAAA,IAAkB,mBAC/CK,GAAsBL,EAAAA,EAAAA,IAAkB,YACxCM,GAAqBN,EAAAA,EAAAA,IAAkB,WACvCO,GAAsBP,EAAAA,EAAAA,IAAkB,YACxCQ,GAA6BR,EAAAA,EAAAA,IAAkB,mBAC/CS,GAA8BT,EAAAA,EAAAA,IAAkB,oBAChDU,GAA8BV,EAAAA,EAAAA,IAAkB,oBAEtD,OAAQW,EAAAA,EAAAA,OCnCRC,EAAAA,EAAAA,IA4EM,MA5ENjC,EA4EM,EA1EJkC,EAAAA,EAAAA,IAiEM,MAjENhC,EAiEM,EA/DJgC,EAAAA,EAAAA,IA2DM,MA3DN/B,EA2DM,CDzBJY,EAAO,KAAOA,EAAO,ICjCrBmB,EAAAA,EAAAA,IAIM,OAJDjC,MAAM,kBAAgB,EACzBiC,EAAAA,EAAAA,IAAiB,UAAb,aACJA,EAAAA,EAAAA,IACM,OADDjC,MAAM,qBDkCT,KC9BJiC,EAAAA,EAAAA,IAUM,MAVN9B,EAUM,EATJ8B,EAAAA,EAAAA,IAQM,MARN7B,EAQM,CANIS,EAAAqB,WAAWC,OAAS,ID+BvBJ,EAAAA,EAAAA,OChCLK,EAAAA,EAAAA,IAE6CjB,EAAA,CD+BvCb,IAAK,EACL+B,WClCgBxB,EAAAyB,qBDmChB,sBAAuBxB,EAAO,KAAOA,EAAO,GAAMyB,GCnClC1B,EAAAyB,qBAAoBC,GAAEC,KAAK,QAASC,QAAS5B,EAAA6B,gBACpCC,YAAY,YAAYC,MAAA,sCACpDC,SAAQhC,EAAAiC,qBAAsBC,UAAA,IDwC1B,KAAM,EAAG,CAAC,aAAc,UAAW,eACtCC,EAAAA,EAAAA,IAAoB,IAAI,ICvC5BC,EAAAA,EAAAA,IAAuF5B,EAAA,CAA5E6B,KAAK,UAAUV,KAAK,QAASW,QAAOtC,EAAAuC,iBD4C5C,CACDC,SAASC,EAAAA,EAAAA,IC7CqD,IAAWxC,EAAA,KAAAA,EAAA,KD8CvEyC,EAAAA,EAAAA,IC9C4D,kBDgD9DC,EAAG,EACHC,GAAI,CAAC,IACJ,EAAG,CAAC,aCjDPR,EAAAA,EAAAA,IAC0C5B,EAAA,CAD/B6B,KAAK,UAAUV,KAAK,QAASW,QAAOtC,EAAA6C,cAAed,MAAA,uBAC3De,QAAS9C,EAAA+C,YDuDT,CACDP,SAASC,EAAAA,EAAAA,ICxDa,IAAMxC,EAAA,KAAAA,EAAA,KDyD1ByC,EAAAA,EAAAA,ICzDoB,aD2DtBC,EAAG,EACHC,GAAI,CAAC,IACJ,EAAG,CAAC,UAAW,iBCzDtBR,EAAAA,EAAAA,IAAoC3B,EAAA,CAAxBsB,MAAA,mBAGkB/B,EAAAgD,UAAU1B,OAAS,ID2D5CJ,EAAAA,EAAAA,OC3DLC,EAAAA,EAAAA,IAKM,MALN3B,EAKM,EAJJ4C,EAAAA,EAAAA,IAAyG1B,EAAA,CD4DnGc,WC5DaxB,EAAAiD,cD6Db,sBAAuBhD,EAAO,KAAOA,EAAO,GAAMyB,GC7DrC1B,EAAAiD,cAAavB,GAAEC,KAAK,QAAQG,YAAY,YAAYI,UAAA,GAAUH,MAAA,iBDkE1E,KAAM,EAAG,CAAC,gBCjEjBX,EAAAA,EAAAA,IAEM,MAFN1B,EAEM,EADJ0B,EAAAA,EAAAA,IAA0E,YAApE,MAAE8B,EAAAA,EAAAA,IAAGlD,EAAAmD,kBAAkB7B,QAAS,OAAG4B,EAAAA,EAAAA,IAAGlD,EAAAgD,UAAU1B,QAAS,OAAI,SDqEnEa,EAAAA,EAAAA,IAAoB,IAAI,IChE5Bf,EAAAA,EAAAA,IA4BM,MA5BNzB,EA4BM,EA3BJyC,EAAAA,EAAAA,IA0BWtB,EAAA,CA1BAsC,KAAMpD,EAAAmD,kBAAmBE,OAAA,GAAOtB,MAAA,6BAAmCJ,KAAK,SDsEhF,CACDa,SAASC,EAAAA,EAAAA,ICtET,IAA6E,EAA7EL,EAAAA,EAAAA,IAA6EzB,EAAA,CAA5D2C,KAAK,OAAOC,MAAM,MAAMC,MAAM,MAAM,8BACrDpB,EAAAA,EAAAA,IAA4EzB,EAAA,CAA3D2C,KAAK,OAAOC,MAAM,KAAKC,MAAM,MAAM,8BACpDpB,EAAAA,EAAAA,IAWkBzB,EAAA,CAXD4C,MAAM,MAAM,YAAU,ODoFlC,CCnFQf,SAAOC,EAAAA,EAAAA,IAQVgB,GARiB,EACvBrC,EAAAA,EAAAA,IAOM,MAPNxB,EAOM,EANJwC,EAAAA,EAAAA,IAC0E1B,EAAA,CDoFtEc,WCrFexB,EAAA0D,gBAAgBD,EAAME,IAAIC,MDsFzC,sBAAwBlC,GCtFT1B,EAAA0D,gBAAgBD,EAAME,IAAIC,MAAIlC,EAAGI,YAAY,QAAQH,KAAK,QAC1EK,SAAMN,GAAE1B,EAAA6D,kBAAkBJ,EAAME,IAAIC,MAAOzE,MAAM,qBD0F/C,KAAM,EAAG,CAAC,aAAc,sBAAuB,aCzFnCa,EAAA8D,mBAAmBL,EAAME,OD2FnCzC,EAAAA,EAAAA,OC3FPK,EAAAA,EAAAA,IAGYf,EAAA,CDyFJf,IAAK,EC5FmC4C,KAAK,OAAOV,KAAK,QAC9DW,QAAKZ,GAAE1B,EAAA+D,qBAAqBN,EAAME,KAAMxE,MAAM,eDgGxC,CACDqD,SAASC,EAAAA,EAAAA,ICjG8C,IAE/DxC,EAAA,KAAAA,EAAA,KDgGUyC,EAAAA,EAAAA,IClGqD,WDoGvDC,EAAG,EACHC,GAAI,CAAC,IACJ,KAAM,CAAC,cACVT,EAAAA,EAAAA,IAAoB,IAAI,OAGhCQ,EAAG,KCpGPP,EAAAA,EAAAA,IAUkBzB,EAAA,CAVD4C,MAAM,QAAQC,MAAM,MAAM,4BD0GtC,CCzGQhB,SAAOC,EAAAA,EAAAA,IAMVgB,GANiB,CACZzD,EAAAgE,mBAAmBP,EAAME,IAAIC,QD2GjC1C,EAAAA,EAAAA,OC3GPC,EAAAA,EAAAA,IAKM,MALNtB,EAKM,EAJJuC,EAAAA,EAAAA,IAEUvB,EAAA,MD0GF2B,SAASC,EAAAA,EAAAA,IC3Gf,IAAY,EAAZL,EAAAA,EAAAA,IAAYxB,KD8GN+B,EAAG,KC5GXvB,EAAAA,EAAAA,IAAqD,aAAA8B,EAAAA,EAAAA,IAA5ClD,EAAAgE,mBAAmBP,EAAME,IAAIC,OAAI,QDgHrC1C,EAAAA,EAAAA,OC9GPC,EAAAA,EAAAA,IAA4C,OAA5CrB,EAA+B,aDgH/B6C,EAAG,MAGPA,EAAG,GACF,EAAG,CAAC,cC5GXP,EAAAA,EAAAA,IAAqDrB,EAAA,CAAnCkD,cAAcjE,EAAAkE,mBAAiB,6BAInD9B,EAAAA,EAAAA,IACsCpB,EAAA,CD6GpCQ,WC9GyBxB,EAAAmE,mBD+GzB,sBAAuBlE,EAAO,KAAOA,EAAO,GAAMyB,GC/GzB1B,EAAAmE,mBAAkBzC,GAAG,cAAa1B,EAAAoE,iBAAmB,gBAAepE,EAAAqE,oBAC5FC,cAActE,EAAAuE,mBDkHd,KAAM,EAAG,CAAC,aAAc,cAAe,gBAAiB,mBC/G3DnC,EAAAA,EAAAA,IACoEnB,EAAA,CDgHlEO,WCjHyBxB,EAAAwE,0BDkHzB,sBAAuBvE,EAAO,KAAOA,EAAO,GAAMyB,GClHzB1B,EAAAwE,0BAAyB9C,GAAG+C,MAAOzE,EAAA0E,yBAA2BC,UAAU,EAChGC,MAAO5E,EAAA6E,kBAAoBC,UAAS9E,EAAA+E,2BDsHpC,KAAM,EAAG,CAAC,aAAc,QAAS,QAAS,eAEjD,C,0IEjMA,MAAM7F,EAAa,CAAEC,MAAO,sBACtBC,EAAa,CAAED,MAAO,iBAa5B,OAA4B6F,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,mBACRC,MAAO,CACL1D,WAAY,CAAEa,KAAM8C,SACpBV,MAAO,CAAC,EACRE,SAAU,CAAEtC,KAAM8C,QAAS3C,SAAS,GACpCoC,MAAO,CAAEpC,QAAS,QAEpB4C,MAAO,CAAC,oBAAqB,eAAgB,WAC7CC,KAAAA,CAAMC,GAAgBC,KAAMC,ICc9B,MAAMN,EAAQI,EAMRC,EAAOC,EAOPC,GAAUC,EAAAA,EAAAA,KAAI,GACdC,GAAeD,EAAAA,EAAAA,IAAI,IAGnBE,GAAiBC,EAAAA,EAAAA,IAAS,KAC9B,GAAoB,OAAhBX,EAAMT,YAAkCqB,IAAhBZ,EAAMT,MAChC,MAAO,GAGT,GAA2B,kBAAhBS,EAAMT,MAEf,IACE,MAAMsB,EAASC,KAAKC,MAAMf,EAAMT,OAChC,OAAOuB,KAAKE,UAAUH,EAAQ,KAAM,E,CACpC,MAEA,OAAOb,EAAMT,K,MAIf,IACE,OAAOuB,KAAKE,UAAUhB,EAAMT,MAAO,KAAM,E,CACzC,MACA,OAAO0B,OAAOjB,EAAMT,M,KAM1B2B,EAAAA,EAAAA,IAAM,IAAMlB,EAAM1D,WAAa6E,IAC7BZ,EAAQhB,MAAQ4B,EACZA,IACFV,EAAalB,MAAQmB,EAAenB,UAIxC2B,EAAAA,EAAAA,IAAMX,EAAUY,IACdd,EAAK,oBAAqBc,MAG5BD,EAAAA,EAAAA,IAAM,IAAMlB,EAAMT,MAAO,KACnBgB,EAAQhB,QACVkB,EAAalB,MAAQmB,EAAenB,SAKxC,MAAM6B,EAAcA,KAClBb,EAAQhB,OAAQ,GAGZ8B,EAAe9B,IACdS,EAAMP,UACTY,EAAK,eAAgBd,IAInB+B,EAAgBA,KACftB,EAAMP,UACTY,EAAK,UAAWI,EAAalB,OAE/B6B,KDhBF,MAAO,CAACtG,EAAUC,KAChB,MAAMS,GAAsBH,EAAAA,EAAAA,IAAkB,YACxCC,GAAuBD,EAAAA,EAAAA,IAAkB,aACzCkG,GAAuBlG,EAAAA,EAAAA,IAAkB,aAE/C,OAAQW,EAAAA,EAAAA,OCtGRK,EAAAA,EAAAA,IAyBYkF,EAAA,CD8EVjF,WCtGSiE,EAAAhB,MDuGT,sBAAuBxE,EAAO,KAAOA,EAAO,GAAMyB,GCvGzC+D,EAAOhB,MAAA/C,GACfkD,MAAO5E,EAAA4E,MACRpB,MAAM,MACL,eAAc8C,EACf,wBDwGCI,EAAAA,EAAAA,IAAa,CACdlE,SAASC,EAAAA,EAAAA,ICvGT,IAUM,EAVNrB,EAAAA,EAAAA,IAUM,MAVNlC,EAUM,EATJkD,EAAAA,EAAAA,IAQE1B,EAAA,CDiGEc,WCxGOmE,EAAAlB,MDyGP,sBAAuBxE,EAAO,KAAOA,EAAO,GAAMyB,GCzG3CiE,EAAYlB,MAAA/C,GACrBW,KAAK,WACJsE,KAAM,GACNhC,SAAU3E,EAAA2E,SACV7C,YAAa9B,EAAA2E,SAAW,GAAK,YAC9B5C,MAAA,2CACC6E,QAAOL,GD0GL,KAAM,EAAG,CAAC,aAAc,WAAY,oBAG3C5D,EAAG,GACF,CC1GwB3C,EAAA2E,cDqIrBmB,EAzBA,CACElC,KC7GK,SD8GLiD,IAAIpE,EAAAA,EAAAA,IC7GR,IAGM,EAHNrB,EAAAA,EAAAA,IAGM,MAHNhC,EAGM,EAFJgD,EAAAA,EAAAA,IAA8C5B,EAAA,CAAlC8B,QAAOgE,GAAW,CD+GtB9D,SAASC,EAAAA,EAAAA,IC/Ge,IAAExC,EAAA,KAAAA,EAAA,KDgHxByC,EAAAA,EAAAA,IChHsB,SDkHxBC,EAAG,EACHC,GAAI,CAAC,MClHbR,EAAAA,EAAAA,IAA+D5B,EAAA,CAApD6B,KAAK,UAAWC,QAAOkE,GDuHzB,CACDhE,SAASC,EAAAA,EAAAA,ICxHgC,IAAExC,EAAA,KAAAA,EAAA,KDyHzCyC,EAAAA,EAAAA,ICzHuC,SD2HzCC,EAAG,EACHC,GAAI,CAAC,SAIXnD,IAAK,OAGT,KAAM,CAAC,aAAc,UAE3B,I,UEtJA,MAAMqH,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,QCLA,MAAM5H,EAAa,CAAEC,MAAO,kBACtBC,EAAa,CAAED,MAAO,kBACtBE,EAAa,CAAEF,MAAO,cACtBG,EAAa,CAAEH,MAAO,qBACtBI,EAAa,CAAEJ,MAAO,kBACtBK,EAAa,CAAEL,MAAO,oBACtBO,EAAa,CAAC,SACdC,EAAa,CAAER,MAAO,iBAgB5B,OAA4B6F,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,mBACRC,MAAO,CACL1D,WAAY,CAAEa,KAAM8C,SACpB4B,WAAY,CAAC,EACbC,aAAc,CAAC,GAEjB5B,MAAO,CAAC,oBAAqB,gBAC7BC,KAAAA,CAAMC,GAAgBC,KAAMC,ICuF9B,MAAMN,EAAQI,EAGRC,EAAOC,EAMPC,GAAUC,EAAAA,EAAAA,KAAI,GACduB,GAAavB,EAAAA,EAAAA,IAAI,IACjBwB,GAAcxB,EAAAA,EAAAA,IAAI,IAClByB,GAAiBzB,EAAAA,EAAAA,IAAmB,IACpC0B,GAA0B1B,EAAAA,EAAAA,KAAI,GAC9B2B,GAAoB3B,EAAAA,EAAAA,IAAS,IAC7B4B,GAAa5B,EAAAA,EAAAA,MAGb6B,GAAU1B,EAAAA,EAAAA,IAAS,KACvB,MAAM2B,EAAO,IAAIC,IAAIvC,EAAM8B,aAAaU,IAAIC,GAAKA,EAAEC,UACnD,OAAOC,MAAMC,KAAKN,GAAMO,SAGpBC,GAAcnC,EAAAA,EAAAA,IAAS,KAC3B,IAAKX,EAAM6B,WACT,MAAO,SAGT,MAAMkB,EAAW/C,EAAM6B,WAAWkB,SAC5BC,EAAWhD,EAAM6B,WAAWmB,SAASC,cAC3C,MAAO,YAAYF,MAAaC,OAG5BE,GAAiBvC,EAAAA,EAAAA,IAAS,KAC9B,IAAIwC,EAASnD,EAAM8B,aAQnB,GALIE,EAAYzC,QACd4D,EAASA,EAAOC,OAAOX,GAAKA,EAAEC,UAAYV,EAAYzC,QAIpDwC,EAAWxC,MAAO,CACpB,MAAM8D,EAAStB,EAAWxC,MAAM+D,cAChCH,EAASA,EAAOC,OAAOX,GACrBA,EAAE/D,KAAK4E,cAAcC,SAASF,IAC9BZ,EAAEe,YAAYF,cAAcC,SAASF,G,CAIzC,OAAOF,KAITjC,EAAAA,EAAAA,IAAM,IAAMlB,EAAM1D,WAAa6E,IAC7BZ,EAAQhB,MAAQ4B,EACZA,IAEFY,EAAWxC,MAAQ,GACnB0C,EAAe1C,MAAQ,GAEnB8C,EAAQ9C,MAAMnD,OAAS,IACzB4F,EAAYzC,MAAQ8C,EAAQ9C,MAAM,IAElCkE,EAAAA,EAAAA,IAAS,KACPC,WAMRxC,EAAAA,EAAAA,IAAMX,EAAUY,IACdd,EAAK,oBAAqBc,MAG5BD,EAAAA,EAAAA,IAAMc,EAAa,MAEjByB,EAAAA,EAAAA,IAAS,KACPC,QAKJ,MAAMC,EAAkBA,KAEtBD,KAGIA,EAA4BA,KAEhC,GAAItB,EAAW7C,MAAO,CACpB,MAAMqE,EAAmBV,EAAe3D,MACxCqE,EAAiBC,QAAQpF,IACvB2D,EAAW7C,MAAMuE,mBAAmBrF,GAAK,I,GAKzC2C,EAAcA,KAClBb,EAAQhB,OAAQ,GAGZwE,EAAyBC,IAC7B/B,EAAe1C,MAAQyE,GAYnBC,EAAuBC,IAC3B,OAAQA,GACN,KAAKC,EAAAA,GAAUlD,OAAQ,MAAO,GAC9B,KAAKkD,EAAAA,GAAUC,QAAS,MAAO,UAC/B,KAAKD,EAAAA,GAAUE,OAAQ,MAAO,UAC9B,KAAKF,EAAAA,GAAUlE,QAAS,MAAO,UAC/B,KAAKkE,EAAAA,GAAUG,KAAM,MAAO,UAC5B,KAAKH,EAAAA,GAAUxB,MAAO,MAAO,OAC7B,KAAKwB,EAAAA,GAAUI,OAAQ,MAAO,OAC9B,QAAS,MAAO,KAIdC,EAAejF,GACL,OAAVA,QAA4BqB,IAAVrB,EACb,GAGY,kBAAVA,EACFA,EAAMnD,OAAS,GAAKmD,EAAMkF,UAAU,EAAG,IAAM,MAAQlF,EAGvD0B,OAAO1B,GAGVmF,EAAkBC,IACtBxC,EAAkB5C,MAAQoF,EAAMpF,MAChC2C,EAAwB3C,OAAQ,GAG5BqF,EAAsBA,KACU,IAAhC3C,EAAe1C,MAAMnD,QAKzBiE,EAAK,eAAgB4B,EAAe1C,OACpCsF,EAAAA,GAAUC,QAAQ,QAAQ7C,EAAe1C,MAAMnD,cAC/CgF,KANEyD,EAAAA,GAAUE,QAAQ,eD/EtB,MAAO,CAACjK,EAAUC,KAChB,MAAMiK,GAAuB3J,EAAAA,EAAAA,IAAkB,aACzC4J,GAAuB5J,EAAAA,EAAAA,IAAkB,aACzCM,GAAqBN,EAAAA,EAAAA,IAAkB,WACvCG,GAAsBH,EAAAA,EAAAA,IAAkB,YACxCI,GAA6BJ,EAAAA,EAAAA,IAAkB,mBAC/C6J,GAAoB7J,EAAAA,EAAAA,IAAkB,UACtCC,GAAuBD,EAAAA,EAAAA,IAAkB,aACzCO,GAAsBP,EAAAA,EAAAA,IAAkB,YACxCkG,GAAuBlG,EAAAA,EAAAA,IAAkB,aAE/C,OAAQW,EAAAA,EAAAA,OCvMRK,EAAAA,EAAAA,IAuGYkF,EAAA,CDiGVjF,WCvMSiE,EAAAhB,MDwMT,sBAAuBxE,EAAO,KAAOA,EAAO,GAAMyB,GCxMzC+D,EAAOhB,MAAA/C,GACfkD,MAAOoD,EAAAvD,MACRjB,MAAM,MACL,eAAc8C,EACf,uBDyMC,CC3HU+D,QAAM5H,EAAAA,EAAAA,IACf,IASM,EATNrB,EAAAA,EAAAA,IASM,MATNzB,EASM,EARJyC,EAAAA,EAAAA,IAA8C5B,EAAA,CAAlC8B,QAAOgE,GAAW,CD6H5B9D,SAASC,EAAAA,EAAAA,IC7HqB,IAAExC,EAAA,KAAAA,EAAA,KD8H9ByC,EAAAA,EAAAA,IC9H4B,SDgI9BC,EAAG,EACHC,GAAI,CAAC,MChIPR,EAAAA,EAAAA,IAMY5B,EAAA,CALV6B,KAAK,UACJC,QAAOwH,EACPQ,SAAoC,IAA1BnD,EAAA1C,MAAenD,QDmIzB,CACDkB,SAASC,EAAAA,EAAAA,ICnIV,IACO,EDmIJC,EAAAA,EAAAA,ICpIH,WACOQ,EAAAA,EAAAA,IAAGiE,EAAA1C,MAAenD,QAAS,KACnC,KDoIEqB,EAAG,GACF,EAAG,CAAC,iBAGXH,SAASC,EAAAA,EAAAA,IC7NT,IA0EM,EA1ENrB,EAAAA,EAAAA,IA0EM,MA1ENlC,EA0EM,EAxEJkC,EAAAA,EAAAA,IAyBM,MAzBNhC,EAyBM,EAxBJgD,EAAAA,EAAAA,IAYY+H,EAAA,CDkNR3I,WC7NO0F,EAAAzC,MD8NP,sBAAuBxE,EAAO,KAAOA,EAAO,GAAMyB,GC9N3CwF,EAAWzC,MAAA/C,GACpBI,YAAY,SACZC,MAAA,gBACCC,SAAQ6G,GD+NN,CACDrG,SAASC,EAAAA,EAAAA,IC7NT,IAAsB,GD8NnBvB,EAAAA,EAAAA,KAAW,IC/NhBC,EAAAA,EAAAA,IAKEoJ,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAJcjD,EAAA9C,MAAPgG,KD+NKvJ,EAAAA,EAAAA,OChOdK,EAAAA,EAAAA,IAKE2I,EAAA,CAHCzK,IAAKgL,EACLlH,MAAOkH,EACPhG,MAAOgG,GDgOD,KAAM,EAAG,CAAC,QAAS,YACpB,QAEN9H,EAAG,GACF,EAAG,CAAC,gBChOTP,EAAAA,EAAAA,IASW1B,EAAA,CDyNPc,WCjOOyF,EAAAxC,MDkOP,sBAAuBxE,EAAO,KAAOA,EAAO,GAAMyB,GClO3CuF,EAAUxC,MAAA/C,GACnBI,YAAY,YACZI,UAAA,GACAH,MAAA,iBDmOG,CCjOQ2I,QAAMjI,EAAAA,EAAAA,IACf,IAA6B,EAA7BL,EAAAA,EAAAA,IAA6BvB,EAAA,MDmOzB2B,SAASC,EAAAA,EAAAA,ICnOJ,IAAU,EAAVL,EAAAA,EAAAA,KAAUuI,EAAAA,EAAAA,IAAAC,EAAAA,WDsOfjI,EAAG,MAGPA,EAAG,GACF,EAAG,CAAC,kBCpOXvB,EAAAA,EAAAA,IAyCM,MAzCN/B,EAyCM,EAxCJ+B,EAAAA,EAAAA,IAIM,MAJN9B,EAIM,EAHJ8B,EAAAA,EAAAA,IAEM,MAFN7B,EAA4B,SACtB2D,EAAAA,EAAAA,IAAGiE,EAAA1C,MAAenD,QAAS,KAAC4B,EAAAA,EAAAA,IAAGkF,EAAA3D,MAAenD,QAAS,QAC7D,MAEFc,EAAAA,EAAAA,IAkCWtB,EAAA,CDmMP+J,QCpOE,aAAJnF,IAAI4B,EACHlE,KAAMgF,EAAA3D,MACPqG,OAAO,MACNC,kBAAkB9B,EACnB,UAAQ,OACRtH,KAAK,SDsOF,CACDa,SAASC,EAAAA,EAAAA,ICrOX,IAA+C,EAA/CL,EAAAA,EAAAA,IAA+CzB,EAAA,CAA9B0B,KAAK,YAAYmB,MAAM,QACxCpB,EAAAA,EAAAA,IAA6EzB,EAAA,CAA5D2C,KAAK,OAAOC,MAAM,MAAMC,MAAM,MAAM,8BACrDpB,EAAAA,EAAAA,IAMkBzB,EAAA,CAND2C,KAAK,YAAYC,MAAM,KAAKC,MAAM,ODkP5C,CCjPMhB,SAAOC,EAAAA,EAAAA,IAChB,EADoBkB,SAAG,EACvBvB,EAAAA,EAAAA,IAESgI,EAAA,CAFA/H,KAAM8G,EAAoBxF,EAAIyF,WAAYzH,KAAK,SDqPjD,CACDa,SAASC,EAAAA,EAAAA,ICrPb,IAAmB,EDsPbC,EAAAA,EAAAA,KAAiBQ,EAAAA,EAAAA,ICtPpBS,EAAIyF,WAAS,KDwPZzG,EAAG,GACF,KAAM,CAAC,WAEZA,EAAG,KCvPTP,EAAAA,EAAAA,IAgBkBzB,EAAA,CAhBD2C,KAAK,QAAQC,MAAM,MAAM,YAAU,OD6P7C,CC5PMf,SAAOC,EAAAA,EAAAA,IAChB,EADoBkB,SAAG,EACvBvC,EAAAA,EAAAA,IAYM,MAZN5B,EAYM,EAXJ4B,EAAAA,EAAAA,IAEO,QAFDjC,MAAM,mBAAoByF,MAAO8E,EAAY/F,EAAIc,SDgQhDvB,EAAAA,EAAAA,IC/PFwG,EAAY/F,EAAIc,QAAK,EAAA/E,IAE1B0C,EAAAA,EAAAA,IAOY5B,EAAA,CANV6B,KAAK,OACLV,KAAK,QACJW,QAAKZ,GAAEkI,EAAejG,GACvBxE,MAAM,eD+PD,CACDqD,SAASC,EAAAA,EAAAA,IC/Pd,IAEDxC,EAAA,KAAAA,EAAA,KD8PQyC,EAAAA,EAAAA,IChQP,WDkQKC,EAAG,EACHC,GAAI,CAAC,IACJ,KAAM,CAAC,gBAGdD,EAAG,MAGPA,EAAG,GACF,EAAG,CAAC,cCjPbP,EAAAA,EAAAA,IAKE4I,EAAA,CDgPExJ,WCpPO4F,EAAA3C,MDqPP,sBAAuBxE,EAAO,KAAOA,EAAO,GAAMyB,GCrP3C0F,EAAuB3C,MAAA/C,GAC/B+C,MAAO4C,EAAA5C,MACPE,UAAU,EACXC,MAAM,ODsPH,KAAM,EAAG,CAAC,aAAc,YAE7BjC,EAAG,GACF,EAAG,CAAC,aAAc,UAEvB,IE3VA,MAAM,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,QCNA,MAAMzD,EAAa,CCDZC,MAAM,sBDEPC,EAAa,CCDVD,MAAM,kBDETE,EAAa,CCARF,MAAM,kBDCXG,EAAa,CCKVH,MAAM,oBDJTI,EAAa,CAAC,SACdC,EAAa,CCcCL,MAAM,QDbpBO,EAAa,CC0BFP,MAAM,kBDxBjB,SAAUY,EAAOC,EAAUC,EAAYC,EAAYC,EAAYC,EAAWC,GAC9E,MAAMG,GAAuBD,EAAAA,EAAAA,IAAkB,aACzC6J,GAAoB7J,EAAAA,EAAAA,IAAkB,UACtCI,GAA6BJ,EAAAA,EAAAA,IAAkB,mBAC/CO,GAAsBP,EAAAA,EAAAA,IAAkB,YAE9C,OAAQW,EAAAA,EAAAA,OCfRC,EAAAA,EAAAA,IAwEM,MAxENjC,EAwEM,EAvEJkC,EAAAA,EAAAA,IAMM,MANNhC,EAMM,CDUJa,EAAO,KAAOA,EAAO,ICfrBmB,EAAAA,EAAAA,IAA8B,UAA1B,yBAAqB,KACzBA,EAAAA,EAAAA,IAGM,MAHN/B,EAGM,EAFJ+C,EAAAA,EAAAA,IAAgF5B,EAAA,CAArEmB,KAAK,QAAQU,KAAK,UAAWC,QAAOtC,EAAAiL,iBDmB5C,CACDzI,SAASC,EAAAA,EAAAA,ICpBqD,IAAIxC,EAAA,KAAAA,EAAA,KDqBhEyC,EAAAA,EAAAA,ICrB4D,WDuB9DC,EAAG,EACHC,GAAI,CAAC,IACJ,EAAG,CAAC,YCxBgE5C,EAAAkL,UAAU5J,OAAS,ID0BrFJ,EAAAA,EAAAA,OC1BLK,EAAAA,EAAAA,IAA2Gf,EAAA,CD2BrGf,IAAK,EC3BAkC,KAAK,QAAQU,KAAK,SAAUC,QAAOtC,EAAAmL,mBD+BvC,CACD3I,SAASC,EAAAA,EAAAA,IChC8E,IAAExC,EAAA,KAAAA,EAAA,KDiCvFyC,EAAAA,EAAAA,ICjCqF,SDmCvFC,EAAG,EACHC,GAAI,CAAC,IACJ,EAAG,CAAC,cACPT,EAAAA,EAAAA,IAAoB,IAAI,QClChCf,EAAAA,EAAAA,IA8DM,MA9DN9B,EA8DM,EA7DJ8C,EAAAA,EAAAA,IA4DWtB,EAAA,CA5DAsC,KAAMpD,EAAAkL,UAAW7H,OAAA,GAAOtB,MAAA,eAAoBJ,KAAK,SD0CzD,CACDa,SAASC,EAAAA,EAAAA,IC1CT,IAIkB,EAJlBL,EAAAA,EAAAA,IAIkBzB,EAAA,CAJD4C,MAAM,KAAKC,MAAM,MD8C7B,CC7CQhB,SAAOC,EAAAA,EAAAA,IACmGgB,GAD5F,EACvBrB,EAAAA,EAAAA,IAAmHgI,EAAA,CAA1G/H,KAAMrC,EAAAoL,mBAAmB3H,EAAME,IAAIuE,UAAWvG,KAAK,SDiDvD,CACDa,SAASC,EAAAA,EAAAA,IClDuD,IAAsC,EDmDpGC,EAAAA,EAAAA,KAAiBQ,EAAAA,EAAAA,ICnDgDO,EAAME,IAAIuE,SAASC,eAAW,KDqDjGxF,EAAG,GACF,KAAM,CAAC,WAEZA,EAAG,KCpDPP,EAAAA,EAAAA,IAMkBzB,EAAA,CAND4C,MAAM,MAAM,YAAU,ODyDlC,CCxDQf,SAAOC,EAAAA,EAAAA,IAGVgB,GAHiB,EACvBrC,EAAAA,EAAAA,IAEM,OAFDjC,MAAM,YAAayF,MAAOnB,EAAME,IAAI0H,MD4DpC,EC3DHjK,EAAAA,EAAAA,IAAkD,OAAlD5B,GAAkD0D,EAAAA,EAAAA,IAA5BO,EAAME,IAAIsE,UAAQ,ID6DrC,EAAG1I,KAERoD,EAAG,KC1DPP,EAAAA,EAAAA,IAIkBzB,EAAA,CAJD4C,MAAM,KAAKC,MAAM,OD+D7B,CC9DQhB,SAAOC,EAAAA,EAAAA,IAC8FgB,GADvF,EACvBrB,EAAAA,EAAAA,IAA8GgI,EAAA,CAArG/H,KAAMrC,EAAAsL,iBAAiB7H,EAAME,IAAI4H,QAAS5J,KAAK,SDkEnD,CACDa,SAASC,EAAAA,EAAAA,ICnEmD,IAAqC,EDoE/FC,EAAAA,EAAAA,KAAiBQ,EAAAA,EAAAA,ICpE4ClD,EAAAwL,cAAc/H,EAAME,IAAI4H,SAAM,KDsE7F5I,EAAG,GACF,KAAM,CAAC,WAEZA,EAAG,KCrEPP,EAAAA,EAAAA,IAsCkBzB,EAAA,CAtCD4C,MAAM,KAAKC,MAAM,OD0E7B,CCzEQhB,SAAOC,EAAAA,EAAAA,IAmCVgB,GAnCiB,EACvBrC,EAAAA,EAAAA,IAkCM,MAlCN1B,EAkCM,CAhCI+D,EAAME,IAAI4H,SAAWvL,EAAAyL,iBAAiBC,SAAWjI,EAAME,IAAI4H,SAAWvL,EAAAyL,iBAAiBE,QD0ExFzK,EAAAA,EAAAA,OC3EPK,EAAAA,EAAAA,IAOYf,EAAA,CDqEJf,IAAK,EC1EX4C,KAAK,UACLV,KAAK,QACJW,QAAKZ,GAAE1B,EAAA4L,cAAcnI,EAAME,IAAIkI,KD4EzB,CACDrJ,SAASC,EAAAA,EAAAA,IC5EhB,IAEDxC,EAAA,KAAAA,EAAA,KD2EUyC,EAAAA,EAAAA,IC7ET,aD+EOC,EAAG,EACHC,GAAI,CAAC,IACJ,KAAM,CAAC,cACVT,EAAAA,EAAAA,IAAoB,IAAI,GC7EtBsB,EAAME,IAAI4H,SAAWvL,EAAAyL,iBAAiBK,SD+EvC5K,EAAAA,EAAAA,OChFPK,EAAAA,EAAAA,IAOYf,EAAA,CD0EJf,IAAK,EC/EX4C,KAAK,UACLV,KAAK,QACJW,QAAKZ,GAAE1B,EAAA+L,oBAAoBtI,EAAME,MDiF3B,CACDnB,SAASC,EAAAA,EAAAA,ICjFhB,IAEDxC,EAAA,KAAAA,EAAA,KDgFUyC,EAAAA,EAAAA,IClFT,aDoFOC,EAAG,EACHC,GAAI,CAAC,IACJ,KAAM,CAAC,cACVT,EAAAA,EAAAA,IAAoB,IAAI,ICnF9BC,EAAAA,EAAAA,IAMY5B,EAAA,CALV6B,KAAK,UACLV,KAAK,QACJW,QAAKZ,GAAE1B,EAAAgM,mBAAmBvI,EAAME,IAAI0H,ODqFlC,CACD7I,SAASC,EAAAA,EAAAA,ICrFZ,IAEDxC,EAAA,KAAAA,EAAA,KDoFMyC,EAAAA,EAAAA,ICtFL,cDwFGC,EAAG,EACHC,GAAI,CAAC,IACJ,KAAM,CAAC,aCtFZR,EAAAA,EAAAA,IAMY5B,EAAA,CALV6B,KAAK,SACLV,KAAK,QACJW,QAAKZ,GAAE1B,EAAAiM,eAAexI,EAAME,IAAIkI,KDwF9B,CACDrJ,SAASC,EAAAA,EAAAA,ICxFZ,IAEDxC,EAAA,KAAAA,EAAA,KDuFMyC,EAAAA,EAAAA,ICzFL,WD2FGC,EAAG,EACHC,GAAI,CAAC,IACJ,KAAM,CAAC,gBAGdD,EAAG,MAGPA,EAAG,GACF,EAAG,CAAC,YAGb,C,sBClFA,GAAeuJ,EAAAA,EAAAA,IAAgB,CAC7BtI,KAAM,kBACNwB,MAAO,CAAC,gBACRC,KAAAA,CAAMH,GAAO,KAAEK,IAEb,MAAM2F,GAAYxF,EAAAA,EAAAA,IAAkB,IAG9ByG,EAAgBC,UACpB,IACE,MAAMC,QAAiBC,EAAAA,GAAOC,aAAaC,iBAC3CtB,EAAUzG,MAAQ4H,EAASjJ,I,CAC3B,MAAOqJ,GACPC,QAAQD,MAAM,YAAaA,GAC3B1C,EAAAA,GAAU0C,MAAM,W,GAIdxB,EAAkBmB,UACtB,IACE,MAAMC,QAAiBC,EAAAA,GAAOC,aAAaI,oBACrCC,EAAYP,EAASjJ,KAEvBwJ,GAAaA,EAAUtL,OAAS,UAC5BuL,EAAaD,GACnB7C,EAAAA,GAAUC,QAAQ,QAAQ4C,EAAUtL,kB,CAEtC,MAAOmL,GACPC,QAAQD,MAAM,UAAWA,GACzB1C,EAAAA,GAAU0C,MAAM,S,GAIdI,EAAeT,UACnB,IACE,MAAMC,QAAiBC,EAAAA,GAAOC,aAAaO,eAAe,CAAEF,cACtDG,EAAWV,EAASjJ,KAY1B,OATA2J,EAAShE,QAAQiE,IACf,MAAMC,EAAgB/B,EAAUzG,MAAMyI,UAAUC,GAAKA,EAAEtB,KAAOmB,EAAKnB,IAC/DoB,GAAiB,EACnB/B,EAAUzG,MAAMwI,GAAiBD,EAEjC9B,EAAUzG,MAAM2I,KAAKJ,KAIlBD,C,CACP,MAAON,GAEP,MADAC,QAAQD,MAAM,UAAWA,GACnBA,C,GAIJb,EAAgBQ,UACpB,IAEE,MAAMY,EAAO9B,EAAUzG,MAAM4I,KAAKF,GAAKA,EAAEtB,KAAOyB,GAC5CN,IACFA,EAAKzB,OAASE,EAAAA,GAAiB8B,SAGjC,MAAMlB,QAAiBC,EAAAA,GAAOC,aAAaiB,gBAAgB,CACzDF,WAGIG,EAAcpB,EAASjJ,KAGvBsK,EAAQxC,EAAUzG,MAAMyI,UAAUC,GAAKA,EAAEtB,KAAOyB,GAClDI,GAAS,IACXxC,EAAUzG,MAAMiJ,GAASD,GAG3B1D,EAAAA,GAAUC,QAAQ,eAAeyD,EAAYzG,cAAc1F,QAAU,SAGjEmM,EAAYzG,cAAgByG,EAAYzG,aAAa1F,OAAS,GAChEiE,EAAK,eAAgBkI,EAAaA,EAAYzG,a,CAEhD,MAAOyF,GACPC,QAAQD,MAAM,UAAWA,GACzB1C,EAAAA,GAAU0C,MAAM,UAGhB,MAAMO,EAAO9B,EAAUzG,MAAM4I,KAAKF,GAAKA,EAAEtB,KAAOyB,GAC5CN,IACFA,EAAKzB,OAASE,EAAAA,GAAiBE,M,GAK/BI,EAAuBiB,IACvBA,EAAKhG,cAAgBgG,EAAKhG,aAAa1F,OAAS,EAClDiE,EAAK,eAAgByH,EAAMA,EAAKhG,cAEhC+C,EAAAA,GAAUE,QAAQ,cAIhB+B,EAAqBI,UACzB,UACQE,EAAAA,GAAOqB,SAASC,aAAaC,E,CACnC,MAAOpB,GACPC,QAAQD,MAAM,WAAYA,GAC1B1C,EAAAA,GAAU0C,MAAM,U,GAIdR,EAAiBG,UACrB,UAEQE,EAAAA,GAAOC,aAAauB,iBAAiB,CAAER,WAG7C,MAAMS,EAAY7C,EAAUzG,MAAMyI,UAAUC,GAAKA,EAAEtB,KAAOyB,GACtDS,GAAa,IACf7C,EAAUzG,MAAMuJ,OAAOD,EAAW,GAClChE,EAAAA,GAAUC,QAAQ,W,CAEpB,MAAOyC,GACPC,QAAQD,MAAM,UAAWA,GACzB1C,EAAAA,GAAU0C,MAAM,S,GAIdtB,EAAoBiB,UACxB,UACQ6B,EAAAA,EAAaC,QAAQ,kBAAmB,KAAM,CAClDC,kBAAmB,KACnBC,iBAAkB,KAClB/L,KAAM,kBAIFiK,EAAAA,GAAOC,aAAa8B,mBAG1BnD,EAAUzG,MAAQ,GAClBsF,EAAAA,GAAUC,QAAQ,c,CAClB,MAAOyC,GACO,WAAVA,IACFC,QAAQD,MAAM,UAAWA,GACzB1C,EAAAA,GAAU0C,MAAM,U,GAMhBrB,EAAsBlD,IAC1B,OAAQA,GACN,KAAKoG,EAAAA,GAAeC,MAAO,MAAO,UAClC,KAAKD,EAAAA,GAAeE,KAAM,MAAO,UACjC,KAAKF,EAAAA,GAAeG,IAAK,MAAO,UAChC,QAAS,MAAO,KAIdnD,EAAoBC,IACxB,OAAQA,GACN,KAAKE,EAAAA,GAAiBC,QAAS,MAAO,GACtC,KAAKD,EAAAA,GAAiB8B,QAAS,MAAO,UACtC,KAAK9B,EAAAA,GAAiBK,OAAQ,MAAO,UACrC,KAAKL,EAAAA,GAAiBE,MAAO,MAAO,SACpC,QAAS,MAAO,KAIdH,EAAiBD,IACrB,OAAQA,GACN,KAAKE,EAAAA,GAAiBC,QAAS,MAAO,MACtC,KAAKD,EAAAA,GAAiB8B,QAAS,MAAO,MACtC,KAAK9B,EAAAA,GAAiBK,OAAQ,MAAO,MACrC,KAAKL,EAAAA,GAAiBE,MAAO,MAAO,OACpC,QAAS,OAAOJ,IAKdmD,EAAmBA,KACvBvC,KAQF,OAJAwC,EAAAA,EAAAA,IAAU,KACRxC,MAGK,CACLjB,YACAD,kBACAW,gBACAG,sBACAC,qBACAC,iBACAd,oBACAC,qBACAE,mBACAE,gBACAkD,mBAEAjD,iBAAgBA,EAAAA,GAEpB,IC3RF,MAAM,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAAS,GAAQ,CAAC,YAAY,qBAEzF,QT0FA,GAAeS,EAAAA,EAAAA,IAAgB,CAC7BtI,KAAM,oBACNgL,WAAY,CACVC,iBAAgB,EAChBC,gBAAe,EACf9D,iBAAgB,EAChB+D,SAAQA,EAAAA,UAEV1J,KAAAA,GAEE,MAAM2J,GAAYtJ,EAAAA,EAAAA,IAAmB,IAC/BuJ,GAAavJ,EAAAA,EAAAA,IAAY,YACzBwJ,GAAmBxJ,EAAAA,EAAAA,IAAY,IAC/ByJ,GAAqBzJ,EAAAA,EAAAA,IAAY,IACjCjE,GAAuBiE,EAAAA,EAAAA,IAAc,IACrCmI,GAAWnI,EAAAA,EAAAA,IAAY,IACvB1C,GAAY0C,EAAAA,EAAAA,IAAoB,IAChChC,GAAkBgC,EAAAA,EAAAA,IAA+B,CAAC,GAClD0J,GAAiB1J,EAAAA,EAAAA,IAAY,IAC7BzC,GAAgByC,EAAAA,EAAAA,IAAY,IAG5B2J,GAAU3J,EAAAA,EAAAA,KAAI,GACd3C,GAAa2C,EAAAA,EAAAA,KAAI,GAGjBvB,GAAqBuB,EAAAA,EAAAA,KAAI,GACzBtB,GAAmBsB,EAAAA,EAAAA,IAAuB,MAC1CrB,GAAsBqB,EAAAA,EAAAA,IAAmB,IACzC4J,GAAmB5J,EAAAA,EAAAA,IAA+B,CAAC,GAGnD6J,GAAiB7J,EAAAA,EAAAA,IAAiB,IAAI+B,KAGtCjD,GAA4BkB,EAAAA,EAAAA,KAAI,GAChChB,GAA2BgB,EAAAA,EAAAA,IAAY,IACvC8J,GAA0B9J,EAAAA,EAAAA,IAAY,IACtCb,GAAoBgB,EAAAA,EAAAA,IAAS,IAAM,UAAU2J,EAAwB/K,SAGrEpD,GAAawE,EAAAA,EAAAA,IAAS,KAC1B,MAAM4J,EAAc,IAAIhI,IAAIuH,EAAUvK,MAAMiD,IAAIgI,GAAKA,EAAEC,WACvD,OAAO9H,MAAMC,KAAK2H,GAAanH,OAAOsH,GAAKA,KAGvC/N,GAAkBgE,EAAAA,EAAAA,IAAS,IACxBxE,EAAWoD,MAAMiD,IAAIiI,IAAO,CACjClL,MAAOkL,EACPpM,MAAOoM,EACPE,SAAUb,EAAUvK,MACjB6D,OAAOoH,GAAKA,EAAEC,WAAaA,GAC3BjI,IAAIoI,IAAO,CACVrL,MAAOqL,EAASjE,GAChBtI,MAAOuM,EAASlM,YAKlBmM,GAAoBlK,EAAAA,EAAAA,IAAS,IAC5BqJ,EAAiBzK,MACfuK,EAAUvK,MAAM6D,OAAOoH,GAAKA,EAAEC,WAAaT,EAAiBzK,OAD/B,IAIhCtB,GAAoB0C,EAAAA,EAAAA,IAAS,KACjC,IAAK5C,EAAcwB,MACjB,OAAOzB,EAAUyB,MAEnB,MAAMuL,EAAU/M,EAAcwB,MAAM+D,cACpC,OAAOxF,EAAUyB,MAAM6D,OAAO2H,GAC5BA,EAAErM,KAAK4E,cAAcC,SAASuH,MAI5BE,GAAsBrK,EAAAA,EAAAA,IAAS,IAAM0J,EAAe9K,MAAM9C,MAE1DwO,GAAoBtK,EAAAA,EAAAA,IAAS,IAAM0J,EAAe9K,MAAM9C,KAAO,GAG/DyO,EAAgBhE,UACpB,IACE,MAAMC,QAAiBC,EAAAA,GAAOC,aAAa8D,eAC3CrB,EAAUvK,MAAQ4H,EAASjJ,KAGvB/B,EAAWoD,MAAMnD,OAAS,IAC5B4N,EAAiBzK,MAAQpD,EAAWoD,MAAM,GAC1C6L,I,CAEF,MAAO7D,GACPC,QAAQD,MAAM,UAAWA,E,GAIvB8D,EAAqBA,KAEzBvN,EAAUyB,MAAQ,GAClBf,EAAgBe,MAAQ,CAAC,EACzB2K,EAAe3K,MAAQ,GACvB8K,EAAe9K,MAAM+L,SAGjBF,EAAmBA,KACnBP,EAAkBtL,MAAMnD,OAAS,EACnC6N,EAAmB1K,MAAQsL,EAAkBtL,MAAM,GAAGoH,GAEtDsD,EAAmB1K,MAAQ,IAIzBlC,EAAkB6J,UACtB,IACE,MAAMC,QAAiBC,EAAAA,GAAOC,aAAakE,aAC3C,IAAKpE,EAASjJ,KACZ,OAIF+L,EAAmB1K,MAAQ,GAC3ByK,EAAiBzK,MAAQ,GAEzBoJ,EAASpJ,MAAQ4H,EAASjJ,WAEpBsN,G,CACN,MAAOjE,GACPC,QAAQD,MAAM,UAAWA,E,GAIvBiE,EAAoBtE,UACxB,GAAKyB,EAASpJ,MAAd,CAEA4K,EAAQ5K,OAAQ,EAChB,IACE,MAAMkM,EAAoC,CACxC1B,WAAY,OACZpB,SAAUA,EAASpJ,OAGf4H,QAAiBC,EAAAA,GAAOC,aAAaqE,UAAUD,GAC/CE,EAASxE,EAASjJ,KAExBJ,EAAUyB,MAAQoM,EAAO7N,UACzBoM,EAAe3K,MAAQoM,EAAOzB,eAG9B1L,EAAgBe,MAAQ,CAAC,EACzBoM,EAAO7N,UAAU+F,QAAQ+H,IACvBpN,EAAgBe,MAAMqM,EAASlN,MAAQkN,EAASrM,OAAS,KAG3D8K,EAAe9K,MAAM+L,QACrBzG,EAAAA,GAAUC,QAAQ,QAAQ6G,EAAO7N,UAAU1B,a,CAC3C,MAAOmL,GACPC,QAAQD,MAAM,UAAWA,GACzB1C,EAAAA,GAAU0C,MAAM,S,CAClB,QACE4C,EAAQ5K,OAAQ,C,CA3BS,GA+BvBxC,EAAwBwC,IACxBA,GAA0B,IAAjBA,EAAMnD,SACjB4N,EAAiBzK,MAAQA,EAAM,GAC/B0K,EAAmB1K,MAAQA,EAAM,GACjCsM,MAIEC,EAA0BrB,GACvBX,EAAUvK,MAAM6D,OAAOoH,GAAKA,EAAEC,WAAaA,GAG9CoB,EAAuB3E,UAC3B,GAAK+C,EAAmB1K,MAAxB,CAEA4K,EAAQ5K,OAAQ,EAChB,IACE,MAAMkM,EAAoC,CACxC1B,WAAY,WACZgC,WAAY9B,EAAmB1K,MAC/BoJ,SAAU,IAGNxB,QAAiBC,EAAAA,GAAOC,aAAaqE,UAAUD,GAC/CE,EAASxE,EAASjJ,KAExBJ,EAAUyB,MAAQoM,EAAO7N,UACzBoM,EAAe3K,MAAQoM,EAAOzB,eAG9B1L,EAAgBe,MAAQ,CAAC,EACzBoM,EAAO7N,UAAU+F,QAAQ+H,IACvBpN,EAAgBe,MAAMqM,EAASlN,MAAQkN,EAASrM,OAAS,KAG3D8K,EAAe9K,MAAM+L,QACrBzG,EAAAA,GAAUC,QAAQ,UAAU6G,EAAO7N,UAAU1B,a,CAC7C,MAAOmL,GACPC,QAAQD,MAAM,UAAWA,GACzB1C,EAAAA,GAAU0C,MAAM,S,CAClB,QACE4C,EAAQ5K,OAAQ,C,CA5BmB,GAgCjCZ,EAAqBqN,IACzB3B,EAAe9K,MAAM0M,IAAID,IAGrBrO,EAAgBuJ,UACpBrJ,EAAW0B,OAAQ,EACnB,IACE,MAAMkM,EAA+B,CACnC1B,WAAYA,EAAWxK,MACvBwM,WAAiC,aAArBhC,EAAWxK,MAAuB0K,EAAmB1K,WAAQqB,EACzE+H,SAA+B,SAArBoB,EAAWxK,MAAmBoJ,EAASpJ,MAAQ,GACzDf,gBAAiBA,EAAgBe,OAG7B4H,QAAiBC,EAAAA,GAAOC,aAAa6E,YAAYT,GACjDE,EAASxE,EAASjJ,KAExB2G,EAAAA,GAAUC,QAAQ,WAClBuF,EAAe9K,MAAM+L,cAEflE,EAAAA,GAAOqB,SAASC,aAAaiD,EAAOQ,e,CAE1C,MAAO5E,GACPC,QAAQD,MAAM,UAAWA,GACzB1C,EAAAA,GAAU0C,MAAM,S,CAClB,QACE1J,EAAW0B,OAAQ,C,GAKjBP,EAAoBA,CAAC8I,EAAkB3E,KAC3CjE,EAAiBK,MAAQuI,EACzB3I,EAAoBI,MAAQ4D,EAC5BlE,EAAmBM,OAAQ,GAGvBF,EAAqB8D,IACzBA,EAAOU,QAAQc,IAEb,MAAMiH,EAAW9N,EAAUyB,MAAM4I,KAAK4C,GAAKA,EAAErM,KAAK4E,gBAAkBqB,EAAMjG,KAAK4E,eAC3EsI,IAEFpN,EAAgBe,MAAMqM,EAASlN,MAAQuC,OAAO0D,EAAMpF,OAEpD6K,EAAiB7K,MAAMqM,EAASlN,MAAQiG,EAAMyH,OAE9C/B,EAAe9K,MAAM0M,IAAIL,EAASlN,SAMtCmG,EAAAA,GAAUC,QAAQ,QAAQ3B,EAAO/G,eAG7B0C,EAAsBkN,GACnB5B,EAAiB7K,MAAMyM,GAI1BpN,EAAsBgN,IAC1B,MAAMrM,EAAQf,EAAgBe,MAAMqM,EAASlN,MAC7C,IAAKa,EAAO,OAAO,EAGnB,MAAM8M,EAAe9M,EAAM+M,OAC3B,GAAID,EAAaE,WAAW,MAAQF,EAAa9I,SAAS,KACxD,OAAO,EAIT,GAAI8I,EAAaE,WAAW,MAAQF,EAAaE,WAAW,KAC1D,IAEE,OADAzL,KAAKC,MAAMsL,IACJ,C,CACP,MAEA,OAAOA,EAAa9I,SAAS,MAAQ8I,EAAa9I,SAAS,I,CAK/D,MAAMpG,EAAOyO,EAASzO,KAAKmG,cAC3B,OAAOnG,EAAKoG,SAAS,WAAapG,EAAKoG,SAAS,UAAYpG,EAAKoG,SAAS,OAItE1E,EAAwB+M,IAC5BtB,EAAwB/K,MAAQqM,EAASlN,KACzCc,EAAyBD,MAAQf,EAAgBe,MAAMqM,EAASlN,OAAS,GACzEY,EAA0BC,OAAQ,GAI9BM,EAA6BN,IAC7B+K,EAAwB/K,QAC1Bf,EAAgBe,MAAM+K,EAAwB/K,OAASA,EACvDZ,EAAkB2L,EAAwB/K,SAIxCiN,EAAerG,GACdA,GACEA,EAAKsG,MAAM,SAASC,OADT,GAIdxG,EAAsBlD,IAC1B,OAAQA,GACN,KAAKoG,EAAAA,GAAeC,MAAO,MAAO,UAClC,KAAKD,EAAAA,GAAeE,KAAM,MAAO,UACjC,KAAKF,EAAAA,GAAeG,IAAK,MAAO,UAChC,QAAS,MAAO,KAIdoD,EAAyB7E,IAE7B,IAAI8E,EAAQ,EAMZ,OALArI,OAAOsI,KAAKzC,EAAiB7K,OAAOsE,QAAQmI,IACtC5B,EAAiB7K,MAAMyM,KAAelE,EAAK/E,UAC7C6J,MAGGA,GAaT,OAJAnD,EAAAA,EAAAA,IAAUvC,gBACFgE,MAGD,CACLpB,YACAC,aACAC,mBACAC,qBACA1N,uBACAoM,WACA7K,YACAU,kBACA0L,iBACAnM,gBACAoM,UACAtM,aACA1B,aACAQ,kBACAkO,oBACA5M,oBACA+M,sBACAC,oBACAI,qBACAD,mBACA/N,kBACAN,uBACA+O,yBACAD,uBACAlN,oBACAhB,gBACA6O,cACAtG,qBACAyG,wBAEA1N,qBACAC,mBACAC,sBACAH,oBACAK,oBACAP,qBAEAF,qBACAC,uBACAgB,4BACAP,4BACAE,2BACAG,oBAEAkK,SAAQ,WACRtD,iBAAgBA,EAAAA,GAEpB,IUneF,MAAM,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAAS1L,GAAQ,CAAC,YAAY,qBAEzF,O", "sources": ["webpack://tab-kit-web/./src/views/ParameterToolView.vue?331e", "webpack://tab-kit-web/./src/views/ParameterToolView.vue", "webpack://tab-kit-web/./src/components/ParamValueViewer.vue?2066", "webpack://tab-kit-web/./src/components/ParamValueViewer.vue", "webpack://tab-kit-web/./src/components/ParamValueViewer.vue?ab66", "webpack://tab-kit-web/./src/components/ParamParseDialog.vue?64b6", "webpack://tab-kit-web/./src/components/ParamParseDialog.vue", "webpack://tab-kit-web/./src/components/ParamParseDialog.vue?7eb6", "webpack://tab-kit-web/./src/components/DataFileManager.vue?eea6", "webpack://tab-kit-web/./src/components/DataFileManager.vue", "webpack://tab-kit-web/./src/components/DataFileManager.vue?b969", "webpack://tab-kit-web/./src/views/ParameterToolView.vue?3f0a"], "sourcesContent": ["import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, createTextVNode as _createTextVNode, withCtx as _withCtx, createVNode as _createVNode, toDisplayString as _toDisplayString, createElementBlock as _createElementBlock } from \"vue\"\n\nconst _hoisted_1 = { class: \"parameter-tool\" }\nconst _hoisted_2 = { class: \"main-content\" }\nconst _hoisted_3 = { class: \"cin-parameters-section\" }\nconst _hoisted_4 = { class: \"cin-operations-content\" }\nconst _hoisted_5 = { class: \"operation-row\" }\nconst _hoisted_6 = {\n  key: 0,\n  class: \"search-bar\"\n}\nconst _hoisted_7 = { class: \"filter-info\" }\nconst _hoisted_8 = { class: \"parameters-table\" }\nconst _hoisted_9 = { class: \"param-value-cell\" }\nconst _hoisted_10 = {\n  key: 0,\n  class: \"parameter-source\"\n}\nconst _hoisted_11 = {\n  key: 1,\n  class: \"no-source\"\n}\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  const _component_el_cascader = _resolveComponent(\"el-cascader\")!\n  const _component_el_button = _resolveComponent(\"el-button\")!\n  const _component_el_divider = _resolveComponent(\"el-divider\")!\n  const _component_el_input = _resolveComponent(\"el-input\")!\n  const _component_el_table_column = _resolveComponent(\"el-table-column\")!\n  const _component_document = _resolveComponent(\"document\")!\n  const _component_el_icon = _resolveComponent(\"el-icon\")!\n  const _component_el_table = _resolveComponent(\"el-table\")!\n  const _component_DataFileManager = _resolveComponent(\"DataFileManager\")!\n  const _component_ParamParseDialog = _resolveComponent(\"ParamParseDialog\")!\n  const _component_ParamValueViewer = _resolveComponent(\"ParamValueViewer\")!\n\n  return (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _createElementVNode(\"div\", _hoisted_2, [\n      _createElementVNode(\"div\", _hoisted_3, [\n        _cache[7] || (_cache[7] = _createElementVNode(\"div\", { class: \"section-header\" }, [\n          _createElementVNode(\"h4\", null, \"CIN 参数列表\"),\n          _createElementVNode(\"div\", { class: \"header-actions\" })\n        ], -1)),\n        _createElementVNode(\"div\", _hoisted_4, [\n          _createElementVNode(\"div\", _hoisted_5, [\n            (_ctx.categories.length > 0)\n              ? (_openBlock(), _createBlock(_component_el_cascader, {\n                  key: 0,\n                  modelValue: _ctx.selectedTemplatePath,\n                  \"onUpdate:modelValue\": _cache[0] || (_cache[0] = ($event: any) => ((_ctx.selectedTemplatePath) = $event)),\n                  size: \"small\",\n                  options: _ctx.cascaderOptions,\n                  placeholder: \"选择 CIN 模板\",\n                  style: {\"width\":\"260px\",\"margin-right\":\"16px\"},\n                  onChange: _ctx.handleTemplateChange,\n                  clearable: \"\"\n                }, null, 8, [\"modelValue\", \"options\", \"onChange\"]))\n              : _createCommentVNode(\"\", true),\n            _createVNode(_component_el_button, {\n              type: \"primary\",\n              size: \"small\",\n              onClick: _ctx.selectLocalFile\n            }, {\n              default: _withCtx(() => _cache[4] || (_cache[4] = [\n                _createTextVNode(\"选择本地 CIN 文件\")\n              ])),\n              _: 1,\n              __: [4]\n            }, 8, [\"onClick\"]),\n            _createVNode(_component_el_button, {\n              type: \"primary\",\n              size: \"small\",\n              onClick: _ctx.exportCinFile,\n              style: {\"margin-left\":\"auto\"},\n              loading: _ctx.processing\n            }, {\n              default: _withCtx(() => _cache[5] || (_cache[5] = [\n                _createTextVNode(\"导出 CIN\")\n              ])),\n              _: 1,\n              __: [5]\n            }, 8, [\"onClick\", \"loading\"])\n          ])\n        ]),\n        _createVNode(_component_el_divider, { style: {\"margin\":\"8px 0\"} }),\n        (_ctx.variables.length > 0)\n          ? (_openBlock(), _createElementBlock(\"div\", _hoisted_6, [\n              _createVNode(_component_el_input, {\n                modelValue: _ctx.searchKeyword,\n                \"onUpdate:modelValue\": _cache[1] || (_cache[1] = ($event: any) => ((_ctx.searchKeyword) = $event)),\n                size: \"small\",\n                placeholder: \"搜索参数名称...\",\n                clearable: \"\",\n                style: {\"width\":\"260px\"}\n              }, null, 8, [\"modelValue\"]),\n              _createElementVNode(\"div\", _hoisted_7, [\n                _createElementVNode(\"span\", null, \"共 \" + _toDisplayString(_ctx.filteredVariables.length) + \" / \" + _toDisplayString(_ctx.variables.length) + \" 个参数\", 1)\n              ])\n            ]))\n          : _createCommentVNode(\"\", true),\n        _createElementVNode(\"div\", _hoisted_8, [\n          _createVNode(_component_el_table, {\n            data: _ctx.filteredVariables,\n            border: \"\",\n            style: {\"width\":\"100%\",\"height\":\"100%\"},\n            size: \"small\"\n          }, {\n            default: _withCtx(() => [\n              _createVNode(_component_el_table_column, {\n                prop: \"name\",\n                label: \"参数名\",\n                width: \"200\",\n                \"show-overflow-tooltip\": \"\"\n              }),\n              _createVNode(_component_el_table_column, {\n                prop: \"type\",\n                label: \"类型\",\n                width: \"120\",\n                \"show-overflow-tooltip\": \"\"\n              }),\n              _createVNode(_component_el_table_column, {\n                label: \"参数值\",\n                \"min-width\": \"200\"\n              }, {\n                default: _withCtx((scope) => [\n                  _createElementVNode(\"div\", _hoisted_9, [\n                    _createVNode(_component_el_input, {\n                      modelValue: _ctx.parameterValues[scope.row.name],\n                      \"onUpdate:modelValue\": ($event: any) => ((_ctx.parameterValues[scope.row.name]) = $event),\n                      placeholder: \"输入参数值\",\n                      size: \"small\",\n                      onChange: ($event: any) => (_ctx.onParameterChange(scope.row.name)),\n                      class: \"param-value-input\"\n                    }, null, 8, [\"modelValue\", \"onUpdate:modelValue\", \"onChange\"]),\n                    (_ctx.isComplexParameter(scope.row))\n                      ? (_openBlock(), _createBlock(_component_el_button, {\n                          key: 0,\n                          type: \"text\",\n                          size: \"small\",\n                          onClick: ($event: any) => (_ctx.editComplexParameter(scope.row)),\n                          class: \"edit-button\"\n                        }, {\n                          default: _withCtx(() => _cache[6] || (_cache[6] = [\n                            _createTextVNode(\" 编辑 \")\n                          ])),\n                          _: 2,\n                          __: [6]\n                        }, 1032, [\"onClick\"]))\n                      : _createCommentVNode(\"\", true)\n                  ])\n                ]),\n                _: 1\n              }),\n              _createVNode(_component_el_table_column, {\n                label: \"参数值来源\",\n                width: \"150\",\n                \"show-overflow-tooltip\": \"\"\n              }, {\n                default: _withCtx((scope) => [\n                  (_ctx.getParameterSource(scope.row.name))\n                    ? (_openBlock(), _createElementBlock(\"div\", _hoisted_10, [\n                        _createVNode(_component_el_icon, null, {\n                          default: _withCtx(() => [\n                            _createVNode(_component_document)\n                          ]),\n                          _: 1\n                        }),\n                        _createElementVNode(\"span\", null, _toDisplayString(_ctx.getParameterSource(scope.row.name)), 1)\n                      ]))\n                    : (_openBlock(), _createElementBlock(\"span\", _hoisted_11, \"CIN 文件\"))\n                ]),\n                _: 1\n              })\n            ]),\n            _: 1\n          }, 8, [\"data\"])\n        ])\n      ]),\n      _createVNode(_component_DataFileManager, { onParseResult: _ctx.handleParseResult }, null, 8, [\"onParseResult\"])\n    ]),\n    _createVNode(_component_ParamParseDialog, {\n      modelValue: _ctx.parseDialogVisible,\n      \"onUpdate:modelValue\": _cache[2] || (_cache[2] = ($event: any) => ((_ctx.parseDialogVisible) = $event)),\n      \"source-file\": _ctx.currentParseFile,\n      \"parsed-params\": _ctx.currentParsedParams,\n      onApplyParams: _ctx.applyParsedParams\n    }, null, 8, [\"modelValue\", \"source-file\", \"parsed-params\", \"onApplyParams\"]),\n    _createVNode(_component_ParamValueViewer, {\n      modelValue: _ctx.complexParamDialogVisible,\n      \"onUpdate:modelValue\": _cache[3] || (_cache[3] = ($event: any) => ((_ctx.complexParamDialogVisible) = $event)),\n      value: _ctx.currentComplexParamValue,\n      readonly: false,\n      title: _ctx.complexParamTitle,\n      onConfirm: _ctx.handleComplexParamConfirm\n    }, null, 8, [\"modelValue\", \"value\", \"title\", \"onConfirm\"])\n  ]))\n}", "<template>\n  <div class=\"parameter-tool\">\n    <!-- 参数编辑主区域 -->\n    <div class=\"main-content\">\n      <!-- CIN 参数列表卡片 -->\n      <div class=\"cin-parameters-section\">\n        <div class=\"section-header\">\n          <h4>CIN 参数列表</h4>\n          <div class=\"header-actions\">\n          </div>\n        </div>\n\n        <div class=\"cin-operations-content\">\n          <div class=\"operation-row\">\n            <el-cascader v-model=\"selectedTemplatePath\" size=\"small\" :options=\"cascaderOptions\"\n              v-if=\"categories.length > 0\" placeholder=\"选择 CIN 模板\" style=\"width: 260px; margin-right: 16px;\"\n              @change=\"handleTemplateChange\" clearable />\n\n            <el-button type=\"primary\" size=\"small\" @click=\"selectLocalFile\">选择本地 CIN 文件</el-button>\n            <el-button type=\"primary\" size=\"small\" @click=\"exportCinFile\" style=\"margin-left: auto;\"\n              :loading=\"processing\">导出 CIN</el-button>\n          </div>\n        </div>\n\n        <el-divider style=\"margin: 8px 0\" />\n\n        <!-- 搜索和过滤 -->\n        <div class=\"search-bar\" v-if=\"variables.length > 0\">\n          <el-input v-model=\"searchKeyword\" size=\"small\" placeholder=\"搜索参数名称...\" clearable style=\"width: 260px;\" />\n          <div class=\"filter-info\">\n            <span>共 {{ filteredVariables.length }} / {{ variables.length }} 个参数</span>\n          </div>\n        </div>\n\n        <!-- 参数表格 -->\n        <div class=\"parameters-table\">\n          <el-table :data=\"filteredVariables\" border style=\"width: 100%; height: 100%;\" size=\"small\">\n            <el-table-column prop=\"name\" label=\"参数名\" width=\"200\" show-overflow-tooltip />\n            <el-table-column prop=\"type\" label=\"类型\" width=\"120\" show-overflow-tooltip />\n            <el-table-column label=\"参数值\" min-width=\"200\">\n              <template #default=\"scope\">\n                <div class=\"param-value-cell\">\n                  <el-input v-model=\"parameterValues[scope.row.name]\" placeholder=\"输入参数值\" size=\"small\"\n                    @change=\"onParameterChange(scope.row.name)\" class=\"param-value-input\" />\n                  <el-button v-if=\"isComplexParameter(scope.row)\" type=\"text\" size=\"small\"\n                    @click=\"editComplexParameter(scope.row)\" class=\"edit-button\">\n                    编辑\n                  </el-button>\n                </div>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"参数值来源\" width=\"150\" show-overflow-tooltip>\n              <template #default=\"scope\">\n                <div v-if=\"getParameterSource(scope.row.name)\" class=\"parameter-source\">\n                  <el-icon>\n                    <document />\n                  </el-icon>\n                  <span>{{ getParameterSource(scope.row.name) }}</span>\n                </div>\n                <span v-else class=\"no-source\">CIN 文件</span>\n              </template>\n            </el-table-column>\n          </el-table>\n        </div>\n      </div>\n\n      <!-- 数据文件管理 -->\n      <DataFileManager @parse-result=\"handleParseResult\" />\n    </div>\n\n    <!-- 参数导入结果弹窗 -->\n    <ParamParseDialog v-model=\"parseDialogVisible\" :source-file=\"currentParseFile\" :parsed-params=\"currentParsedParams\"\n      @apply-params=\"applyParsedParams\" />\n\n    <!-- 复杂参数编辑弹窗 -->\n    <ParamValueViewer v-model=\"complexParamDialogVisible\" :value=\"currentComplexParamValue\" :readonly=\"false\"\n      :title=\"complexParamTitle\" @confirm=\"handleComplexParamConfirm\" />\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { defineComponent, ref, computed, onMounted } from \"vue\";\nimport {\n  appApi,\n  CinTemplate,\n  CaplVariable,\n  CinParameterParseRequest,\n  CinParameterRequest,\n  SourceFile,\n  SourceFileType,\n  SourceFileStatus,\n  ParsedParam\n} from \"@/api/appApi\";\nimport { ElMessage } from \"element-plus\";\nimport { Document } from '@element-plus/icons-vue';\nimport ParamParseDialog from \"@/components/ParamParseDialog.vue\";\nimport DataFileManager from \"@/components/DataFileManager.vue\";\nimport ParamValueViewer from \"@/components/ParamValueViewer.vue\";\n\nexport default defineComponent({\n  name: \"ParameterToolView\",\n  components: {\n    ParamParseDialog,\n    DataFileManager,\n    ParamValueViewer,\n    Document\n  },\n  setup() {\n    // 数据\n    const templates = ref<CinTemplate[]>([]);\n    const sourceType = ref<string>(\"template\");\n    const selectedCategory = ref<string>(\"\");\n    const selectedTemplateId = ref<string>(\"\");\n    const selectedTemplatePath = ref<string[]>([]);\n    const filePath = ref<string>(\"\");\n    const variables = ref<CaplVariable[]>([]);\n    const parameterValues = ref<{ [key: string]: string }>({});\n    const sourceFilePath = ref<string>(\"\");\n    const searchKeyword = ref<string>(\"\");\n\n    // 状态\n    const parsing = ref(false);\n    const processing = ref(false);\n\n    // 源文件管理相关\n    const parseDialogVisible = ref(false);\n    const currentParseFile = ref<SourceFile | null>(null);\n    const currentParsedParams = ref<ParsedParam[]>([]);\n    const parameterSources = ref<{ [key: string]: string }>({});\n\n    // 新增状态\n    const modifiedParams = ref<Set<string>>(new Set());\n\n    // 复杂参数编辑相关\n    const complexParamDialogVisible = ref(false);\n    const currentComplexParamValue = ref<string>('');\n    const currentComplexParamName = ref<string>('');\n    const complexParamTitle = computed(() => `编辑参数 - ${currentComplexParamName.value}`);\n\n    // 计算属性\n    const categories = computed(() => {\n      const categorySet = new Set(templates.value.map(t => t.category));\n      return Array.from(categorySet).filter(c => c);\n    });\n\n    const cascaderOptions = computed(() => {\n      return categories.value.map(category => ({\n        value: category,\n        label: category,\n        children: templates.value\n          .filter(t => t.category === category)\n          .map(template => ({\n            value: template.id,\n            label: template.name\n          }))\n      }));\n    });\n\n    const filteredTemplates = computed(() => {\n      if (!selectedCategory.value) return [];\n      return templates.value.filter(t => t.category === selectedCategory.value);\n    });\n\n    const filteredVariables = computed(() => {\n      if (!searchKeyword.value) {\n        return variables.value;\n      }\n      const keyword = searchKeyword.value.toLowerCase();\n      return variables.value.filter(v =>\n        v.name.toLowerCase().includes(keyword)\n      );\n    });\n\n    const modifiedParamsCount = computed(() => modifiedParams.value.size);\n\n    const hasUnsavedChanges = computed(() => modifiedParams.value.size > 0);\n\n    // 方法\n    const loadTemplates = async () => {\n      try {\n        const response = await appApi.cinParameter.getTemplates();\n        templates.value = response.data;\n\n        // 自动选择第一个类别和模板\n        if (categories.value.length > 0) {\n          selectedCategory.value = categories.value[0];\n          onCategoryChange();\n        }\n      } catch (error) {\n        console.error('加载模板失败:', error);\n      }\n    };\n\n    const onSourceTypeChange = () => {\n      // 清空之前的数据\n      variables.value = [];\n      parameterValues.value = {};\n      sourceFilePath.value = \"\";\n      modifiedParams.value.clear();\n    };\n\n    const onCategoryChange = () => {\n      if (filteredTemplates.value.length > 0) {\n        selectedTemplateId.value = filteredTemplates.value[0].id;\n      } else {\n        selectedTemplateId.value = \"\";\n      }\n    };\n\n    const selectLocalFile = async () => {\n      try {\n        const response = await appApi.cinParameter.selectFile();\n        if (!response.data) {\n          return;\n        }\n\n        // 清空之前的选择\n        selectedTemplateId.value = '';\n        selectedCategory.value = '';\n\n        filePath.value = response.data;\n        // 自动解析选择的文件\n        await parseSelectedFile();\n      } catch (error) {\n        console.error('选择文件失败:', error);\n      }\n    };\n\n    const parseSelectedFile = async () => {\n      if (!filePath.value) return;\n\n      parsing.value = true;\n      try {\n        const request: CinParameterParseRequest = {\n          sourceType: \"file\",\n          filePath: filePath.value\n        };\n\n        const response = await appApi.cinParameter.parseFile(request);\n        const result = response.data;\n\n        variables.value = result.variables;\n        sourceFilePath.value = result.sourceFilePath;\n\n        // 初始化参数值\n        parameterValues.value = {};\n        result.variables.forEach(variable => {\n          parameterValues.value[variable.name] = variable.value || \"\";\n        });\n\n        modifiedParams.value.clear();\n        ElMessage.success(`成功解析 ${result.variables.length} 个参数`);\n      } catch (error) {\n        console.error('解析文件失败:', error);\n        ElMessage.error('解析文件失败');\n      } finally {\n        parsing.value = false;\n      }\n    };\n\n    const handleTemplateChange = (value: string[]) => {\n      if (value && value.length === 2) {\n        selectedCategory.value = value[0];\n        selectedTemplateId.value = value[1];\n        loadSelectedTemplate();\n      }\n    };\n\n    const getTemplatesByCategory = (category: string) => {\n      return templates.value.filter(t => t.category === category);\n    };\n\n    const loadSelectedTemplate = async () => {\n      if (!selectedTemplateId.value) return;\n\n      parsing.value = true;\n      try {\n        const request: CinParameterParseRequest = {\n          sourceType: \"template\",\n          templateId: selectedTemplateId.value,\n          filePath: \"\"\n        };\n\n        const response = await appApi.cinParameter.parseFile(request);\n        const result = response.data;\n\n        variables.value = result.variables;\n        sourceFilePath.value = result.sourceFilePath;\n\n        // 初始化参数值\n        parameterValues.value = {};\n        result.variables.forEach(variable => {\n          parameterValues.value[variable.name] = variable.value || \"\";\n        });\n\n        modifiedParams.value.clear();\n        ElMessage.success(`成功加载模板 ${result.variables.length} 个参数`);\n      } catch (error) {\n        console.error('加载模板失败:', error);\n        ElMessage.error('加载模板失败');\n      } finally {\n        parsing.value = false;\n      }\n    };\n\n    const onParameterChange = (paramName: string) => {\n      modifiedParams.value.add(paramName);\n    };\n\n    const exportCinFile = async () => {\n      processing.value = true;\n      try {\n        const request: CinParameterRequest = {\n          sourceType: sourceType.value,\n          templateId: sourceType.value === \"template\" ? selectedTemplateId.value : undefined,\n          filePath: sourceType.value === \"file\" ? filePath.value : \"\",\n          parameterValues: parameterValues.value\n        };\n\n        const response = await appApi.cinParameter.processFile(request);\n        const result = response.data;\n\n        ElMessage.success(`文件导出成功！`);\n        modifiedParams.value.clear();\n\n        await appApi.explorer.openExplorer(result.outputFilePath);\n\n      } catch (error) {\n        console.error('导出文件失败:', error);\n        ElMessage.error('导出文件失败');\n      } finally {\n        processing.value = false;\n      }\n    };\n\n    // 源文件管理相关方法\n    const handleParseResult = (file: SourceFile, params: ParsedParam[]) => {\n      currentParseFile.value = file;\n      currentParsedParams.value = params;\n      parseDialogVisible.value = true;\n    };\n\n    const applyParsedParams = (params: ParsedParam[]) => {\n      params.forEach(param => {\n        // 查找对应的变量（不区分大小写）\n        const variable = variables.value.find(v => v.name.toLowerCase() === param.name.toLowerCase());\n        if (variable) {\n          // 应用参数值\n          parameterValues.value[variable.name] = String(param.value);\n          // 记录参数来源\n          parameterSources.value[variable.name] = param.source;\n          // 标记为已修改\n          modifiedParams.value.add(variable.name);\n        }\n      });\n\n      // 注意：文件列表现在由DataFileManager组件管理\n\n      ElMessage.success(`成功应用 ${params.length} 个参数`);\n    };\n\n    const getParameterSource = (paramName: string) => {\n      return parameterSources.value[paramName];\n    };\n\n    // 判断参数是否为复杂类型\n    const isComplexParameter = (variable: CaplVariable) => {\n      const value = parameterValues.value[variable.name];\n      if (!value) return false;\n\n      // 检查是否是结构体类型（包含大括号）\n      const trimmedValue = value.trim();\n      if (trimmedValue.startsWith('{') && trimmedValue.includes(',')) {\n        return true;\n      }\n\n      // 检查是否是JSON格式\n      if (trimmedValue.startsWith('{') || trimmedValue.startsWith('[')) {\n        try {\n          JSON.parse(trimmedValue);\n          return true;\n        } catch {\n          // 不是有效的JSON，但可能是CIN格式的结构体\n          return trimmedValue.includes(',') || trimmedValue.includes('{');\n        }\n      }\n\n      // 检查变量类型是否为复杂类型\n      const type = variable.type.toLowerCase();\n      return type.includes('struct') || type.includes('array') || type.includes('[]');\n    };\n\n    // 编辑复杂参数\n    const editComplexParameter = (variable: CaplVariable) => {\n      currentComplexParamName.value = variable.name;\n      currentComplexParamValue.value = parameterValues.value[variable.name] || '';\n      complexParamDialogVisible.value = true;\n    };\n\n    // 处理复杂参数编辑确认\n    const handleComplexParamConfirm = (value: string) => {\n      if (currentComplexParamName.value) {\n        parameterValues.value[currentComplexParamName.value] = value;\n        onParameterChange(currentComplexParamName.value);\n      }\n    };\n\n    const getFileName = (path: string) => {\n      if (!path) return '';\n      return path.split(/[\\\\/]/).pop() || '';\n    };\n\n    const getFileTypeTagType = (fileType: SourceFileType) => {\n      switch (fileType) {\n        case SourceFileType.Arxml: return 'primary';\n        case SourceFileType.Sddb: return 'success';\n        case SourceFileType.Ldf: return 'warning';\n        default: return '';\n      }\n    };\n\n    const getAppliedParamsCount = (file: SourceFile) => {\n      // 计算该文件已应用的参数数量\n      let count = 0;\n      Object.keys(parameterSources.value).forEach(paramName => {\n        if (parameterSources.value[paramName] === file.fileName) {\n          count++;\n        }\n      });\n      return count;\n    };\n\n\n\n\n\n\n\n    onMounted(async () => {\n      await loadTemplates();\n    });\n\n    return {\n      templates,\n      sourceType,\n      selectedCategory,\n      selectedTemplateId,\n      selectedTemplatePath,\n      filePath,\n      variables,\n      parameterValues,\n      sourceFilePath,\n      searchKeyword,\n      parsing,\n      processing,\n      categories,\n      cascaderOptions,\n      filteredTemplates,\n      filteredVariables,\n      modifiedParamsCount,\n      hasUnsavedChanges,\n      onSourceTypeChange,\n      onCategoryChange,\n      selectLocalFile,\n      handleTemplateChange,\n      getTemplatesByCategory,\n      loadSelectedTemplate,\n      onParameterChange,\n      exportCinFile,\n      getFileName,\n      getFileTypeTagType,\n      getAppliedParamsCount,\n      // 源文件管理相关\n      parseDialogVisible,\n      currentParseFile,\n      currentParsedParams,\n      handleParseResult,\n      applyParsedParams,\n      getParameterSource,\n      // 复杂参数编辑相关\n      isComplexParameter,\n      editComplexParameter,\n      handleComplexParamConfirm,\n      complexParamDialogVisible,\n      currentComplexParamValue,\n      complexParamTitle,\n      // 图标和枚举\n      Document,\n      SourceFileStatus\n    };\n  },\n});\n</script>\n\n<style scoped>\n.parameter-tool {\n  height: 100vh;\n  display: flex;\n  flex-direction: column;\n  background-color: var(--el-bg-color-page);\n}\n\n.toolbar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 20px;\n  background: var(--el-bg-color);\n  border-bottom: 1px solid var(--el-border-color-base);\n  flex-shrink: 0;\n}\n\n.toolbar-left {\n  display: flex;\n  align-items: center;\n}\n\n.toolbar-right {\n  display: flex;\n  gap: 8px;\n}\n\n.current-file-info {\n  padding: 8px 20px;\n  background: var(--el-color-primary-light-9);\n  border-bottom: 1px solid var(--el-border-color-lighter);\n  flex-shrink: 0;\n}\n\n.file-info-content {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.file-icon {\n  color: var(--el-color-primary);\n}\n\n.file-name {\n  font-weight: 500;\n  color: var(--el-text-color-primary);\n}\n\n.main-content {\n  flex: 1;\n  overflow: hidden;\n  display: flex;\n  flex-direction: column;\n  padding: 20px;\n  min-height: 0;\n  /* 允许flex子元素收缩 */\n}\n\n.search-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.filter-info {\n  display: flex;\n  gap: 16px;\n  font-size: 14px;\n  color: var(--el-text-color-secondary);\n}\n\n.import-info {\n  color: var(--el-color-primary);\n}\n\n\n\n.current-value {\n  color: var(--el-text-color-regular);\n  font-style: italic;\n}\n\n.parameter-source {\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  font-size: 12px;\n  color: var(--el-color-primary);\n}\n\n.param-value-cell {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.param-value-input {\n  flex: 1;\n}\n\n.edit-button {\n  flex-shrink: 0;\n  padding: 0 4px;\n}\n\n.no-source {\n  color: var(--el-text-color-placeholder);\n  font-style: italic;\n}\n\n/* CIN 参数列表卡片样式 */\n.cin-parameters-section {\n  margin-bottom: 16px;\n  padding: 16px;\n  background: var(--el-bg-color);\n  border: 1px solid var(--el-border-color);\n  border-radius: 6px;\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  min-height: 0;\n  /* 允许flex子元素收缩 */\n}\n\n.cin-operations-content {\n  margin-top: 12px;\n  flex-shrink: 0;\n  /* 不允许收缩 */\n}\n\n.operation-row {\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n  gap: 8px;\n}\n\n.search-bar {\n  margin-bottom: 12px;\n  flex-shrink: 0;\n  /* 不允许收缩 */\n}\n\n.parameters-table {\n  flex: 1;\n  overflow: hidden;\n  min-height: 0;\n  /* 允许flex子元素收缩 */\n}\n\n\n\n.imported-files-section {\n  margin-top: 16px;\n  padding: 16px;\n  background: var(--el-bg-color);\n  border: 1px solid var(--el-border-color);\n  border-radius: 6px;\n}\n\n.imported-files-section h4 {\n  margin: 0 0 12px 0;\n  color: var(--el-text-color-primary);\n  font-size: 14px;\n  font-weight: 600;\n}\n\n.imported-files-list {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.imported-file-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 8px 12px;\n  background: var(--el-fill-color-light);\n  border-radius: 4px;\n}\n\n.imported-file-item .file-name {\n  font-weight: 500;\n}\n\n.param-count {\n  font-size: 12px;\n  color: var(--el-text-color-secondary);\n}\n\n.file-actions {\n  margin-left: auto;\n  display: flex;\n  gap: 8px;\n}\n\n.template-selection {\n  display: flex;\n}\n\n.category-list {\n  width: 200px;\n  border-right: 1px solid var(--el-border-color-lighter);\n  overflow-y: auto;\n}\n\n.category-item {\n  padding: 12px 16px;\n  cursor: pointer;\n  border-bottom: 1px solid var(--el-border-color-lighter);\n}\n\n.category-item:hover {\n  background: var(--el-fill-color-light);\n}\n\n.category-item.active {\n  background: var(--el-color-primary-light-9);\n  color: var(--el-color-primary);\n  font-weight: 500;\n}\n\n.template-list {\n  flex: 1;\n  overflow-y: auto;\n  padding: 8px;\n}\n\n.template-item {\n  padding: 12px;\n  margin-bottom: 8px;\n  border: 1px solid var(--el-border-color-lighter);\n  border-radius: 4px;\n  cursor: pointer;\n}\n\n.template-item:hover {\n  border-color: var(--el-color-primary);\n}\n\n.template-item.active {\n  border-color: var(--el-color-primary);\n  background: var(--el-color-primary-light-9);\n}\n\n.template-name {\n  font-weight: 500;\n  color: var(--el-text-color-primary);\n  margin-bottom: 4px;\n}\n\n.template-desc {\n  font-size: 12px;\n  color: var(--el-text-color-secondary);\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .toolbar {\n    flex-direction: column;\n    gap: 8px;\n    align-items: stretch;\n  }\n\n  .toolbar-left,\n  .toolbar-right {\n    justify-content: center;\n  }\n\n  .search-bar {\n    flex-direction: column;\n    gap: 8px;\n    align-items: stretch;\n  }\n\n  .template-selection {\n    flex-direction: column;\n    height: auto;\n  }\n\n  .category-list {\n    width: auto;\n    border-right: none;\n    border-bottom: 1px solid var(--el-border-color-lighter);\n    max-height: 150px;\n  }\n}\n</style>\n", "import { defineComponent as _defineComponent } from 'vue'\nimport { resolveComponent as _resolveComponent, createVNode as _createVNode, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, withCtx as _withCtx, createSlots as _createSlots, openBlock as _openBlock, createBlock as _createBlock } from \"vue\"\n\nconst _hoisted_1 = { class: \"param-value-viewer\" }\nconst _hoisted_2 = { class: \"dialog-footer\" }\n\nimport { ref, computed, watch } from 'vue';\n\n// Props\ninterface Props {\n  modelValue: boolean;\n  value: any;\n  readonly?: boolean;\n  title?: string;\n}\n\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'ParamValueViewer',\n  props: {\n    modelValue: { type: Boolean },\n    value: {},\n    readonly: { type: Boolean, default: false },\n    title: { default: '参数值' }\n  },\n  emits: [\"update:modelValue\", \"update:value\", \"confirm\"],\n  setup(__props: any, { emit: __emit }) {\n\nconst props = __props;\n\n// Emits\nconst emit = __emit;\n\n// 数据\nconst visible = ref(false);\nconst displayValue = ref('');\n\n// 计算属性\nconst formattedValue = computed(() => {\n  if (props.value === null || props.value === undefined) {\n    return '';\n  }\n  \n  if (typeof props.value === 'string') {\n    // 如果是字符串，尝试格式化JSON\n    try {\n      const parsed = JSON.parse(props.value);\n      return JSON.stringify(parsed, null, 2);\n    } catch {\n      // 如果不是有效的JSON，直接返回原值\n      return props.value;\n    }\n  } else {\n    // 如果是对象，序列化为JSON\n    try {\n      return JSON.stringify(props.value, null, 2);\n    } catch {\n      return String(props.value);\n    }\n  }\n});\n\n// 监听器\nwatch(() => props.modelValue, (newVal) => {\n  visible.value = newVal;\n  if (newVal) {\n    displayValue.value = formattedValue.value;\n  }\n});\n\nwatch(visible, (newVal) => {\n  emit('update:modelValue', newVal);\n});\n\nwatch(() => props.value, () => {\n  if (visible.value) {\n    displayValue.value = formattedValue.value;\n  }\n});\n\n// 方法\nconst handleClose = () => {\n  visible.value = false;\n};\n\nconst handleInput = (value: string) => {\n  if (!props.readonly) {\n    emit('update:value', value);\n  }\n};\n\nconst handleConfirm = () => {\n  if (!props.readonly) {\n    emit('confirm', displayValue.value);\n  }\n  handleClose();\n};\n\nreturn (_ctx: any,_cache: any) => {\n  const _component_el_input = _resolveComponent(\"el-input\")!\n  const _component_el_button = _resolveComponent(\"el-button\")!\n  const _component_el_dialog = _resolveComponent(\"el-dialog\")!\n\n  return (_openBlock(), _createBlock(_component_el_dialog, {\n    modelValue: visible.value,\n    \"onUpdate:modelValue\": _cache[1] || (_cache[1] = ($event: any) => ((visible).value = $event)),\n    title: _ctx.title,\n    width: \"60%\",\n    \"before-close\": handleClose,\n    \"destroy-on-close\": \"\"\n  }, _createSlots({\n    default: _withCtx(() => [\n      _createElementVNode(\"div\", _hoisted_1, [\n        _createVNode(_component_el_input, {\n          modelValue: displayValue.value,\n          \"onUpdate:modelValue\": _cache[0] || (_cache[0] = ($event: any) => ((displayValue).value = $event)),\n          type: \"textarea\",\n          rows: 15,\n          readonly: _ctx.readonly,\n          placeholder: _ctx.readonly ? '' : '请输入参数值...',\n          style: {\"font-family\":\"'Courier New', monospace\"},\n          onInput: handleInput\n        }, null, 8, [\"modelValue\", \"readonly\", \"placeholder\"])\n      ])\n    ]),\n    _: 2\n  }, [\n    (!_ctx.readonly)\n      ? {\n          name: \"footer\",\n          fn: _withCtx(() => [\n            _createElementVNode(\"div\", _hoisted_2, [\n              _createVNode(_component_el_button, { onClick: handleClose }, {\n                default: _withCtx(() => _cache[2] || (_cache[2] = [\n                  _createTextVNode(\"取消\")\n                ])),\n                _: 1,\n                __: [2]\n              }),\n              _createVNode(_component_el_button, {\n                type: \"primary\",\n                onClick: handleConfirm\n              }, {\n                default: _withCtx(() => _cache[3] || (_cache[3] = [\n                  _createTextVNode(\"确定\")\n                ])),\n                _: 1,\n                __: [3]\n              })\n            ])\n          ]),\n          key: \"0\"\n        }\n      : undefined\n  ]), 1032, [\"modelValue\", \"title\"]))\n}\n}\n\n})", "<template>\n  <el-dialog\n    v-model=\"visible\"\n    :title=\"title\"\n    width=\"60%\"\n    :before-close=\"handleClose\"\n    destroy-on-close\n  >\n    <div class=\"param-value-viewer\">\n      <el-input\n        v-model=\"displayValue\"\n        type=\"textarea\"\n        :rows=\"15\"\n        :readonly=\"readonly\"\n        :placeholder=\"readonly ? '' : '请输入参数值...'\"\n        style=\"font-family: 'Courier New', monospace;\"\n        @input=\"handleInput\"\n      />\n    </div>\n\n    <template #footer v-if=\"!readonly\">\n      <div class=\"dialog-footer\">\n        <el-button @click=\"handleClose\">取消</el-button>\n        <el-button type=\"primary\" @click=\"handleConfirm\">确定</el-button>\n      </div>\n    </template>\n  </el-dialog>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref, computed, watch } from 'vue';\n\n// Props\ninterface Props {\n  modelValue: boolean;\n  value: any;\n  readonly?: boolean;\n  title?: string;\n}\n\nconst props = withDefaults(defineProps<Props>(), {\n  readonly: false,\n  title: '参数值'\n});\n\n// Emits\nconst emit = defineEmits<{\n  'update:modelValue': [value: boolean];\n  'update:value': [value: string];\n  'confirm': [value: string];\n}>();\n\n// 数据\nconst visible = ref(false);\nconst displayValue = ref('');\n\n// 计算属性\nconst formattedValue = computed(() => {\n  if (props.value === null || props.value === undefined) {\n    return '';\n  }\n  \n  if (typeof props.value === 'string') {\n    // 如果是字符串，尝试格式化JSON\n    try {\n      const parsed = JSON.parse(props.value);\n      return JSON.stringify(parsed, null, 2);\n    } catch {\n      // 如果不是有效的JSON，直接返回原值\n      return props.value;\n    }\n  } else {\n    // 如果是对象，序列化为JSON\n    try {\n      return JSON.stringify(props.value, null, 2);\n    } catch {\n      return String(props.value);\n    }\n  }\n});\n\n// 监听器\nwatch(() => props.modelValue, (newVal) => {\n  visible.value = newVal;\n  if (newVal) {\n    displayValue.value = formattedValue.value;\n  }\n});\n\nwatch(visible, (newVal) => {\n  emit('update:modelValue', newVal);\n});\n\nwatch(() => props.value, () => {\n  if (visible.value) {\n    displayValue.value = formattedValue.value;\n  }\n});\n\n// 方法\nconst handleClose = () => {\n  visible.value = false;\n};\n\nconst handleInput = (value: string) => {\n  if (!props.readonly) {\n    emit('update:value', value);\n  }\n};\n\nconst handleConfirm = () => {\n  if (!props.readonly) {\n    emit('confirm', displayValue.value);\n  }\n  handleClose();\n};\n</script>\n\n<style scoped>\n.param-value-viewer {\n  min-height: 300px;\n}\n\n.dialog-footer {\n  display: flex;\n  justify-content: flex-end;\n  gap: 12px;\n}\n</style>\n", "import script from \"./ParamValueViewer.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./ParamValueViewer.vue?vue&type=script&setup=true&lang=ts\"\n\nimport \"./ParamValueViewer.vue?vue&type=style&index=0&id=089a4f20&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-089a4f20\"]])\n\nexport default __exports__", "import { defineComponent as _defineComponent } from 'vue'\nimport { renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, resolveComponent as _resolveComponent, createBlock as _createBlock, withCtx as _withCtx, createVNode as _createVNode, unref as _unref, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode } from \"vue\"\n\nconst _hoisted_1 = { class: \"dialog-content\" }\nconst _hoisted_2 = { class: \"filter-section\" }\nconst _hoisted_3 = { class: \"param-list\" }\nconst _hoisted_4 = { class: \"param-list-header\" }\nconst _hoisted_5 = { class: \"selection-info\" }\nconst _hoisted_6 = { class: \"param-value-cell\" }\nconst _hoisted_7 = [\"title\"]\nconst _hoisted_8 = { class: \"dialog-footer\" }\n\nimport { ref, computed, watch, nextTick } from 'vue';\nimport { ElMessage } from 'element-plus';\nimport { Search } from '@element-plus/icons-vue';\nimport { SourceFile, ParsedParam, SourceFileType, ParamType } from '@/api/appApi';\nimport ParamValueViewer from './ParamValueViewer.vue';\n\n// Props\ninterface Props {\n  modelValue: boolean;\n  sourceFile: SourceFile | null;\n  parsedParams: ParsedParam[];\n}\n\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'ParamParseDialog',\n  props: {\n    modelValue: { type: Boolean },\n    sourceFile: {},\n    parsedParams: {}\n  },\n  emits: [\"update:modelValue\", \"apply-params\"],\n  setup(__props: any, { emit: __emit }) {\n\nconst props = __props;\n\n// Emits\nconst emit = __emit;\n\n// 数据\nconst visible = ref(false);\nconst searchText = ref('');\nconst selectedEcu = ref('');\nconst selectedParams = ref<ParsedParam[]>([]);\nconst paramValueDialogVisible = ref(false);\nconst currentParamValue = ref<any>('');\nconst paramTable = ref();\n\n// 计算属性\nconst ecuList = computed(() => {\n  const ecus = new Set(props.parsedParams.map(p => p.ecuName));\n  return Array.from(ecus).sort();\n});\n\nconst dialogTitle = computed(() => {\n  if (!props.sourceFile) {\n    return '参数解析结果';\n  }\n\n  const fileName = props.sourceFile.fileName;\n  const fileType = props.sourceFile.fileType.toUpperCase();\n  return `参数解析结果 - ${fileName} (${fileType})`;\n});\n\nconst filteredParams = computed(() => {\n  let params = props.parsedParams;\n\n  // 按ECU筛选\n  if (selectedEcu.value) {\n    params = params.filter(p => p.ecuName === selectedEcu.value);\n  }\n\n  // 按名称搜索\n  if (searchText.value) {\n    const search = searchText.value.toLowerCase();\n    params = params.filter(p => \n      p.name.toLowerCase().includes(search) ||\n      p.description.toLowerCase().includes(search)\n    );\n  }\n\n  return params;\n});\n\n// 监听器\nwatch(() => props.modelValue, (newVal) => {\n  visible.value = newVal;\n  if (newVal) {\n    // 重置状态\n    searchText.value = '';\n    selectedParams.value = [];\n    // 默认选择第一个ECU\n    if (ecuList.value.length > 0) {\n      selectedEcu.value = ecuList.value[0];\n      // 默认全选当前ECU的参数\n      nextTick(() => {\n        selectAllCurrentEcuParams();\n      });\n    }\n  }\n});\n\nwatch(visible, (newVal) => {\n  emit('update:modelValue', newVal);\n});\n\nwatch(selectedEcu, () => {\n  // ECU切换时默认全选当前ECU的参数\n  nextTick(() => {\n    selectAllCurrentEcuParams();\n  });\n});\n\n// 方法\nconst handleEcuChange = () => {\n  // 切换ECU时默认全选\n  selectAllCurrentEcuParams();\n};\n\nconst selectAllCurrentEcuParams = () => {\n  // 全选当前ECU的参数\n  if (paramTable.value) {\n    const currentEcuParams = filteredParams.value;\n    currentEcuParams.forEach(row => {\n      paramTable.value.toggleRowSelection(row, true);\n    });\n  }\n};\n\nconst handleClose = () => {\n  visible.value = false;\n};\n\nconst handleSelectionChange = (selection: ParsedParam[]) => {\n  selectedParams.value = selection;\n};\n\nconst getFileTypeTagType = (fileType?: SourceFileType) => {\n  switch (fileType) {\n    case SourceFileType.Arxml: return 'primary';\n    case SourceFileType.Sddb: return 'success';\n    case SourceFileType.Ldf: return 'warning';\n    default: return '';\n  }\n};\n\nconst getParamTypeTagType = (paramType: ParamType) => {\n  switch (paramType) {\n    case ParamType.String: return '';\n    case ParamType.Integer: return 'success';\n    case ParamType.Double: return 'success';\n    case ParamType.Boolean: return 'warning';\n    case ParamType.Json: return 'primary';\n    case ParamType.Array: return 'info';\n    case ParamType.Object: return 'info';\n    default: return '';\n  }\n};\n\nconst formatValue = (value: any) => {\n  if (value === null || value === undefined) {\n    return '';\n  }\n  \n  if (typeof value === 'string') {\n    return value.length > 50 ? value.substring(0, 50) + '...' : value;\n  }\n  \n  return String(value);\n};\n\nconst showParamValue = (param: ParsedParam) => {\n  currentParamValue.value = param.value;\n  paramValueDialogVisible.value = true;\n};\n\nconst applySelectedParams = () => {\n  if (selectedParams.value.length === 0) {\n    ElMessage.warning('请先选择要应用的参数');\n    return;\n  }\n\n  emit('apply-params', selectedParams.value);\n  ElMessage.success(`成功应用 ${selectedParams.value.length} 个参数`);\n  handleClose();\n};\n\nreturn (_ctx: any,_cache: any) => {\n  const _component_el_option = _resolveComponent(\"el-option\")!\n  const _component_el_select = _resolveComponent(\"el-select\")!\n  const _component_el_icon = _resolveComponent(\"el-icon\")!\n  const _component_el_input = _resolveComponent(\"el-input\")!\n  const _component_el_table_column = _resolveComponent(\"el-table-column\")!\n  const _component_el_tag = _resolveComponent(\"el-tag\")!\n  const _component_el_button = _resolveComponent(\"el-button\")!\n  const _component_el_table = _resolveComponent(\"el-table\")!\n  const _component_el_dialog = _resolveComponent(\"el-dialog\")!\n\n  return (_openBlock(), _createBlock(_component_el_dialog, {\n    modelValue: visible.value,\n    \"onUpdate:modelValue\": _cache[3] || (_cache[3] = ($event: any) => ((visible).value = $event)),\n    title: dialogTitle.value,\n    width: \"80%\",\n    \"before-close\": handleClose,\n    \"destroy-on-close\": \"\"\n  }, {\n    footer: _withCtx(() => [\n      _createElementVNode(\"div\", _hoisted_8, [\n        _createVNode(_component_el_button, { onClick: handleClose }, {\n          default: _withCtx(() => _cache[5] || (_cache[5] = [\n            _createTextVNode(\"取消\")\n          ])),\n          _: 1,\n          __: [5]\n        }),\n        _createVNode(_component_el_button, {\n          type: \"primary\",\n          onClick: applySelectedParams,\n          disabled: selectedParams.value.length === 0\n        }, {\n          default: _withCtx(() => [\n            _createTextVNode(\" 应用参数 (\" + _toDisplayString(selectedParams.value.length) + \") \", 1)\n          ]),\n          _: 1\n        }, 8, [\"disabled\"])\n      ])\n    ]),\n    default: _withCtx(() => [\n      _createElementVNode(\"div\", _hoisted_1, [\n        _createElementVNode(\"div\", _hoisted_2, [\n          _createVNode(_component_el_select, {\n            modelValue: selectedEcu.value,\n            \"onUpdate:modelValue\": _cache[0] || (_cache[0] = ($event: any) => ((selectedEcu).value = $event)),\n            placeholder: \"选择 ECU\",\n            style: {\"width\":\"200px\"},\n            onChange: handleEcuChange\n          }, {\n            default: _withCtx(() => [\n              (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(ecuList.value, (ecu) => {\n                return (_openBlock(), _createBlock(_component_el_option, {\n                  key: ecu,\n                  label: ecu,\n                  value: ecu\n                }, null, 8, [\"label\", \"value\"]))\n              }), 128))\n            ]),\n            _: 1\n          }, 8, [\"modelValue\"]),\n          _createVNode(_component_el_input, {\n            modelValue: searchText.value,\n            \"onUpdate:modelValue\": _cache[1] || (_cache[1] = ($event: any) => ((searchText).value = $event)),\n            placeholder: \"搜索参数名称...\",\n            clearable: \"\",\n            style: {\"width\":\"300px\"}\n          }, {\n            prefix: _withCtx(() => [\n              _createVNode(_component_el_icon, null, {\n                default: _withCtx(() => [\n                  _createVNode(_unref(Search))\n                ]),\n                _: 1\n              })\n            ]),\n            _: 1\n          }, 8, [\"modelValue\"])\n        ]),\n        _createElementVNode(\"div\", _hoisted_3, [\n          _createElementVNode(\"div\", _hoisted_4, [\n            _createElementVNode(\"div\", _hoisted_5, \" 已选择 \" + _toDisplayString(selectedParams.value.length) + \"/\" + _toDisplayString(filteredParams.value.length) + \" 个参数 \", 1)\n          ]),\n          _createVNode(_component_el_table, {\n            ref_key: \"paramTable\",\n            ref: paramTable,\n            data: filteredParams.value,\n            height: \"400\",\n            onSelectionChange: handleSelectionChange,\n            \"row-key\": \"name\",\n            size: \"small\"\n          }, {\n            default: _withCtx(() => [\n              _createVNode(_component_el_table_column, {\n                type: \"selection\",\n                width: \"55\"\n              }),\n              _createVNode(_component_el_table_column, {\n                prop: \"name\",\n                label: \"参数名\",\n                width: \"200\",\n                \"show-overflow-tooltip\": \"\"\n              }),\n              _createVNode(_component_el_table_column, {\n                prop: \"paramType\",\n                label: \"类型\",\n                width: \"100\"\n              }, {\n                default: _withCtx(({ row }) => [\n                  _createVNode(_component_el_tag, {\n                    type: getParamTypeTagType(row.paramType),\n                    size: \"small\"\n                  }, {\n                    default: _withCtx(() => [\n                      _createTextVNode(_toDisplayString(row.paramType), 1)\n                    ]),\n                    _: 2\n                  }, 1032, [\"type\"])\n                ]),\n                _: 1\n              }),\n              _createVNode(_component_el_table_column, {\n                prop: \"value\",\n                label: \"参数值\",\n                \"min-width\": \"200\"\n              }, {\n                default: _withCtx(({ row }) => [\n                  _createElementVNode(\"div\", _hoisted_6, [\n                    _createElementVNode(\"span\", {\n                      class: \"param-value-text\",\n                      title: formatValue(row.value)\n                    }, _toDisplayString(formatValue(row.value)), 9, _hoisted_7),\n                    _createVNode(_component_el_button, {\n                      type: \"text\",\n                      size: \"small\",\n                      onClick: ($event: any) => (showParamValue(row)),\n                      class: \"view-button\"\n                    }, {\n                      default: _withCtx(() => _cache[4] || (_cache[4] = [\n                        _createTextVNode(\" 查看 \")\n                      ])),\n                      _: 2,\n                      __: [4]\n                    }, 1032, [\"onClick\"])\n                  ])\n                ]),\n                _: 1\n              })\n            ]),\n            _: 1\n          }, 8, [\"data\"])\n        ])\n      ]),\n      _createVNode(ParamValueViewer, {\n        modelValue: paramValueDialogVisible.value,\n        \"onUpdate:modelValue\": _cache[2] || (_cache[2] = ($event: any) => ((paramValueDialogVisible).value = $event)),\n        value: currentParamValue.value,\n        readonly: true,\n        title: \"参数值\"\n      }, null, 8, [\"modelValue\", \"value\"])\n    ]),\n    _: 1\n  }, 8, [\"modelValue\", \"title\"]))\n}\n}\n\n})", "<template>\n  <el-dialog\n    v-model=\"visible\"\n    :title=\"dialogTitle\"\n    width=\"80%\"\n    :before-close=\"handleClose\"\n    destroy-on-close\n  >\n    <div class=\"dialog-content\">\n      <!-- ECU选择和搜索 -->\n      <div class=\"filter-section\">\n        <el-select \n          v-model=\"selectedEcu\" \n          placeholder=\"选择 ECU\" \n          style=\"width: 200px;\"\n          @change=\"handleEcuChange\"\n        >\n          <el-option\n            v-for=\"ecu in ecuList\"\n            :key=\"ecu\"\n            :label=\"ecu\"\n            :value=\"ecu\"\n          />\n        </el-select>\n        \n        <el-input\n          v-model=\"searchText\"\n          placeholder=\"搜索参数名称...\"\n          clearable\n          style=\"width: 300px\"\n        >\n          <template #prefix>\n            <el-icon><Search /></el-icon>\n          </template>\n        </el-input>\n      </div>\n\n      <!-- 参数列表 -->\n      <div class=\"param-list\">\n        <div class=\"param-list-header\">\n          <div class=\"selection-info\">\n            已选择 {{ selectedParams.length }}/{{ filteredParams.length }} 个参数\n          </div>\n        </div>\n        <el-table\n          ref=\"paramTable\"\n          :data=\"filteredParams\"\n          height=\"400\"\n          @selection-change=\"handleSelectionChange\"\n          row-key=\"name\"\n          size=\"small\"\n        >\n          <el-table-column type=\"selection\" width=\"55\" />\n          <el-table-column prop=\"name\" label=\"参数名\" width=\"200\" show-overflow-tooltip />\n          <el-table-column prop=\"paramType\" label=\"类型\" width=\"100\">\n            <template #default=\"{ row }\">\n              <el-tag :type=\"getParamTypeTagType(row.paramType)\" size=\"small\">\n                {{ row.paramType }}\n              </el-tag>\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"value\" label=\"参数值\" min-width=\"200\">\n            <template #default=\"{ row }\">\n              <div class=\"param-value-cell\">\n                <span class=\"param-value-text\" :title=\"formatValue(row.value)\">\n                  {{ formatValue(row.value) }}\n                </span>\n                <el-button\n                  type=\"text\"\n                  size=\"small\"\n                  @click=\"showParamValue(row)\"\n                  class=\"view-button\"\n                >\n                  查看\n                </el-button>\n              </div>\n            </template>\n          </el-table-column>\n        </el-table>\n      </div>\n\n\n    </div>\n\n    <template #footer>\n      <div class=\"dialog-footer\">\n        <el-button @click=\"handleClose\">取消</el-button>\n        <el-button \n          type=\"primary\" \n          @click=\"applySelectedParams\"\n          :disabled=\"selectedParams.length === 0\"\n        >\n          应用参数 ({{ selectedParams.length }})\n        </el-button>\n      </div>\n    </template>\n\n    <!-- 参数值查看弹窗 -->\n    <ParamValueViewer\n      v-model=\"paramValueDialogVisible\"\n      :value=\"currentParamValue\"\n      :readonly=\"true\"\n      title=\"参数值\"\n    />\n  </el-dialog>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref, computed, watch, nextTick } from 'vue';\nimport { ElMessage } from 'element-plus';\nimport { Search } from '@element-plus/icons-vue';\nimport { SourceFile, ParsedParam, SourceFileType, ParamType } from '@/api/appApi';\nimport ParamValueViewer from './ParamValueViewer.vue';\n\n// Props\ninterface Props {\n  modelValue: boolean;\n  sourceFile: SourceFile | null;\n  parsedParams: ParsedParam[];\n}\n\nconst props = defineProps<Props>();\n\n// Emits\nconst emit = defineEmits<{\n  'update:modelValue': [value: boolean];\n  'apply-params': [params: ParsedParam[]];\n}>();\n\n// 数据\nconst visible = ref(false);\nconst searchText = ref('');\nconst selectedEcu = ref('');\nconst selectedParams = ref<ParsedParam[]>([]);\nconst paramValueDialogVisible = ref(false);\nconst currentParamValue = ref<any>('');\nconst paramTable = ref();\n\n// 计算属性\nconst ecuList = computed(() => {\n  const ecus = new Set(props.parsedParams.map(p => p.ecuName));\n  return Array.from(ecus).sort();\n});\n\nconst dialogTitle = computed(() => {\n  if (!props.sourceFile) {\n    return '参数解析结果';\n  }\n\n  const fileName = props.sourceFile.fileName;\n  const fileType = props.sourceFile.fileType.toUpperCase();\n  return `参数解析结果 - ${fileName} (${fileType})`;\n});\n\nconst filteredParams = computed(() => {\n  let params = props.parsedParams;\n\n  // 按ECU筛选\n  if (selectedEcu.value) {\n    params = params.filter(p => p.ecuName === selectedEcu.value);\n  }\n\n  // 按名称搜索\n  if (searchText.value) {\n    const search = searchText.value.toLowerCase();\n    params = params.filter(p => \n      p.name.toLowerCase().includes(search) ||\n      p.description.toLowerCase().includes(search)\n    );\n  }\n\n  return params;\n});\n\n// 监听器\nwatch(() => props.modelValue, (newVal) => {\n  visible.value = newVal;\n  if (newVal) {\n    // 重置状态\n    searchText.value = '';\n    selectedParams.value = [];\n    // 默认选择第一个ECU\n    if (ecuList.value.length > 0) {\n      selectedEcu.value = ecuList.value[0];\n      // 默认全选当前ECU的参数\n      nextTick(() => {\n        selectAllCurrentEcuParams();\n      });\n    }\n  }\n});\n\nwatch(visible, (newVal) => {\n  emit('update:modelValue', newVal);\n});\n\nwatch(selectedEcu, () => {\n  // ECU切换时默认全选当前ECU的参数\n  nextTick(() => {\n    selectAllCurrentEcuParams();\n  });\n});\n\n// 方法\nconst handleEcuChange = () => {\n  // 切换ECU时默认全选\n  selectAllCurrentEcuParams();\n};\n\nconst selectAllCurrentEcuParams = () => {\n  // 全选当前ECU的参数\n  if (paramTable.value) {\n    const currentEcuParams = filteredParams.value;\n    currentEcuParams.forEach(row => {\n      paramTable.value.toggleRowSelection(row, true);\n    });\n  }\n};\n\nconst handleClose = () => {\n  visible.value = false;\n};\n\nconst handleSelectionChange = (selection: ParsedParam[]) => {\n  selectedParams.value = selection;\n};\n\nconst getFileTypeTagType = (fileType?: SourceFileType) => {\n  switch (fileType) {\n    case SourceFileType.Arxml: return 'primary';\n    case SourceFileType.Sddb: return 'success';\n    case SourceFileType.Ldf: return 'warning';\n    default: return '';\n  }\n};\n\nconst getParamTypeTagType = (paramType: ParamType) => {\n  switch (paramType) {\n    case ParamType.String: return '';\n    case ParamType.Integer: return 'success';\n    case ParamType.Double: return 'success';\n    case ParamType.Boolean: return 'warning';\n    case ParamType.Json: return 'primary';\n    case ParamType.Array: return 'info';\n    case ParamType.Object: return 'info';\n    default: return '';\n  }\n};\n\nconst formatValue = (value: any) => {\n  if (value === null || value === undefined) {\n    return '';\n  }\n  \n  if (typeof value === 'string') {\n    return value.length > 50 ? value.substring(0, 50) + '...' : value;\n  }\n  \n  return String(value);\n};\n\nconst showParamValue = (param: ParsedParam) => {\n  currentParamValue.value = param.value;\n  paramValueDialogVisible.value = true;\n};\n\nconst applySelectedParams = () => {\n  if (selectedParams.value.length === 0) {\n    ElMessage.warning('请先选择要应用的参数');\n    return;\n  }\n\n  emit('apply-params', selectedParams.value);\n  ElMessage.success(`成功应用 ${selectedParams.value.length} 个参数`);\n  handleClose();\n};\n</script>\n\n<style scoped>\n.dialog-content {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n\n.filter-section {\n  display: flex;\n  gap: 16px;\n  align-items: center;\n}\n\n.param-list {\n  border: 1px solid var(--el-border-color-base);\n  border-radius: 6px;\n}\n\n.param-list-header {\n  display: flex;\n  justify-content: flex-end;\n  align-items: center;\n  padding: 8px 12px;\n  background: var(--el-bg-color-page);\n  border-bottom: 1px solid var(--el-border-color-base);\n}\n\n.selection-info {\n  font-size: 12px;\n  color: var(--el-text-color-regular);\n}\n\n.param-value-cell {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.param-value-text {\n  flex: 1;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.view-button {\n  flex-shrink: 0;\n  padding: 0 4px;\n}\n\n.dialog-footer {\n  display: flex;\n  justify-content: flex-end;\n  gap: 8px;\n}\n</style>\n", "import script from \"./ParamParseDialog.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./ParamParseDialog.vue?vue&type=script&setup=true&lang=ts\"\n\nimport \"./ParamParseDialog.vue?vue&type=style&index=0&id=df65a9d6&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-df65a9d6\"]])\n\nexport default __exports__", "import { createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, createElementBlock as _createElementBlock } from \"vue\"\n\nconst _hoisted_1 = { class: \"data-files-section\" }\nconst _hoisted_2 = { class: \"section-header\" }\nconst _hoisted_3 = { class: \"header-actions\" }\nconst _hoisted_4 = { class: \"data-files-table\" }\nconst _hoisted_5 = [\"title\"]\nconst _hoisted_6 = { class: \"name\" }\nconst _hoisted_7 = { class: \"action-buttons\" }\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  const _component_el_button = _resolveComponent(\"el-button\")!\n  const _component_el_tag = _resolveComponent(\"el-tag\")!\n  const _component_el_table_column = _resolveComponent(\"el-table-column\")!\n  const _component_el_table = _resolveComponent(\"el-table\")!\n\n  return (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _createElementVNode(\"div\", _hoisted_2, [\n      _cache[2] || (_cache[2] = _createElementVNode(\"h4\", null, \"数据文件 (ARXML/SDDB/LDF)\", -1)),\n      _createElementVNode(\"div\", _hoisted_3, [\n        _createVNode(_component_el_button, {\n          size: \"small\",\n          type: \"primary\",\n          onClick: _ctx.selectDataFiles\n        }, {\n          default: _withCtx(() => _cache[0] || (_cache[0] = [\n            _createTextVNode(\"添加文件\")\n          ])),\n          _: 1,\n          __: [0]\n        }, 8, [\"onClick\"]),\n        (_ctx.dataFiles.length > 0)\n          ? (_openBlock(), _createBlock(_component_el_button, {\n              key: 0,\n              size: \"small\",\n              type: \"danger\",\n              onClick: _ctx.clearAllDataFiles\n            }, {\n              default: _withCtx(() => _cache[1] || (_cache[1] = [\n                _createTextVNode(\"清空\")\n              ])),\n              _: 1,\n              __: [1]\n            }, 8, [\"onClick\"]))\n          : _createCommentVNode(\"\", true)\n      ])\n    ]),\n    _createElementVNode(\"div\", _hoisted_4, [\n      _createVNode(_component_el_table, {\n        data: _ctx.dataFiles,\n        border: \"\",\n        style: {\"width\":\"100%\"},\n        size: \"small\"\n      }, {\n        default: _withCtx(() => [\n          _createVNode(_component_el_table_column, {\n            label: \"类型\",\n            width: \"80\"\n          }, {\n            default: _withCtx((scope) => [\n              _createVNode(_component_el_tag, {\n                type: _ctx.getFileTypeTagType(scope.row.fileType),\n                size: \"small\"\n              }, {\n                default: _withCtx(() => [\n                  _createTextVNode(_toDisplayString(scope.row.fileType.toUpperCase()), 1)\n                ]),\n                _: 2\n              }, 1032, [\"type\"])\n            ]),\n            _: 1\n          }),\n          _createVNode(_component_el_table_column, {\n            label: \"文件名\",\n            \"min-width\": \"200\"\n          }, {\n            default: _withCtx((scope) => [\n              _createElementVNode(\"div\", {\n                class: \"file-name\",\n                title: scope.row.path\n              }, [\n                _createElementVNode(\"span\", _hoisted_6, _toDisplayString(scope.row.fileName), 1)\n              ], 8, _hoisted_5)\n            ]),\n            _: 1\n          }),\n          _createVNode(_component_el_table_column, {\n            label: \"状态\",\n            width: \"100\"\n          }, {\n            default: _withCtx((scope) => [\n              _createVNode(_component_el_tag, {\n                type: _ctx.getStatusTagType(scope.row.status),\n                size: \"small\"\n              }, {\n                default: _withCtx(() => [\n                  _createTextVNode(_toDisplayString(_ctx.getStatusText(scope.row.status)), 1)\n                ]),\n                _: 2\n              }, 1032, [\"type\"])\n            ]),\n            _: 1\n          }),\n          _createVNode(_component_el_table_column, {\n            label: \"操作\",\n            width: \"300\"\n          }, {\n            default: _withCtx((scope) => [\n              _createElementVNode(\"div\", _hoisted_7, [\n                (scope.row.status === _ctx.SourceFileStatus.Pending || scope.row.status === _ctx.SourceFileStatus.Error)\n                  ? (_openBlock(), _createBlock(_component_el_button, {\n                      key: 0,\n                      type: \"primary\",\n                      size: \"small\",\n                      onClick: ($event: any) => (_ctx.parseDataFile(scope.row.id))\n                    }, {\n                      default: _withCtx(() => _cache[3] || (_cache[3] = [\n                        _createTextVNode(\" 解析参数 \")\n                      ])),\n                      _: 2,\n                      __: [3]\n                    }, 1032, [\"onClick\"]))\n                  : _createCommentVNode(\"\", true),\n                (scope.row.status === _ctx.SourceFileStatus.Parsed)\n                  ? (_openBlock(), _createBlock(_component_el_button, {\n                      key: 1,\n                      type: \"success\",\n                      size: \"small\",\n                      onClick: ($event: any) => (_ctx.viewDataFileDetails(scope.row))\n                    }, {\n                      default: _withCtx(() => _cache[4] || (_cache[4] = [\n                        _createTextVNode(\" 查看结果 \")\n                      ])),\n                      _: 2,\n                      __: [4]\n                    }, 1032, [\"onClick\"]))\n                  : _createCommentVNode(\"\", true),\n                _createVNode(_component_el_button, {\n                  type: \"warning\",\n                  size: \"small\",\n                  onClick: ($event: any) => (_ctx.openDataFileFolder(scope.row.path))\n                }, {\n                  default: _withCtx(() => _cache[5] || (_cache[5] = [\n                    _createTextVNode(\" 打开文件夹 \")\n                  ])),\n                  _: 2,\n                  __: [5]\n                }, 1032, [\"onClick\"]),\n                _createVNode(_component_el_button, {\n                  type: \"danger\",\n                  size: \"small\",\n                  onClick: ($event: any) => (_ctx.removeDataFile(scope.row.id))\n                }, {\n                  default: _withCtx(() => _cache[6] || (_cache[6] = [\n                    _createTextVNode(\" 移除 \")\n                  ])),\n                  _: 2,\n                  __: [6]\n                }, 1032, [\"onClick\"])\n              ])\n            ]),\n            _: 1\n          })\n        ]),\n        _: 1\n      }, 8, [\"data\"])\n    ])\n  ]))\n}", "<template>\n  <div class=\"data-files-section\">\n    <div class=\"section-header\">\n      <h4>数据文件 (ARXML/SDDB/LDF)</h4>\n      <div class=\"header-actions\">\n        <el-button size=\"small\" type=\"primary\" @click=\"selectDataFiles\">添加文件</el-button>\n        <el-button size=\"small\" type=\"danger\" @click=\"clearAllDataFiles\" v-if=\"dataFiles.length > 0\">清空</el-button>\n      </div>\n    </div>\n    \n    <div class=\"data-files-table\">\n      <el-table :data=\"dataFiles\" border style=\"width: 100%\" size=\"small\">\n        <el-table-column label=\"类型\" width=\"80\">\n          <template #default=\"scope\">\n            <el-tag :type=\"getFileTypeTagType(scope.row.fileType)\" size=\"small\">{{ scope.row.fileType.toUpperCase() }}</el-tag>\n          </template>\n        </el-table-column>\n        \n        <el-table-column label=\"文件名\" min-width=\"200\">\n          <template #default=\"scope\">\n            <div class=\"file-name\" :title=\"scope.row.path\">\n              <span class=\"name\">{{ scope.row.fileName }}</span>\n            </div>\n          </template>\n        </el-table-column>\n        \n        <el-table-column label=\"状态\" width=\"100\">\n          <template #default=\"scope\">\n            <el-tag :type=\"getStatusTagType(scope.row.status)\" size=\"small\">{{ getStatusText(scope.row.status) }}</el-tag>\n          </template>\n        </el-table-column>\n        \n        <el-table-column label=\"操作\" width=\"300\">\n          <template #default=\"scope\">\n            <div class=\"action-buttons\">\n              <el-button \n                v-if=\"scope.row.status === SourceFileStatus.Pending || scope.row.status === SourceFileStatus.Error\"\n                type=\"primary\" \n                size=\"small\"\n                @click=\"parseDataFile(scope.row.id)\"\n              >\n                解析参数\n              </el-button>\n              \n              <el-button \n                v-if=\"scope.row.status === SourceFileStatus.Parsed\"\n                type=\"success\" \n                size=\"small\"\n                @click=\"viewDataFileDetails(scope.row)\"\n              >\n                查看结果\n              </el-button>\n              \n              <el-button\n                type=\"warning\"\n                size=\"small\"\n                @click=\"openDataFileFolder(scope.row.path)\"\n              >\n                打开文件夹\n              </el-button>\n              \n              <el-button \n                type=\"danger\" \n                size=\"small\"\n                @click=\"removeDataFile(scope.row.id)\"\n              >\n                移除\n              </el-button>\n            </div>\n          </template>\n        </el-table-column>\n      </el-table>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { defineComponent, ref, onMounted } from \"vue\";\nimport {\n  appApi,\n  SourceFile,\n  SourceFileType,\n  SourceFileStatus\n} from \"@/api/appApi\";\nimport { ElMessage, ElMessageBox } from \"element-plus\";\n\nexport default defineComponent({\n  name: \"DataFileManager\",\n  emits: ['parse-result'],\n  setup(props, { emit }) {\n    // 数据\n    const dataFiles = ref<SourceFile[]>([]);\n\n    // 方法\n    const loadDataFiles = async () => {\n      try {\n        const response = await appApi.cinParameter.getSourceFiles();\n        dataFiles.value = response.data;\n      } catch (error) {\n        console.error('加载数据文件失败:', error);\n        ElMessage.error('加载数据文件失败');\n      }\n    };\n\n    const selectDataFiles = async () => {\n      try {\n        const response = await appApi.cinParameter.selectSourceFiles();\n        const filePaths = response.data;\n        \n        if (filePaths && filePaths.length > 0) {\n          await addDataFiles(filePaths);\n          ElMessage.success(`成功添加 ${filePaths.length} 个参数数据文件`);\n        }\n      } catch (error) {\n        console.error('选择文件失败:', error);\n        ElMessage.error('选择文件失败');\n      }\n    };\n\n    const addDataFiles = async (filePaths: string[]) => {\n      try {\n        const response = await appApi.cinParameter.addSourceFiles({ filePaths });\n        const newFiles = response.data;\n        \n        // 更新导入文件列表\n        newFiles.forEach(file => {\n          const existingIndex = dataFiles.value.findIndex(f => f.id === file.id);\n          if (existingIndex >= 0) {\n            dataFiles.value[existingIndex] = file;\n          } else {\n            dataFiles.value.push(file);\n          }\n        });\n        \n        return newFiles;\n      } catch (error) {\n        console.error('添加文件失败:', error);\n        throw error;\n      }\n    };\n\n    const parseDataFile = async (fileId: string) => {\n      try {\n        // 先更新本地状态\n        const file = dataFiles.value.find(f => f.id === fileId);\n        if (file) {\n          file.status = SourceFileStatus.Parsing;\n        }\n\n        const response = await appApi.cinParameter.parseSourceFile({\n          fileId\n        });\n        \n        const updatedFile = response.data;\n        \n        // 更新文件列表中的对应项\n        const index = dataFiles.value.findIndex(f => f.id === fileId);\n        if (index >= 0) {\n          dataFiles.value[index] = updatedFile;\n        }\n        \n        ElMessage.success(`参数解析完成，共解析出 ${updatedFile.parsedParams?.length || 0} 个参数`);\n        \n        // 发送解析结果事件给父组件\n        if (updatedFile.parsedParams && updatedFile.parsedParams.length > 0) {\n          emit('parse-result', updatedFile, updatedFile.parsedParams);\n        }\n      } catch (error) {\n        console.error('解析文件失败:', error);\n        ElMessage.error('参数解析失败');\n        \n        // 恢复状态\n        const file = dataFiles.value.find(f => f.id === fileId);\n        if (file) {\n          file.status = SourceFileStatus.Error;\n        }\n      }\n    };\n\n    const viewDataFileDetails = (file: SourceFile) => {\n      if (file.parsedParams && file.parsedParams.length > 0) {\n        emit('parse-result', file, file.parsedParams);\n      } else {\n        ElMessage.warning('该文件暂无解析结果');\n      }\n    };\n\n    const openDataFileFolder = async (filePath: string) => {\n      try {\n        await appApi.explorer.openExplorer(filePath);\n      } catch (error) {\n        console.error('打开文件夹失败:', error);\n        ElMessage.error('打开文件夹失败');\n      }\n    };\n\n    const removeDataFile = async (fileId: string) => {\n      try {\n        // 调用服务端接口移除文件\n        await appApi.cinParameter.removeSourceFile({ fileId });\n        \n        // 从本地列表中移除文件\n        const fileIndex = dataFiles.value.findIndex(f => f.id === fileId);\n        if (fileIndex >= 0) {\n          dataFiles.value.splice(fileIndex, 1);\n          ElMessage.success('已移除数据文件');\n        }\n      } catch (error) {\n        console.error('移除文件失败:', error);\n        ElMessage.error('移除文件失败');\n      }\n    };\n\n    const clearAllDataFiles = async () => {\n      try {\n        await ElMessageBox.confirm('确定要清空所有参数数据文件吗？', '确认', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        });\n\n        // 调用服务端接口清空所有文件\n        await appApi.cinParameter.clearSourceFiles();\n        \n        // 清空本地文件列表\n        dataFiles.value = [];\n        ElMessage.success('已清空所有参数数据文件');\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('清空文件失败:', error);\n          ElMessage.error('清空文件失败');\n        }\n      }\n    };\n\n    // 辅助方法\n    const getFileTypeTagType = (fileType: SourceFileType) => {\n      switch (fileType) {\n        case SourceFileType.Arxml: return 'primary';\n        case SourceFileType.Sddb: return 'success';\n        case SourceFileType.Ldf: return 'warning';\n        default: return '';\n      }\n    };\n\n    const getStatusTagType = (status: SourceFileStatus) => {\n      switch (status) {\n        case SourceFileStatus.Pending: return '';\n        case SourceFileStatus.Parsing: return 'warning';\n        case SourceFileStatus.Parsed: return 'success';\n        case SourceFileStatus.Error: return 'danger';\n        default: return '';\n      }\n    };\n\n    const getStatusText = (status: SourceFileStatus) => {\n      switch (status) {\n        case SourceFileStatus.Pending: return '待解析';\n        case SourceFileStatus.Parsing: return '解析中';\n        case SourceFileStatus.Parsed: return '已解析';\n        case SourceFileStatus.Error: return '解析失败';\n        default: return status;\n      }\n    };\n\n    // 暴露给父组件的方法\n    const refreshDataFiles = () => {\n      loadDataFiles();\n    };\n\n    // 生命周期\n    onMounted(() => {\n      loadDataFiles();\n    });\n\n    return {\n      dataFiles,\n      selectDataFiles,\n      parseDataFile,\n      viewDataFileDetails,\n      openDataFileFolder,\n      removeDataFile,\n      clearAllDataFiles,\n      getFileTypeTagType,\n      getStatusTagType,\n      getStatusText,\n      refreshDataFiles,\n      // 枚举\n      SourceFileStatus\n    };\n  },\n});\n</script>\n\n<style scoped>\n/* 参数数据文件样式 */\n.data-files-section {\n  padding: 16px;\n  background: var(--el-bg-color);\n  border: 1px solid var(--el-border-color);\n  border-radius: 6px;\n  flex-shrink: 0;\n  max-height: 300px;\n  overflow-y: auto;\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n}\n\n.section-header h4 {\n  margin: 0;\n  color: var(--el-text-color-primary);\n  font-size: 14px;\n  font-weight: 600;\n}\n\n.header-actions {\n  display: flex;\n  gap: 8px;\n}\n\n.data-files-table {\n  margin-top: 12px;\n}\n\n.data-files-table .file-name .name {\n  font-weight: 500;\n}\n\n.action-buttons {\n  display: flex;\n  gap: 4px;\n  flex-wrap: wrap;\n}\n</style>\n", "import { render } from \"./DataFileManager.vue?vue&type=template&id=3b908206&scoped=true&ts=true\"\nimport script from \"./DataFileManager.vue?vue&type=script&lang=ts\"\nexport * from \"./DataFileManager.vue?vue&type=script&lang=ts\"\n\nimport \"./DataFileManager.vue?vue&type=style&index=0&id=3b908206&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-3b908206\"]])\n\nexport default __exports__", "import { render } from \"./ParameterToolView.vue?vue&type=template&id=8af326d6&scoped=true&ts=true\"\nimport script from \"./ParameterToolView.vue?vue&type=script&lang=ts\"\nexport * from \"./ParameterToolView.vue?vue&type=script&lang=ts\"\n\nimport \"./ParameterToolView.vue?vue&type=style&index=0&id=8af326d6&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-8af326d6\"]])\n\nexport default __exports__"], "names": ["_hoisted_1", "class", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_hoisted_6", "key", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_hoisted_10", "_hoisted_11", "render", "_ctx", "_cache", "$props", "$setup", "$data", "$options", "_component_el_cascader", "_resolveComponent", "_component_el_button", "_component_el_divider", "_component_el_input", "_component_el_table_column", "_component_document", "_component_el_icon", "_component_el_table", "_component_DataFileManager", "_component_ParamParseDialog", "_component_ParamValueViewer", "_openBlock", "_createElementBlock", "_createElementVNode", "categories", "length", "_createBlock", "modelValue", "selectedTemplatePath", "$event", "size", "options", "cascaderOptions", "placeholder", "style", "onChange", "handleTemplateChange", "clearable", "_createCommentVNode", "_createVNode", "type", "onClick", "selectLocalFile", "default", "_withCtx", "_createTextVNode", "_", "__", "exportCinFile", "loading", "processing", "variables", "searchKeyword", "_toDisplayString", "filteredVariables", "data", "border", "prop", "label", "width", "scope", "parameterValues", "row", "name", "onParameterChange", "isComplexParameter", "editComplexParameter", "getParameterSource", "onParseResult", "handleParseResult", "parseDialogVisible", "currentParseFile", "currentParsedParams", "onApplyParams", "applyParsedParams", "complexParamDialogVisible", "value", "currentComplexParamValue", "readonly", "title", "complexParamTitle", "onConfirm", "handleComplexParamConfirm", "_defineComponent", "__name", "props", "Boolean", "emits", "setup", "__props", "emit", "__emit", "visible", "ref", "displayValue", "formattedValue", "computed", "undefined", "parsed", "JSON", "parse", "stringify", "String", "watch", "newVal", "handleClose", "handleInput", "handleConfirm", "_component_el_dialog", "_createSlots", "rows", "onInput", "fn", "__exports__", "sourceFile", "parsedParams", "searchText", "<PERSON><PERSON><PERSON>", "selectedPara<PERSON>", "paramValueDialogVisible", "currentParamValue", "paramTable", "ecuList", "ecus", "Set", "map", "p", "ecuName", "Array", "from", "sort", "dialogTitle", "fileName", "fileType", "toUpperCase", "filteredParams", "params", "filter", "search", "toLowerCase", "includes", "description", "nextTick", "selectAllCurrentEcuParams", "handleEcuChange", "currentEcuParams", "for<PERSON>ach", "toggleRowSelection", "handleSelectionChange", "selection", "getParamTypeTagType", "paramType", "ParamType", "Integer", "Double", "Json", "Object", "formatValue", "substring", "showParamValue", "param", "applySelectedParams", "ElMessage", "success", "warning", "_component_el_option", "_component_el_select", "_component_el_tag", "footer", "disabled", "_Fragment", "_renderList", "ecu", "prefix", "_unref", "Search", "ref_key", "height", "onSelectionChange", "ParamValueViewer", "selectDataFiles", "dataFiles", "clearAllDataFiles", "getFileTypeTagType", "path", "getStatusTagType", "status", "getStatusText", "SourceFileStatus", "Pending", "Error", "parseDataFile", "id", "Parsed", "viewDataFileDetails", "openDataFileFolder", "removeDataFile", "defineComponent", "loadDataFiles", "async", "response", "appApi", "cinParameter", "getSourceFiles", "error", "console", "selectSourceFiles", "filePaths", "addDataFiles", "addSourceFiles", "newFiles", "file", "existingIndex", "findIndex", "f", "push", "find", "fileId", "Parsing", "parseSourceFile", "updatedFile", "index", "explorer", "openExplorer", "filePath", "removeSourceFile", "fileIndex", "splice", "ElMessageBox", "confirm", "confirmButtonText", "cancelButtonText", "clearSourceFiles", "SourceFileType", "Arxml", "Sddb", "Ldf", "refreshDataFiles", "onMounted", "components", "ParamParseDialog", "DataFileManager", "Document", "templates", "sourceType", "selectedCate<PERSON><PERSON>", "selectedTemplateId", "sourceFilePath", "parsing", "parameterSources", "modifiedParams", "currentComplexParamName", "categorySet", "t", "category", "c", "children", "template", "filteredTemplates", "keyword", "v", "modifiedParamsCount", "hasUnsavedChanges", "loadTemplates", "getTemplates", "onCategoryChange", "onSourceTypeChange", "clear", "selectFile", "parseSelectedFile", "request", "parseFile", "result", "variable", "loadSelectedTemplate", "getTemplatesByCategory", "templateId", "paramName", "add", "processFile", "outputFilePath", "source", "trimmedValue", "trim", "startsWith", "getFileName", "split", "pop", "getAppliedParamsCount", "count", "keys"], "sourceRoot": ""}