﻿using Alsi.Common.Parsers.Sddb;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using Alsi.Common.Utils;

namespace Alsi.Tab.Kit.Core.Services.Capl.Params
{
    internal class DtcsLoader
    {
        public void Load(string ecuNode, string source, SW sw, IParamCollection collection, int order)
        {
            var service19 = sw.Services.Service.FirstOrDefault(x => x.ID == "19");
            if (service19 == null)
            {
                return;
            }

            var dtcIds = new List<uint>();
            var missingFrameDtcs = new List<MissingFrameDtcInformation>();
            foreach (var dtc in service19.DTCS.DTC)
            {
                var id = dtc.ID.Trim();
                if (id.ToUpper().StartsWith("0X"))
                {
                    id = id.Substring(2);
                }

                if (uint.TryParse(id, NumberStyles.HexNumber, null, out var parsedDtcId))
                {
                    dtcIds.Add(parsedDtcId);
                }
            }

            if (dtcIds.Any())
            {
                var value = JsonUtils.Serialize(dtcIds);
                var key = CaplParamConsts.Dtcs;
                collection.AddValue(ecuNode, key, value, source, order);

                foreach (var dtc in dtcIds)
                {
                    if (dtc % 0x100 == 0x87)
                    {
                        missingFrameDtcs.Add(new MissingFrameDtcInformation(dtc));
                    }
                }
                value = JsonUtils.Serialize(missingFrameDtcs.ToArray());
                key = CaplParamConsts.MissingFrameDtcs;
                collection.AddValue(ecuNode, key, value, source, order);
            }
        }
    }
}
