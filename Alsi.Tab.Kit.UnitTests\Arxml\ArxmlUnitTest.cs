﻿using Alsi.Common.Utils;
using Alsi.Tab.Kit.Core.Services.Capl;
using Alsi.Tab.Kit.Core.Services.Capl.Params;
namespace Alsi.Tab.Kit.UnitTests;

public partial class ArxmlUnitTest : ArxmlUnitTestBase
{
    private const string ecuName = "DDM";

    [Fact]
    public void LoadArxml_LoadStandardParams()
    {
        var arxmlPath = GetArxmlPath("SDB22400_DDM_221218_AR-4.2.2_Unflattened.arxml");
        var collection = new CaplParamLoader().Load(arxmlPath);

        var busCanNmDlc = collection.GetValue(ecuName, CaplParamConsts.BUS_CANNM_DLC);
        busCanNmDlc.ShouldBe("8");

        var pncGatewayJson = collection.GetValue(ecuName, CaplParamConsts.PNC_GATEWAY_LIST);
        var pncGateway = JsonUtils.Deserialize<ClusterPncGatewayType[]>(pncGatewayJson);
        pncGateway.Length.ShouldBe(1);
        pncGateway[0].clusterName.ShouldBe("BodyCAN_DDM");
        pncGateway[0].pncGatewayType.ShouldBe(PncGatewayType.None);

        var canDiagResId = collection.GetValue(ecuName, CaplParamConsts.CanDiagResId);
        canDiagResId.ShouldBe("1554");

        var canDiagReqId = collection.GetValue(ecuName, CaplParamConsts.CanDiagReqId);
        canDiagReqId.ShouldBe("1810");

        var dutNmMsgIdList = collection.GetValue(ecuName, CaplParamConsts.DUT_NM_MSG_ID_LIST);
        dutNmMsgIdList.ShouldBe("[1283]");

        var canDiagFuncReqId = collection.GetValue(ecuName, CaplParamConsts.CanDiagFuncReqId);
        canDiagFuncReqId.ShouldBe("2047");
    }

    [Fact]
    public void LoadArxml_LoadE2EFrames()
    {
        var arxmlPath = GetArxmlPath("SDB22400_DDM_221218_AR-4.2.2_Unflattened.arxml");
        var collection = new CaplParamLoader().Load(arxmlPath);

        var e2eCanFrameTxJson = collection.GetValue(ecuName, CaplParamConsts.E2ECanFrameTxList);
        var txFrames = JsonUtils.Deserialize<ClusterE2EFrame[]>(e2eCanFrameTxJson);

        var e2eCanFrameRxJson = collection.GetValue(ecuName, CaplParamConsts.E2ECanFrameRxList);
        var rxFrames = JsonUtils.Deserialize<ClusterE2EFrame[]>(e2eCanFrameRxJson);

        // 验证 TX 数据
        txFrames.Length.ShouldBe(1);
        var txCluster = txFrames[0];
        txCluster.clusterName.ShouldBe("BodyCAN");
        txCluster.e2eFrames.Length.ShouldBe(1);

        var txFrame = txCluster.e2eFrames[0];
        txFrame.frameID.ShouldBe(192u);
        txFrame.cycle.ShouldBe(20);
        txFrame.dataLength.ShouldBe(8);
        txFrame.byteOrder.ShouldBe(E2EByteOrder.Motorola);
        txFrame.frameName.ShouldBe("DdmBodyFr07");
        txFrame.signalGroups.Length.ShouldBe(1);

        var txSignalGroup = txFrame.signalGroups[0];
        txSignalGroup.dataId.ShouldBe(1118u);
        txSignalGroup.signals.Length.ShouldBe(7);

        // 验证 TX 信号
        var txSignals = txSignalGroup.signals;
        txSignals[0].name.ShouldBe("isWinSwtReqCntr_1");
        txSignals[0].startBit.ShouldBe(0u);
        txSignals[0].length.ShouldBe((byte)4);
        txSignals[0].sigType.ShouldBe((SigType)0);

        txSignals[1].name.ShouldBe("isWinSwtReqFrntLe_1");
        txSignals[1].startBit.ShouldBe(4u);
        txSignals[1].length.ShouldBe((byte)3);

        txSignals[2].name.ShouldBe("isWinSwtReqFrntRi_1");
        txSignals[2].startBit.ShouldBe(16u);
        txSignals[2].length.ShouldBe((byte)3);

        txSignals[3].name.ShouldBe("isWinSwtReqReLe_1");
        txSignals[3].startBit.ShouldBe(21u);
        txSignals[3].length.ShouldBe((byte)3);

        txSignals[4].name.ShouldBe("isWinSwtReqReRi_1");
        txSignals[4].startBit.ShouldBe(29u);
        txSignals[4].length.ShouldBe((byte)3);

        txSignals[5].name.ShouldBe("isWinSwtReqChks_1");
        txSignals[5].startBit.ShouldBe(8u);
        txSignals[5].length.ShouldBe((byte)8);

        txSignals[6].name.ShouldBe("BuiltInUpdateBit");
        txSignals[6].startBit.ShouldBe(7u);
        txSignals[6].length.ShouldBe((byte)1);
        txSignals[6].sigType.ShouldBe(SigType.UB);

        // 验证 RX 数据
        rxFrames.Length.ShouldBe(1);
        var rxCluster = rxFrames[0];
        rxCluster.clusterName.ShouldBe("BodyCAN");
        rxCluster.e2eFrames.Length.ShouldBe(2);

        // 验证第一个 RX 帧 (CemBodyFr02)
        var rxFrame1 = rxCluster.e2eFrames[0];
        rxFrame1.frameID.ShouldBe(64u);
        rxFrame1.cycle.ShouldBe(0);
        rxFrame1.dataLength.ShouldBe(8);
        rxFrame1.byteOrder.ShouldBe(E2EByteOrder.Motorola);
        rxFrame1.frameName.ShouldBe("CemBodyFr02");
        rxFrame1.signalGroups.Length.ShouldBe(1);

        var rxSignalGroup1 = rxFrame1.signalGroups[0];
        rxSignalGroup1.dataId.ShouldBe(54u);
        rxSignalGroup1.signals.Length.ShouldBe(4);

        var rxSignals1 = rxSignalGroup1.signals;
        rxSignals1[0].name.ShouldBe("isVehMtnStCntr");
        rxSignals1[0].startBit.ShouldBe(3u);
        rxSignals1[0].length.ShouldBe((byte)4);

        rxSignals1[1].name.ShouldBe("isVehMtnStVehMtnSt");
        rxSignals1[1].startBit.ShouldBe(0u);
        rxSignals1[1].length.ShouldBe((byte)3);

        rxSignals1[2].name.ShouldBe("isVehMtnStChks");
        rxSignals1[2].startBit.ShouldBe(8u);
        rxSignals1[2].length.ShouldBe((byte)8);

        rxSignals1[3].name.ShouldBe("BuiltInUpdateBit");
        rxSignals1[3].startBit.ShouldBe(7u);
        rxSignals1[3].length.ShouldBe((byte)1);
        rxSignals1[3].sigType.ShouldBe(SigType.UB);

        // 验证第二个 RX 帧 (CemBodyFr03)
        var rxFrame2 = rxCluster.e2eFrames[1];
        rxFrame2.frameID.ShouldBe(118u);
        rxFrame2.cycle.ShouldBe(0);
        rxFrame2.dataLength.ShouldBe(8);
        rxFrame2.byteOrder.ShouldBe(E2EByteOrder.Motorola);
        rxFrame2.frameName.ShouldBe("CemBodyFr03");
        rxFrame2.signalGroups.Length.ShouldBe(1);

        var rxSignalGroup2 = rxFrame2.signalGroups[0];
        rxSignalGroup2.dataId.ShouldBe(158u);
        rxSignalGroup2.signals.Length.ShouldBe(4);

        var rxSignals2 = rxSignalGroup2.signals;
        rxSignals2[0].name.ShouldBe("isActvnOfIndcrIndcrOutCntr");
        rxSignals2[0].startBit.ShouldBe(0u);
        rxSignals2[0].length.ShouldBe((byte)4);

        rxSignals2[1].name.ShouldBe("isActvnOfIndcrIndcrOut");
        rxSignals2[1].startBit.ShouldBe(4u);
        rxSignals2[1].length.ShouldBe((byte)2);

        rxSignals2[2].name.ShouldBe("isActvnOfIndcrIndcrOutChks");
        rxSignals2[2].startBit.ShouldBe(8u);
        rxSignals2[2].length.ShouldBe((byte)8);

        rxSignals2[3].name.ShouldBe("BuiltInUpdateBit");
        rxSignals2[3].startBit.ShouldBe(6u);
        rxSignals2[3].length.ShouldBe((byte)1);
        rxSignals2[3].sigType.ShouldBe(SigType.UB);
    }
}
