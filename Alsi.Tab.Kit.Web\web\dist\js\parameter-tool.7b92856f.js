"use strict";(self["webpackChunktab_kit_web"]=self["webpackChunktab_kit_web"]||[]).push([[771],{9312:function(e,a,l){l.r(a),l.d(a,{default:function(){return Y}});var t=l(6768),r=l(4232);const s={class:"parameter-tool"},o={class:"main-content"},n={class:"cin-parameters-section"},u={class:"cin-operations-content"},i={class:"operation-row"},c={key:0,class:"search-bar"},d={class:"filter-info"},m={class:"parameters-table"},p={key:0,class:"parameter-source"},v={key:1,class:"no-source"};function g(e,a,l,g,y,f){const h=(0,t.g2)("el-cascader"),k=(0,t.g2)("el-button"),w=(0,t.g2)("el-divider"),b=(0,t.g2)("el-input"),F=(0,t.g2)("el-table-column"),P=(0,t.g2)("document"),_=(0,t.g2)("el-icon"),C=(0,t.g2)("el-table"),V=(0,t.g2)("DataFileManager"),L=(0,t.g2)("ParamParseDialog"),S=(0,t.g2)("ParamValueViewer");return(0,t.uX)(),(0,t.CE)("div",s,[(0,t.Lk)("div",o,[(0,t.Lk)("div",n,[a[7]||(a[7]=(0,t.Lk)("div",{class:"section-header"},[(0,t.Lk)("h4",null,"CIN 参数列表"),(0,t.Lk)("div",{class:"header-actions"})],-1)),(0,t.Lk)("div",u,[(0,t.Lk)("div",i,[e.categories.length>0?((0,t.uX)(),(0,t.Wv)(h,{key:0,modelValue:e.selectedTemplatePath,"onUpdate:modelValue":a[0]||(a[0]=a=>e.selectedTemplatePath=a),size:"small",options:e.cascaderOptions,placeholder:"选择 CIN 模板",style:{width:"260px","margin-right":"16px"},onChange:e.handleTemplateChange,clearable:""},null,8,["modelValue","options","onChange"])):(0,t.Q3)("",!0),(0,t.bF)(k,{type:"primary",size:"small",onClick:e.selectLocalFile},{default:(0,t.k6)(()=>a[4]||(a[4]=[(0,t.eW)("选择本地 CIN 文件")])),_:1,__:[4]},8,["onClick"]),(0,t.bF)(k,{type:"primary",size:"small",onClick:e.exportCinFile,style:{"margin-left":"auto"},loading:e.processing},{default:(0,t.k6)(()=>a[5]||(a[5]=[(0,t.eW)("导出 CIN")])),_:1,__:[5]},8,["onClick","loading"])])]),(0,t.bF)(w,{style:{margin:"8px 0"}}),e.variables.length>0?((0,t.uX)(),(0,t.CE)("div",c,[(0,t.bF)(b,{modelValue:e.searchKeyword,"onUpdate:modelValue":a[1]||(a[1]=a=>e.searchKeyword=a),size:"small",placeholder:"搜索参数名称...",clearable:"",style:{width:"260px"}},null,8,["modelValue"]),(0,t.Lk)("div",d,[(0,t.Lk)("span",null,"共 "+(0,r.v_)(e.filteredVariables.length)+" / "+(0,r.v_)(e.variables.length)+" 个参数",1)])])):(0,t.Q3)("",!0),(0,t.Lk)("div",m,[(0,t.bF)(C,{data:e.filteredVariables,border:"",style:{width:"100%",height:"100%"},size:"small"},{default:(0,t.k6)(()=>[(0,t.bF)(F,{prop:"name",label:"参数名",width:"200","show-overflow-tooltip":""}),(0,t.bF)(F,{prop:"type",label:"数据类型 (CAPL)",width:"200","show-overflow-tooltip":""}),(0,t.bF)(F,{label:"参数值","min-width":"200"},{default:(0,t.k6)(a=>[(0,t.bF)(b,{modelValue:e.parameterValues[a.row.name],"onUpdate:modelValue":l=>e.parameterValues[a.row.name]=l,placeholder:"输入参数值",size:"small",onChange:l=>e.onParameterChange(a.row.name),readonly:e.isComplexParameter(a.row)},null,8,["modelValue","onUpdate:modelValue","onChange","readonly"])]),_:1}),(0,t.bF)(F,{label:"操作",width:"80",align:"center"},{default:(0,t.k6)(l=>[(0,t.bF)(k,{type:"text",size:"small",onClick:a=>e.editComplexParameter(l.row)},{default:(0,t.k6)(()=>a[6]||(a[6]=[(0,t.eW)(" 编辑 ")])),_:2,__:[6]},1032,["onClick"])]),_:1}),(0,t.bF)(F,{label:"参数值来源",width:"150","show-overflow-tooltip":""},{default:(0,t.k6)(a=>[e.getParameterSource(a.row.name)?((0,t.uX)(),(0,t.CE)("div",p,[(0,t.bF)(_,null,{default:(0,t.k6)(()=>[(0,t.bF)(P)]),_:1}),(0,t.Lk)("span",null,(0,r.v_)(e.getParameterSource(a.row.name)),1)])):((0,t.uX)(),(0,t.CE)("span",v,"CIN 文件"))]),_:1})]),_:1},8,["data"])])]),(0,t.bF)(V,{onParseResult:e.handleParseResult},null,8,["onParseResult"])]),(0,t.bF)(L,{modelValue:e.parseDialogVisible,"onUpdate:modelValue":a[2]||(a[2]=a=>e.parseDialogVisible=a),"source-file":e.currentParseFile,"parsed-params":e.currentParsedParams,onApplyParams:e.applyParsedParams},null,8,["modelValue","source-file","parsed-params","onApplyParams"]),(0,t.bF)(S,{modelValue:e.complexParamDialogVisible,"onUpdate:modelValue":a[3]||(a[3]=a=>e.complexParamDialogVisible=a),value:e.currentComplexParamValue,readonly:!1,title:e.complexParamTitle,onConfirm:e.handleComplexParamConfirm},null,8,["modelValue","value","title","onConfirm"])])}l(8111),l(2489),l(116),l(7588),l(1701),l(7642),l(8004),l(3853),l(5876),l(2475),l(5024),l(1698);var y=l(144),f=l(1021),h=l(1219),k=l(7477);const w={class:"param-value-viewer"},b={class:"dialog-footer"};var F=(0,t.pM)({__name:"ParamValueViewer",props:{modelValue:{type:Boolean},value:{},readonly:{type:Boolean,default:!1},title:{default:"参数值"}},emits:["update:modelValue","update:value","confirm"],setup(e,{emit:a}){const l=e,r=a,s=(0,y.KR)(!1),o=(0,y.KR)(""),n=(0,t.EW)(()=>{if(null===l.value||void 0===l.value)return"";if("string"===typeof l.value)try{const e=JSON.parse(l.value);return JSON.stringify(e,null,2)}catch{return l.value}else try{return JSON.stringify(l.value,null,2)}catch{return String(l.value)}});(0,t.wB)(()=>l.modelValue,e=>{s.value=e,e&&(o.value=n.value)}),(0,t.wB)(s,e=>{r("update:modelValue",e)}),(0,t.wB)(()=>l.value,()=>{s.value&&(o.value=n.value)});const u=()=>{s.value=!1},i=e=>{l.readonly||r("update:value",e)},c=()=>{l.readonly||r("confirm",o.value),u()};return(e,a)=>{const l=(0,t.g2)("el-input"),r=(0,t.g2)("el-button"),n=(0,t.g2)("el-dialog");return(0,t.uX)(),(0,t.Wv)(n,{modelValue:s.value,"onUpdate:modelValue":a[1]||(a[1]=e=>s.value=e),title:e.title,width:"60%","before-close":u,"destroy-on-close":""},(0,t.eX)({default:(0,t.k6)(()=>[(0,t.Lk)("div",w,[(0,t.bF)(l,{modelValue:o.value,"onUpdate:modelValue":a[0]||(a[0]=e=>o.value=e),type:"textarea",rows:15,readonly:e.readonly,placeholder:e.readonly?"":"请输入参数值...",style:{"font-family":"'Courier New', monospace"},onInput:i},null,8,["modelValue","readonly","placeholder"])])]),_:2},[e.readonly?void 0:{name:"footer",fn:(0,t.k6)(()=>[(0,t.Lk)("div",b,[(0,t.bF)(r,{onClick:u},{default:(0,t.k6)(()=>a[2]||(a[2]=[(0,t.eW)("取消")])),_:1,__:[2]}),(0,t.bF)(r,{type:"primary",onClick:c},{default:(0,t.k6)(()=>a[3]||(a[3]=[(0,t.eW)("确定")])),_:1,__:[3]})])]),key:"0"}]),1032,["modelValue","title"])}}}),P=l(1241);const _=(0,P.A)(F,[["__scopeId","data-v-089a4f20"]]);var C=_;const V={class:"dialog-content"},L={class:"filter-section"},S={class:"selection-info"},x={class:"param-list"},T=["title"],R={class:"dialog-footer"};var W=(0,t.pM)({__name:"ParamParseDialog",props:{modelValue:{type:Boolean},sourceFile:{},parsedParams:{}},emits:["update:modelValue","apply-params"],setup(e,{emit:a}){const l=e,s=a,o=(0,y.KR)(!1),n=(0,y.KR)(""),u=(0,y.KR)(""),i=(0,y.KR)([]),c=(0,y.KR)(!1),d=(0,y.KR)(""),m=(0,y.KR)(),p=(0,t.EW)(()=>{const e=new Set(l.parsedParams.map(e=>e.ecuName));return Array.from(e).sort()}),v=(0,t.EW)(()=>{if(!l.sourceFile)return"参数解析结果";const e=l.sourceFile.fileName;return`参数解析结果 - ${e}`}),g=(0,t.EW)(()=>{let e=l.parsedParams;if(u.value&&(e=e.filter(e=>e.ecuName===u.value)),n.value){const a=n.value.toLowerCase();e=e.filter(e=>e.name.toLowerCase().includes(a)||e.description.toLowerCase().includes(a))}return e});(0,t.wB)(()=>l.modelValue,e=>{o.value=e,e&&(n.value="",i.value=[],p.value.length>0&&(u.value=p.value[0],(0,t.dY)(()=>{w()})))}),(0,t.wB)(o,e=>{s("update:modelValue",e)}),(0,t.wB)(u,()=>{(0,t.dY)(()=>{w()})});const f=()=>{w()},w=()=>{if(m.value){const e=g.value;e.forEach(e=>{m.value.toggleRowSelection(e,!0)})}},b=()=>{o.value=!1},F=e=>{i.value=e},P=e=>null===e||void 0===e?"":String(e),_=e=>{d.value=e.value,c.value=!0},W=()=>{0!==i.value.length?(s("apply-params",i.value),h.nk.success(`成功应用 ${i.value.length} 个参数`),b()):h.nk.warning("请先选择要应用的参数")};return(e,a)=>{const l=(0,t.g2)("el-option"),s=(0,t.g2)("el-select"),h=(0,t.g2)("el-icon"),w=(0,t.g2)("el-input"),D=(0,t.g2)("el-table-column"),K=(0,t.g2)("el-button"),E=(0,t.g2)("el-table"),z=(0,t.g2)("el-dialog");return(0,t.uX)(),(0,t.Wv)(z,{modelValue:o.value,"onUpdate:modelValue":a[3]||(a[3]=e=>o.value=e),title:v.value,width:"80%","before-close":b,"destroy-on-close":""},{footer:(0,t.k6)(()=>[(0,t.Lk)("div",R,[(0,t.bF)(K,{onClick:b},{default:(0,t.k6)(()=>a[5]||(a[5]=[(0,t.eW)("取消")])),_:1,__:[5]}),(0,t.bF)(K,{type:"primary",onClick:W,disabled:0===i.value.length},{default:(0,t.k6)(()=>[(0,t.eW)(" 应用参数 ("+(0,r.v_)(i.value.length)+") ",1)]),_:1},8,["disabled"])])]),default:(0,t.k6)(()=>[(0,t.Lk)("div",V,[(0,t.Lk)("div",L,[(0,t.bF)(s,{modelValue:u.value,"onUpdate:modelValue":a[0]||(a[0]=e=>u.value=e),size:"small",placeholder:"选择 ECU",style:{width:"100px"},onChange:f},{default:(0,t.k6)(()=>[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(p.value,e=>((0,t.uX)(),(0,t.Wv)(l,{key:e,label:e,value:e},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),(0,t.bF)(w,{modelValue:n.value,"onUpdate:modelValue":a[1]||(a[1]=e=>n.value=e),placeholder:"搜索参数名称...",clearable:"",size:"small",style:{width:"260px"}},{prefix:(0,t.k6)(()=>[(0,t.bF)(h,null,{default:(0,t.k6)(()=>[(0,t.bF)((0,y.R1)(k.Search))]),_:1})]),_:1},8,["modelValue"]),(0,t.Lk)("div",S," 已选择 "+(0,r.v_)(i.value.length)+"/"+(0,r.v_)(g.value.length)+" 个参数 ",1)]),(0,t.Lk)("div",x,[(0,t.bF)(E,{ref_key:"paramTable",ref:m,data:g.value,height:"400",onSelectionChange:F,"row-key":"name",size:"small"},{default:(0,t.k6)(()=>[(0,t.bF)(D,{type:"selection",width:"55"}),(0,t.bF)(D,{prop:"name",label:"参数名",width:"200","show-overflow-tooltip":""}),(0,t.bF)(D,{prop:"paramType",label:"数据类型",width:"200","show-overflow-tooltip":""}),(0,t.bF)(D,{prop:"value",label:"参数值","min-width":"200"},{default:(0,t.k6)(({row:e})=>[(0,t.Lk)("span",{class:"param-value-text",title:P(e.value)},(0,r.v_)(P(e.value)),9,T)]),_:1}),(0,t.bF)(D,{label:"操作",width:"80",align:"center"},{default:(0,t.k6)(({row:e})=>[(0,t.bF)(K,{type:"text",size:"small",onClick:a=>_(e)},{default:(0,t.k6)(()=>a[4]||(a[4]=[(0,t.eW)(" 查看 ")])),_:2,__:[4]},1032,["onClick"])]),_:1})]),_:1},8,["data"])])]),(0,t.bF)(C,{modelValue:c.value,"onUpdate:modelValue":a[2]||(a[2]=e=>c.value=e),value:d.value,readonly:!0,title:"参数值"},null,8,["modelValue","value"])]),_:1},8,["modelValue","title"])}}});const D=(0,P.A)(W,[["__scopeId","data-v-9102454e"]]);var K=D;const E={class:"data-files-section"},z={class:"section-header"},I={class:"header-actions"},O={class:"data-files-table"},Q=["title"],N={class:"name"},A={class:"action-buttons"};function U(e,a,l,s,o,n){const u=(0,t.g2)("el-button"),i=(0,t.g2)("el-tag"),c=(0,t.g2)("el-table-column"),d=(0,t.g2)("el-table");return(0,t.uX)(),(0,t.CE)("div",E,[(0,t.Lk)("div",z,[a[2]||(a[2]=(0,t.Lk)("h4",null,"数据文件 (ARXML/SDDB/LDF)",-1)),(0,t.Lk)("div",I,[(0,t.bF)(u,{size:"small",type:"primary",onClick:e.selectDataFiles},{default:(0,t.k6)(()=>a[0]||(a[0]=[(0,t.eW)("添加文件")])),_:1,__:[0]},8,["onClick"]),e.dataFiles.length>0?((0,t.uX)(),(0,t.Wv)(u,{key:0,size:"small",type:"danger",onClick:e.clearAllDataFiles},{default:(0,t.k6)(()=>a[1]||(a[1]=[(0,t.eW)("清空")])),_:1,__:[1]},8,["onClick"])):(0,t.Q3)("",!0)])]),(0,t.Lk)("div",O,[(0,t.bF)(d,{data:e.dataFiles,border:"",style:{width:"100%"},size:"small"},{default:(0,t.k6)(()=>[(0,t.bF)(c,{label:"类型",width:"80"},{default:(0,t.k6)(a=>[(0,t.bF)(i,{type:e.getFileTypeTagType(a.row.fileType),size:"small"},{default:(0,t.k6)(()=>[(0,t.eW)((0,r.v_)(a.row.fileType.toUpperCase()),1)]),_:2},1032,["type"])]),_:1}),(0,t.bF)(c,{label:"文件名","min-width":"200"},{default:(0,t.k6)(e=>[(0,t.Lk)("div",{class:"file-name",title:e.row.path},[(0,t.Lk)("span",N,(0,r.v_)(e.row.fileName),1)],8,Q)]),_:1}),(0,t.bF)(c,{label:"状态",width:"100"},{default:(0,t.k6)(a=>[(0,t.bF)(i,{type:e.getStatusTagType(a.row.status),size:"small"},{default:(0,t.k6)(()=>[(0,t.eW)((0,r.v_)(e.getStatusText(a.row.status)),1)]),_:2},1032,["type"])]),_:1}),(0,t.bF)(c,{label:"操作",width:"300"},{default:(0,t.k6)(l=>[(0,t.Lk)("div",A,[l.row.status===e.SourceFileStatus.Pending||l.row.status===e.SourceFileStatus.Error?((0,t.uX)(),(0,t.Wv)(u,{key:0,type:"primary",size:"small",onClick:a=>e.parseDataFile(l.row.id)},{default:(0,t.k6)(()=>a[3]||(a[3]=[(0,t.eW)(" 解析参数 ")])),_:2,__:[3]},1032,["onClick"])):(0,t.Q3)("",!0),l.row.status===e.SourceFileStatus.Parsed?((0,t.uX)(),(0,t.Wv)(u,{key:1,type:"success",size:"small",onClick:a=>e.viewDataFileDetails(l.row)},{default:(0,t.k6)(()=>a[4]||(a[4]=[(0,t.eW)(" 查看结果 ")])),_:2,__:[4]},1032,["onClick"])):(0,t.Q3)("",!0),(0,t.bF)(u,{type:"warning",size:"small",onClick:a=>e.openDataFileFolder(l.row.path)},{default:(0,t.k6)(()=>a[5]||(a[5]=[(0,t.eW)(" 打开文件夹 ")])),_:2,__:[5]},1032,["onClick"]),(0,t.bF)(u,{type:"danger",size:"small",onClick:a=>e.removeDataFile(l.row.id)},{default:(0,t.k6)(()=>a[6]||(a[6]=[(0,t.eW)(" 移除 ")])),_:2,__:[6]},1032,["onClick"])])]),_:1})]),_:1},8,["data"])])])}l(4114);var X=l(2933),B=(0,t.pM)({name:"DataFileManager",emits:["parse-result"],setup(e,{emit:a}){const l=(0,y.KR)([]),r=async()=>{try{const e=await f.GQ.cinParameter.getSourceFiles();l.value=e.data}catch(e){console.error("加载数据文件失败:",e),h.nk.error("加载数据文件失败")}},s=async()=>{try{const e=await f.GQ.cinParameter.selectSourceFiles(),a=e.data;a&&a.length>0&&(await o(a),h.nk.success(`成功添加 ${a.length} 个参数数据文件`))}catch(e){console.error("选择文件失败:",e),h.nk.error("选择文件失败")}},o=async e=>{try{const a=await f.GQ.cinParameter.addSourceFiles({filePaths:e}),t=a.data;return t.forEach(e=>{const a=l.value.findIndex(a=>a.id===e.id);a>=0?l.value[a]=e:l.value.push(e)}),t}catch(a){throw console.error("添加文件失败:",a),a}},n=async e=>{try{const t=l.value.find(a=>a.id===e);t&&(t.status=f.O0.Parsing);const r=await f.GQ.cinParameter.parseSourceFile({fileId:e}),s=r.data,o=l.value.findIndex(a=>a.id===e);o>=0&&(l.value[o]=s),h.nk.success(`参数解析完成，共解析出 ${s.parsedParams?.length||0} 个参数`),s.parsedParams&&s.parsedParams.length>0&&a("parse-result",s,s.parsedParams)}catch(t){console.error("解析文件失败:",t),h.nk.error("参数解析失败");const a=l.value.find(a=>a.id===e);a&&(a.status=f.O0.Error)}},u=e=>{e.parsedParams&&e.parsedParams.length>0?a("parse-result",e,e.parsedParams):h.nk.warning("该文件暂无解析结果")},i=async e=>{try{await f.GQ.explorer.openExplorer(e)}catch(a){console.error("打开文件夹失败:",a),h.nk.error("打开文件夹失败")}},c=async e=>{try{await f.GQ.cinParameter.removeSourceFile({fileId:e});const a=l.value.findIndex(a=>a.id===e);a>=0&&(l.value.splice(a,1),h.nk.success("已移除数据文件"))}catch(a){console.error("移除文件失败:",a),h.nk.error("移除文件失败")}},d=async()=>{try{await X.s.confirm("确定要清空所有参数数据文件吗？","确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await f.GQ.cinParameter.clearSourceFiles(),l.value=[],h.nk.success("已清空所有参数数据文件")}catch(e){"cancel"!==e&&(console.error("清空文件失败:",e),h.nk.error("清空文件失败"))}},m=e=>{switch(e){case f.yJ.Arxml:return"primary";case f.yJ.Sddb:return"success";case f.yJ.Ldf:return"warning";default:return""}},p=e=>{switch(e){case f.O0.Pending:return"";case f.O0.Parsing:return"warning";case f.O0.Parsed:return"success";case f.O0.Error:return"danger";default:return""}},v=e=>{switch(e){case f.O0.Pending:return"待解析";case f.O0.Parsing:return"解析中";case f.O0.Parsed:return"已解析";case f.O0.Error:return"解析失败";default:return e}},g=()=>{r()};return(0,t.sV)(()=>{r()}),{dataFiles:l,selectDataFiles:s,parseDataFile:n,viewDataFileDetails:u,openDataFileFolder:i,removeDataFile:c,clearAllDataFiles:d,getFileTypeTagType:m,getStatusTagType:p,getStatusText:v,refreshDataFiles:g,SourceFileStatus:f.O0}}});const G=(0,P.A)(B,[["render",U],["__scopeId","data-v-3b908206"]]);var J=G,M=(0,t.pM)({name:"ParameterToolView",components:{ParamParseDialog:K,DataFileManager:J,ParamValueViewer:C,Document:k.Document},setup(){const e=(0,y.KR)([]),a=(0,y.KR)("template"),l=(0,y.KR)(""),r=(0,y.KR)(""),s=(0,y.KR)([]),o=(0,y.KR)(""),n=(0,y.KR)([]),u=(0,y.KR)({}),i=(0,y.KR)(""),c=(0,y.KR)(""),d=(0,y.KR)(!1),m=(0,y.KR)(!1),p=(0,y.KR)(!1),v=(0,y.KR)(null),g=(0,y.KR)([]),w=(0,y.KR)({}),b=(0,y.KR)(new Set),F=(0,y.KR)(!1),P=(0,y.KR)(""),_=(0,y.KR)(""),C=(0,t.EW)(()=>`编辑参数 - ${_.value}`),V=(0,t.EW)(()=>{const a=new Set(e.value.map(e=>e.category));return Array.from(a).filter(e=>e)}),L=(0,t.EW)(()=>V.value.map(a=>({value:a,label:a,children:e.value.filter(e=>e.category===a).map(e=>({value:e.id,label:e.name}))}))),S=(0,t.EW)(()=>l.value?e.value.filter(e=>e.category===l.value):[]),x=(0,t.EW)(()=>{if(!c.value)return n.value;const e=c.value.toLowerCase();return n.value.filter(a=>a.name.toLowerCase().includes(e))}),T=(0,t.EW)(()=>b.value.size),R=(0,t.EW)(()=>b.value.size>0),W=async()=>{try{const a=await f.GQ.cinParameter.getTemplates();e.value=a.data,V.value.length>0&&(l.value=V.value[0],K())}catch(a){console.error("加载模板失败:",a)}},D=()=>{n.value=[],u.value={},i.value="",b.value.clear()},K=()=>{S.value.length>0?r.value=S.value[0].id:r.value=""},E=async()=>{try{const e=await f.GQ.cinParameter.selectFile();if(!e.data)return;r.value="",l.value="",s.value=[],o.value=e.data,await z()}catch(e){console.error("选择文件失败:",e)}},z=async()=>{if(o.value){d.value=!0;try{const e={sourceType:"file",filePath:o.value},a=await f.GQ.cinParameter.parseFile(e),l=a.data;n.value=l.variables,i.value=l.sourceFilePath,u.value={},l.variables.forEach(e=>{u.value[e.name]=e.value||""}),b.value.clear(),h.nk.success(`成功解析 ${l.variables.length} 个参数`)}catch(e){console.error("解析文件失败:",e),h.nk.error("解析文件失败")}finally{d.value=!1}}},I=e=>{e&&2===e.length&&(l.value=e[0],r.value=e[1],Q())},O=a=>e.value.filter(e=>e.category===a),Q=async()=>{if(r.value){d.value=!0;try{const e={sourceType:"template",templateId:r.value,filePath:""},a=await f.GQ.cinParameter.parseFile(e),l=a.data;n.value=l.variables,i.value=l.sourceFilePath,u.value={},l.variables.forEach(e=>{u.value[e.name]=e.value||""}),b.value.clear(),h.nk.success(`成功加载模板 ${l.variables.length} 个参数`)}catch(e){console.error("加载模板失败:",e),h.nk.error("加载模板失败")}finally{d.value=!1}}},N=e=>{b.value.add(e)},A=async()=>{m.value=!0;try{const e={sourceType:a.value,templateId:"template"===a.value?r.value:void 0,filePath:"file"===a.value?o.value:"",parameterValues:u.value},l=await f.GQ.cinParameter.processFile(e),t=l.data;h.nk.success("文件导出成功！"),b.value.clear(),await f.GQ.explorer.openExplorer(t.outputFilePath)}catch(e){console.error("导出文件失败:",e),h.nk.error("导出文件失败")}finally{m.value=!1}},U=(e,a)=>{v.value=e,g.value=a,p.value=!0},X=e=>{e.forEach(e=>{const a=n.value.find(a=>a.name.toLowerCase()===e.name.toLowerCase());a&&(u.value[a.name]=String(e.value),w.value[a.name]=e.source,b.value.add(a.name))}),h.nk.success(`成功应用 ${e.length} 个参数`)},B=e=>w.value[e],G=e=>{const a=u.value[e.name];if(!a)return!1;const l=a.trim();if(l.startsWith("{")&&l.includes(","))return!0;if(l.startsWith("{")||l.startsWith("["))try{return JSON.parse(l),!0}catch{return l.includes(",")||l.includes("{")}const t=e.type.toLowerCase();return t.includes("struct")||t.includes("array")||t.includes("[]")},J=e=>{_.value=e.name,P.value=u.value[e.name]||"",F.value=!0},M=e=>{_.value&&(u.value[_.value]=e,N(_.value))},$=e=>e&&e.split(/[\\/]/).pop()||"",Y=e=>{switch(e){case f.yJ.Arxml:return"primary";case f.yJ.Sddb:return"success";case f.yJ.Ldf:return"warning";default:return""}},j=e=>{let a=0;return Object.keys(w.value).forEach(l=>{w.value[l]===e.fileName&&a++}),a};return(0,t.sV)(async()=>{await W()}),{templates:e,sourceType:a,selectedCategory:l,selectedTemplateId:r,selectedTemplatePath:s,filePath:o,variables:n,parameterValues:u,sourceFilePath:i,searchKeyword:c,parsing:d,processing:m,categories:V,cascaderOptions:L,filteredTemplates:S,filteredVariables:x,modifiedParamsCount:T,hasUnsavedChanges:R,onSourceTypeChange:D,onCategoryChange:K,selectLocalFile:E,handleTemplateChange:I,getTemplatesByCategory:O,loadSelectedTemplate:Q,onParameterChange:N,exportCinFile:A,getFileName:$,getFileTypeTagType:Y,getAppliedParamsCount:j,parseDialogVisible:p,currentParseFile:v,currentParsedParams:g,handleParseResult:U,applyParsedParams:X,getParameterSource:B,isComplexParameter:G,editComplexParameter:J,handleComplexParamConfirm:M,complexParamDialogVisible:F,currentComplexParamValue:P,complexParamTitle:C,Document:k.Document,SourceFileStatus:f.O0}}});const $=(0,P.A)(M,[["render",g],["__scopeId","data-v-38b01a87"]]);var Y=$}}]);
//# sourceMappingURL=parameter-tool.7b92856f.js.map