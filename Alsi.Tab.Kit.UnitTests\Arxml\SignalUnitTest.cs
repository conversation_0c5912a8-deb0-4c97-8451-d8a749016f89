﻿using Alsi.Common.Parsers.Arxml.Models;
using Alsi.Common.Utils;
using Alsi.Tab.Kit.Core.Services.Capl;
using Alsi.Tab.Kit.Core.Services.Capl.Params;
namespace Alsi.Tab.Kit.UnitTests;

public partial class SignalUnitTest : ArxmlUnitTestBase
{
    private const string ecuName = "ZCUDM";

    [Fact]
    public void LoadArxml_WhenParsingSignalRouting_ShouldAssignCorrectValues()
    {
        var arxmlPath = GetArxmlPath("SDB2243301_ZCUDM_240409_AR-4.2.2_Unflattened.arxml");
        var collection = new CaplParamLoader().Load(arxmlPath);

        var json = collection.GetValue(ecuName, CaplParamConsts.SignalRouting);
        var signalRoutings = JsonUtils.Deserialize<SignalRouting[]>(json);

        signalRoutings.Length.ShouldBeGreaterThan(0);

        var sourceSignal = signalRoutings[0].SourceSignal;
        var targetSignal = signalRoutings[0].TargetSignal;

        sourceSignal.BusType.ShouldBe(ClusterBusType.Can);
        sourceSignal.ChannelName.ShouldBe("ZCUD_CAN1");
        sourceSignal.Dir.ShouldBe(Direction.Rx);
        sourceSignal.FrameName.ShouldBe("FrTrSrsZCUDCAN1Fr05");
        sourceSignal.Id.ToString("X").ShouldBe("21");
        sourceSignal.Length.ShouldBe(1);
        sourceSignal.PduName.ShouldBe("PduTrSrsZCUDCAN1SignalPdu05");
        sourceSignal.SignalGroupName.ShouldBe("");
        sourceSignal.SignalName.ShouldBe("isBltLockStSafeAtDrvrBltLockStErrSts_0");
        sourceSignal.StartPosition.ShouldBe(0x0c);
        sourceSignal.SystemSignalName.ShouldBe("BltLockStSafeAtDrvrBltLockStErrSts");
        sourceSignal.UpdateBit.ShouldBe(-1);

        targetSignal.BusType.ShouldBe(ClusterBusType.CanFd);
        targetSignal.ChannelName.ShouldBe("ZCU_CANFD1");
        targetSignal.Dir.ShouldBe(Direction.Tx);
        targetSignal.FrameName.ShouldBe("FrTrZcudZCUCANFD1Fr02");
        targetSignal.Id.ToString("X").ShouldBe("105");
        targetSignal.Length.ShouldBe(1);
        targetSignal.PduName.ShouldBe("PduTrZcudZCUCANFD1SignalIPdu02");
        targetSignal.SignalGroupName.ShouldBe("");
        targetSignal.SignalName.ShouldBe("isBltLockStSafeAtDrvrBltLockStErrSts_1");
        targetSignal.StartPosition.ShouldBe(0x0c);
        targetSignal.SystemSignalName.ShouldBe("BltLockStSafeAtDrvrBltLockStErrSts");
        targetSignal.UpdateBit.ShouldBe(-1);


        foreach (var signalRouting in signalRoutings)
        {
            var signals = new List<SignalToRoute> { signalRouting.SourceSignal, signalRouting.TargetSignal };
            foreach (var signal in signals)
            {
                signal.BusType.ShouldBeAssignableTo<ClusterBusType>();
                signal.ChannelName.ShouldNotBeNullOrEmpty();
                signal.FrameName.ShouldNotBeNullOrEmpty();
                signal.Dir.ShouldBeAssignableTo<Direction>();
                signal.PduName.ShouldNotBeNullOrEmpty();
                signal.SystemSignalName.ShouldNotBeNullOrEmpty();
                signal.SignalGroupName.ShouldBe("");
            }
        }
    }
}
