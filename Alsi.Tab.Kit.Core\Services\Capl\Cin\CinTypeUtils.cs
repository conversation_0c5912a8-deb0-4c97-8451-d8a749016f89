﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Reflection;
using System.Text;

namespace Alsi.Tab.Kit.Core.Services.Capl.Cin
{
    public static class CinTypeUtils
    {
        #region 类型检查方法

        private static bool IsDouble(Type type)
        {
            return type == typeof(float) || type == typeof(double);
        }

        private static bool IsNumber(Type type)
        {
            return type == typeof(byte) || type == typeof(sbyte) ||
                   type == typeof(short) || type == typeof(ushort) ||
                   type == typeof(int) || type == typeof(uint) ||
                   type == typeof(long) || type == typeof(ulong);
        }

        private static bool IsSimpleType(Type type)
        {
            return IsNumber(type) || IsDouble(type) || type == typeof(string) || type == typeof(bool) || type == typeof(char) || type.IsEnum;
        }

        private static bool IsArray(Type type)
        {
            return type.IsArray || (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(List<>));
        }

        #endregion

        #region 解析方法

        /// <summary>
        /// 解析CIN格式字符串为指定类型的对象
        /// </summary>
        public static T ParseCinValue<T>(string content)
        {
            return (T)ParseCinValue(content, typeof(T));
        }

        /// <summary>
        /// 解析CIN格式字符串为指定类型的对象
        /// </summary>
        public static object ParseCinValue(string content, Type targetType)
        {
            if (string.IsNullOrEmpty(content))
            {
                return GetDefaultValue(targetType);
            }

            var trimContent = content.Trim();

            // 处理简单类型
            if (IsSimpleType(targetType))
            {
                return ParseSimpleValue(trimContent, targetType);
            }

            // 处理枚举类型
            if (targetType.IsEnum)
            {
                return ParseEnumValue(trimContent, targetType);
            }

            // 处理数组类型
            if (IsArray(targetType))
            {
                return ParseArrayValue(trimContent, targetType);
            }

            // 处理结构体类型（包括 class 和 struct）
            if ((targetType.IsClass && !targetType.IsArray) || (targetType.IsValueType && !targetType.IsPrimitive && !targetType.IsEnum))
            {
                return ParseStructValue(trimContent, targetType);
            }

            throw new Exception($"Unsupported type: {targetType.Name}");
        }

        /// <summary>
        /// 解析简单类型值
        /// </summary>
        private static object ParseSimpleValue(string content, Type targetType)
        {
            if (IsNumber(targetType))
            {
                return ParseNumberValue(content.ToLower(), targetType);
            }
            else if (IsDouble(targetType))
            {
                return ParseDoubleValue(content, targetType);
            }
            else if (targetType == typeof(string))
            {
                // 移除字符串的引号
                if (content.StartsWith("\"") && content.EndsWith("\""))
                    return content.Substring(1, content.Length - 2);
                return content;
            }
            else if (targetType == typeof(bool))
            {
                var lowerContent = content.ToLower();
                return lowerContent == "true" || lowerContent == "1";
            }
            else if (targetType == typeof(char))
            {
                if (content.StartsWith("'") && content.EndsWith("'") && content.Length == 3)
                    return content[1];
                return content.Length > 0 ? content[0] : '\0';
            }
            else if (targetType.IsEnum)
            {
                var enumValue = Enum.Parse(targetType, content, true);
                if (enumValue != null)
                {
                    return enumValue;
                }
            }

            throw new Exception($"Cannot parse simple value '{content}' to type {targetType.Name}");
        }

        /// <summary>
        /// 解析数字值
        /// </summary>
        private static object ParseNumberValue(string content, Type targetType)
        {
            long value;

            if (content.StartsWith("0x"))
            {
                var hexPart = content.Substring(2);
                if (!long.TryParse(hexPart, NumberStyles.HexNumber, null, out value))
                {
                    throw new Exception($"Cannot parse hex number: {content}");
                }
            }
            else
            {
                if (!long.TryParse(content, out value))
                {
                    throw new Exception($"Cannot parse number: {content}");
                }
            }

            return Convert.ChangeType(value, targetType);
        }

        /// <summary>
        /// 解析枚举值
        /// </summary>
        private static object ParseEnumValue(string content, Type enumType)
        {
            if (string.IsNullOrEmpty(content))
            {
                return Enum.GetValues(enumType).GetValue(0);
            }

            // 尝试按名称解析
            var enumValue = Enum.Parse(enumType, content, true);
            if (enumValue != null)
            {
                return enumValue.ToString();
            }

            // 尝试按数值解析
            if (int.TryParse(content, out var intValue))
            {
                if (Enum.IsDefined(enumType, intValue))
                {
                    return Enum.ToObject(enumType, intValue);
                }
            }

            // 尝试十六进制解析
            if (content.StartsWith("0x", StringComparison.OrdinalIgnoreCase))
            {
                var hexPart = content.Substring(2);
                if (int.TryParse(hexPart, NumberStyles.HexNumber, null, out var hexValue))
                {
                    if (Enum.IsDefined(enumType, hexValue))
                    {
                        return Enum.ToObject(enumType, hexValue);
                    }
                }
            }

            throw new Exception($"Cannot parse enum value '{content}' for type {enumType.Name}");
        }

        /// <summary>
        /// 解析浮点数值
        /// </summary>
        private static object ParseDoubleValue(string content, Type targetType)
        {
            if (targetType == typeof(float))
            {
                if (!float.TryParse(content, out var floatValue))
                    throw new Exception($"Cannot parse float: {content}");
                return floatValue;
            }
            else if (targetType == typeof(double))
            {
                if (!double.TryParse(content, out var doubleValue))
                    throw new Exception($"Cannot parse double: {content}");
                return doubleValue;
            }

            throw new Exception($"Unsupported double type: {targetType.Name}");
        }

        /// <summary>
        /// 解析数组值
        /// </summary>
        private static object ParseArrayValue(string content, Type arrayType)
        {
            // 移除外层大括号
            if (!content.StartsWith("{") || !content.EndsWith("}"))
                throw new Exception($"Array value must be enclosed in braces: {content}");

            var innerContent = content.Substring(1, content.Length - 2).Trim();

            Type elementType;
            if (arrayType.IsArray)
            {
                elementType = arrayType.GetElementType();
            }
            else if (arrayType.IsGenericType && arrayType.GetGenericTypeDefinition() == typeof(List<>))
            {
                elementType = arrayType.GetGenericArguments()[0];
            }
            else
            {
                throw new Exception($"Unsupported array type: {arrayType.Name}");
            }

            var elements = SplitArrayElements(innerContent);
            var result = new List<object>();

            foreach (var element in elements)
            {
                if (!string.IsNullOrWhiteSpace(element))
                {
                    result.Add(ParseCinValue(element.Trim(), elementType));
                }
            }

            // 转换为目标类型
            if (arrayType.IsArray)
            {
                var array = Array.CreateInstance(elementType, result.Count);
                for (int i = 0; i < result.Count; i++)
                {
                    array.SetValue(result[i], i);
                }
                return array;
            }
            else
            {
                // 创建List<T>
                var listType = typeof(List<>).MakeGenericType(elementType);
                var list = Activator.CreateInstance(listType) as IList;
                foreach (var item in result)
                {
                    list.Add(item);
                }
                return list;
            }
        }

        /// <summary>
        /// 解析结构体值
        /// </summary>
        private static object ParseStructValue(string content, Type structType)
        {
            // 移除外层大括号
            if (!content.StartsWith("{") || !content.EndsWith("}"))
                throw new Exception($"Struct value must be enclosed in braces: {content}");

            var innerContent = content.Substring(1, content.Length - 2).Trim();
            var fieldValues = SplitArrayElements(innerContent);

            // 创建结构体实例
            var instance = Activator.CreateInstance(structType);
            var fields = structType.GetFields(BindingFlags.Public | BindingFlags.Instance);

            // 按顺序赋值字段
            for (int i = 0; i < Math.Min(fieldValues.Count, fields.Length); i++)
            {
                var field = fields[i];
                var fieldValue = fieldValues[i].Trim();

                if (!string.IsNullOrEmpty(fieldValue))
                {
                    var parsedValue = ParseCinValue(fieldValue, field.FieldType);
                    field.SetValue(instance, parsedValue);
                }
            }

            return instance;
        }

        /// <summary>
        /// 分割数组/结构体元素（考虑嵌套大括号）
        /// </summary>
        private static List<string> SplitArrayElements(string content)
        {
            var result = new List<string>();
            var current = new StringBuilder();
            var braceLevel = 0;
            var inQuotes = false;
            var quoteChar = '\0';

            for (int i = 0; i < content.Length; i++)
            {
                var ch = content[i];

                if (!inQuotes)
                {
                    if (ch == '"' || ch == '\'')
                    {
                        inQuotes = true;
                        quoteChar = ch;
                    }
                    else if (ch == '{')
                    {
                        braceLevel++;
                    }
                    else if (ch == '}')
                    {
                        braceLevel--;
                    }
                    else if (ch == ',' && braceLevel == 0)
                    {
                        result.Add(current.ToString());
                        current.Clear();
                        continue;
                    }
                }
                else
                {
                    if (ch == quoteChar && (i == 0 || content[i - 1] != '\\'))
                    {
                        inQuotes = false;
                        quoteChar = '\0';
                    }
                }

                current.Append(ch);
            }

            if (current.Length > 0)
            {
                result.Add(current.ToString());
            }

            return result;
        }

        /// <summary>
        /// 获取类型的默认值
        /// </summary>
        private static object GetDefaultValue(Type type)
        {
            if (type.IsValueType)
            {
                return Activator.CreateInstance(type);
            }

            if (type == typeof(string))
            {
                return string.Empty;
            }

            return null;
        }

        #endregion

        #region 序列化方法

        /// <summary>
        /// 将对象转换为CIN格式字符串
        /// </summary>
        public static string ToCinValue<T>(T param, bool isHex = false)
        {
            return ToCinValue(param, typeof(T), isHex);
        }

        /// <summary>
        /// 将对象转换为CIN格式字符串
        /// </summary>
        public static string ToCinValue(object param, Type paramType, bool isHex = false)
        {
            if (param == null)
            {
                return "null";
            }

            if (paramType.IsEnum)
            {
                return FormatEnumValue(param, paramType, isHex);
            }

            if (IsSimpleType(paramType))
            {
                return FormatSimpleValue(param, paramType, isHex);
            }

            if (IsArray(paramType))
            {
                return FormatArrayValue(param, paramType, isHex);
            }

            // 处理结构体类型（包括 class 和 struct）
            if ((paramType.IsClass && !paramType.IsArray) || (paramType.IsValueType && !paramType.IsPrimitive && !paramType.IsEnum))
            {
                return FormatStructValue(param, paramType, isHex);
            }

            throw new Exception($"Unsupported type: {paramType.Name}");
        }

        /// <summary>
        /// 格式化枚举值
        /// </summary>
        private static string FormatEnumValue(object enumValue, Type enumType, bool isHex)
        {
            if (enumValue == null)
            {
                return "";
            }

            // 在CIN格式中，枚举值通常使用名称而不是数值
            return enumValue.ToString();
        }

        /// <summary>
        /// 格式化简单类型值
        /// </summary>
        private static string FormatSimpleValue(object value, Type valueType, bool isHex)
        {
            if (IsNumber(valueType))
            {
                if (isHex)
                {
                    return $"0x{Convert.ToInt64(value):X}";
                }
                else
                {
                    return value.ToString();
                }
            }
            else if (IsDouble(valueType))
            {
                return value.ToString();
            }
            else if (valueType == typeof(string))
            {
                return $"\"{value}\"";
            }
            else if (valueType == typeof(bool))
            {
                return ((bool)value) ? "true" : "false";
            }
            else if (valueType == typeof(char))
            {
                return $"'{value}'";
            }

            return value.ToString();
        }

        /// <summary>
        /// 格式化数组值
        /// </summary>
        private static string FormatArrayValue(object arrayValue, Type arrayType, bool isHex)
        {
            var sb = new StringBuilder();
            sb.Append("{");

            Type elementType;
            if (arrayType.IsArray)
            {
                elementType = arrayType.GetElementType();
            }
            else if (arrayType.IsGenericType && arrayType.GetGenericTypeDefinition() == typeof(List<>))
            {
                elementType = arrayType.GetGenericArguments()[0];
            }
            else
            {
                throw new Exception($"Unsupported array type: {arrayType.Name}");
            }

            if (arrayValue is IEnumerable enumerable)
            {
                var elements = enumerable.Cast<object>().ToList();
                for (int i = 0; i < elements.Count; i++)
                {
                    if (i > 0) sb.Append(", ");

                    var elementValue = elements[i];
                    var elementIsHex = ShouldUseHex(elementValue, elementType);
                    sb.Append(ToCinValue(elementValue, elementType, elementIsHex));
                }
            }

            sb.Append("}");
            return sb.ToString();
        }

        /// <summary>
        /// 格式化结构体值
        /// </summary>
        private static string FormatStructValue(object structValue, Type structType, bool isHex)
        {
            var sb = new StringBuilder();
            sb.Append("{");

            var fields = structType.GetFields(BindingFlags.Public | BindingFlags.Instance);
            for (int i = 0; i < fields.Length; i++)
            {
                if (i > 0) sb.Append(", ");

                var field = fields[i];
                var fieldValue = field.GetValue(structValue);
                var fieldIsHex = ShouldUseHex(fieldValue, field.FieldType, field);

                sb.Append(ToCinValue(fieldValue, field.FieldType, fieldIsHex));
            }

            sb.Append("}");
            return sb.ToString();
        }

        /// <summary>
        /// 判断是否应该使用十六进制格式
        /// </summary>
        private static bool ShouldUseHex(object value, Type valueType, FieldInfo field = null)
        {
            // 检查字段是否有CinProp特性
            if (field != null)
            {
                var cinProp = field.GetCustomAttribute<CinPropAttribute>();
                if (cinProp != null)
                    return cinProp.IsHex;
            }

            // 默认不使用十六进制
            return false;
        }

        #endregion
    }
}
