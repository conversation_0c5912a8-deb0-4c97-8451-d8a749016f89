﻿using Alsi.Common.Parsers.Ldf.Model;
using System;
using System.Linq;


namespace Alsi.Tab.Kit.Core.Services.Capl.Params
{
    internal class LdfLoader
    {
        public void Load(LdfModel ldfModel, string source, IParamCollection collection, int order)
        {
            foreach (var node in ldfModel.Nodes)
            {
                if (!node.IsMaster)
                {
                    continue;
                }

                bool isSerNrPartNr(LdfScheduleTable table)
                {
                    return table.Name.EndsWith("SerNrPartNr", StringComparison.OrdinalIgnoreCase);
                }

                bool isDiagnosticTable(LdfScheduleTable table)
                {
                    return table.ScheduleTableFrames.Any(
                        frame => frame.Name.Equals("MasterReq", StringComparison.OrdinalIgnoreCase)
                        || frame.Name.Equals("SlaveResp", StringComparison.OrdinalIgnoreCase));
                }

                var scheduleTables = ldfModel.ScheduleTables.Where(x => !isSerNrPartNr(x) && !isDiagnosticTable(x)).ToArray();
                if (scheduleTables.Any())
                {
                    var ecuNode = node.Name;
                    var value = scheduleTables.First().Name;
                    var key = CaplParamConsts.NormalScheduleTableName;
                    collection.AddValue(ecuNode, key, value, source, order);
                }
            }

            foreach (var node in ldfModel.Nodes)
            {
                var ecuNode = node.Name;
                var value = node.ConfigAddress.ToString();
                var key = CaplParamConsts.DIAG_LIN_PHYSIC_REQ_NAD;
                collection.AddValue(ecuNode, key, value, source, order);

                value = node.IsMaster.ToString();
                key = CaplParamConsts.IsMasterDUT;
                collection.AddValue(ecuNode, key, value, source, order);
            }
        }
    }
}
