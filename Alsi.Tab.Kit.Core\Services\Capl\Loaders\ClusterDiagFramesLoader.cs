﻿using Alsi.Common.Parsers.Arxml.Models;
using Alsi.Common.Utils;
using Alsi.Tab.Kit.Core.Services.Capl.Core;
using System.Collections.Generic;

namespace Alsi.Tab.Kit.Core.Services.Capl.Params
{
    internal class ClusterDiagFramesLoader : ArxmlLoaderBase
    {
        public ClusterDiagFramesLoader(ArxmlLoaderContext context)
            : base(context)
        {
        }

        public override void Load()
        {
            List<ClusterDiagFrames> diagRoutings = new List<ClusterDiagFrames>();
            var clusters = arxmlModel.GetCommunicationClusters();
            foreach (var cluster in clusters)
            {
                var diagRouting = new ClusterDiagFrames();
                diagRouting.ClusterName = cluster.Name;
                diagRouting.BusType = cluster.BusType;
                List<GwDiagFrame> diagFrames = new List<GwDiagFrame>();
                foreach (var frame in cluster.Frames)
                {
                    if (diagRouting.BusType == ClusterBusType.Can || diagRouting.BusType == ClusterBusType.CanFd)
                    {
                        if (frame.Id > 0x600 && frame.Id < 0xFFF)
                        {
                            var diagFrame = new GwDiagFrame(frame.Name, frame.Id, frame.Dir);
                            diagFrames.Add(diagFrame);
                        }
                    }
                    else if (diagRouting.BusType == ClusterBusType.Lin)
                    {
                        if (frame.Id == 0x3c || frame.Id == 0x3d)
                        {
                            var diagFrame = new GwDiagFrame(frame.Name, frame.Id, frame.Dir);
                            diagFrames.Add(diagFrame);
                        }
                    }
                    else
                    {
                        return;
                    }
                }
                diagRouting.Frames = diagFrames.ToArray();
                diagRoutings.Add(diagRouting);
            }
            foreach (var ecuNode in arxmlEcuNodes)
            {
                var key = CaplParamConsts.DiagRouting;
                collection.AddValue(ecuNode, key, JsonUtils.Serialize(diagRoutings), source, order);
            }
        }
    }
}
