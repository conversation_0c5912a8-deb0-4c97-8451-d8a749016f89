﻿using Alsi.Common.Parsers.Sddb;
using Alsi.Common.Utils;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;


namespace Alsi.Tab.Kit.Core.Services.Capl.Params
{
    internal class P4ServerTimeLoader
    {
        public void Load(string ecuNode, string source, SW[] sws, IParamCollection collection, int order)
        {
            var list = new List<P4SeverTime>();

            void tryPush(Session[] sessions, byte sid, int subfunctionId, int rid, int did)
            {
                if (sessions == null)
                {
                    return;
                }

                foreach (var session in sessions)
                {
                    if (session == null
                        || !float.TryParse(session.P4ServerMax, out var timeoutMs)
                        || !int.TryParse(session.ID, out var sessionId))
                    {
                        continue;
                    }

                    if (list.Any(x =>
                        x.SessionId == sessionId
                        && x.Sid == sid
                        && x.SubfunctionId == subfunctionId
                        && x.Rid == rid
                        && x.Did == did
                        && Math.Abs(x.TimeoutMs - timeoutMs) < 1e-6))
                    {
                        continue;
                    }
                    list.Add(new P4SeverTime(sessionId, sid, subfunctionId, rid, did, timeoutMs));
                }
            }

            foreach (var sw in sws)
            {
                foreach (var service in sw.Services.Service)
                {
                    if (!byte.TryParse(service.ID, NumberStyles.HexNumber, null, out var serviceId))
                    {
                        continue;
                    }

                    // Service 粒度的 P4ServerTime
                    tryPush(service.Sessions.Session, serviceId,
                        subfunctionId: P4SeverTime.Any, rid: P4SeverTime.Any, did: P4SeverTime.Any);

                    if (service.DataIdentifiers.DataIdentifier == null)
                    {
                        continue;
                    }

                    foreach (var dataIdentifier in service.DataIdentifiers.DataIdentifier)
                    {
                        if (!int.TryParse(dataIdentifier.ID, NumberStyles.HexNumber, null, out var did))
                        {
                            continue;
                        }

                        // DID 粒度的 P4ServerTime
                        tryPush(dataIdentifier.Sessions.Session, serviceId, subfunctionId: P4SeverTime.Any, rid: P4SeverTime.Any, did);
                    }

                    foreach (var subfunction in service.Subfunctions.Subfunction)
                    {
                        if (!byte.TryParse(subfunction.ID, NumberStyles.HexNumber, null, out var subFunctionId))
                        {
                            continue;
                        }

                        // Subfunction 粒度的 P4ServerTime
                        tryPush(subfunction.Sessions.Session, serviceId, subFunctionId, rid: P4SeverTime.Any, did: P4SeverTime.Any);

                        foreach (var routineIdentifier in subfunction.RoutineIdentifiers.RoutineIdentifier)
                        {
                            if (!int.TryParse(routineIdentifier.ID, NumberStyles.HexNumber, null, out var rid))
                            {
                                continue;
                            }

                            // RID 粒度的 P4ServerTime
                            tryPush(routineIdentifier.Sessions.Session, serviceId, subFunctionId, rid, did: P4SeverTime.Any);
                        }
                    }
                }
            }

            list = list.OrderBy(x => x.Sid)
                .ThenBy(x => x.SubfunctionId)
                .ThenBy(x => x.Did)
                .ThenBy(x => x.Rid)
                .ThenBy(x => x.SessionId)
                .ToList();

            var key = CaplParamConsts.P4SeverTimeMatrix;
            var value = JsonUtils.Serialize(list.ToArray());
            collection.AddValue(ecuNode, key, value, source, order);
        }
    }
}
