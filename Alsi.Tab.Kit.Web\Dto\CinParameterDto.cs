using System;
using System.Collections.Generic;

namespace Alsi.Tab.Kit.Web.Dto
{
    public class CinTemplateDto
    {
        public Guid Id { get; set; }
        public string Path { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string FullPath { get; set; } = string.Empty;
        public bool FileExists { get; set; }
    }

    public class CinParameterParseRequest
    {
        public string SourceType { get; set; } = string.Empty;
        public Guid? TemplateId { get; set; }
        public string FilePath { get; set; } = string.Empty;
    }

    // 源文件相关 DTO
    public class SourceFileDto
    {
        public Guid Id { get; set; }
        public string Path { get; set; } = string.Empty;
        public string FileName { get; set; } = string.Empty;
        public string FileType { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public List<ParsedParamDto> ParsedParams { get; set; } = new List<ParsedParamDto>();
    }

    public class ParsedParamDto
    {
        public string EcuName { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Value { get; set; }
        public string ParamType { get; set; } = string.Empty;
        public string Source { get; set; } = string.Empty;
    }

    public class AddSourceFilesRequest
    {
        public string[] FilePaths { get; set; } = Array.Empty<string>();
    }

    public class ParseSourceFileRequest
    {
        public Guid FileId { get; set; }
    }

    public class RemoveSourceFileRequest
    {
        public Guid FileId { get; set; }
    }

    public class UserFileHistoryDto
    {
        public List<string> LastSelectedPaths { get; set; } = new List<string>();
        public DateTime LastUpdateTime { get; set; }
    }
}
