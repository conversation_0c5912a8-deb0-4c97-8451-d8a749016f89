<template>
  <el-dialog
    v-model="visible"
    :title="title"
    width="60%"
    :before-close="handleClose"
    destroy-on-close
  >
    <div class="param-value-viewer">
      <el-input
        v-model="displayValue"
        type="textarea"
        :rows="15"
        :readonly="readonly"
        :placeholder="readonly ? '' : '请输入参数值...'"
        style="font-family: 'Courier New', monospace;"
        @input="handleInput"
      />
    </div>

    <template #footer v-if="!readonly">
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';

// Props
interface Props {
  modelValue: boolean;
  value: any;
  readonly?: boolean;
  title?: string;
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false,
  title: '参数值'
});

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean];
  'update:value': [value: string];
  'confirm': [value: string];
}>();

// 数据
const visible = ref(false);
const displayValue = ref('');

// 计算属性
const formattedValue = computed(() => {
  if (props.value === null || props.value === undefined) {
    return '';
  }
  
  if (typeof props.value === 'string') {
    // 如果是字符串，尝试格式化JSON
    try {
      const parsed = JSON.parse(props.value);
      return JSON.stringify(parsed, null, 2);
    } catch {
      // 如果不是有效的JSON，直接返回原值
      return props.value;
    }
  } else {
    // 如果是对象，序列化为JSON
    try {
      return JSON.stringify(props.value, null, 2);
    } catch {
      return String(props.value);
    }
  }
});

// 监听器
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal;
  if (newVal) {
    displayValue.value = formattedValue.value;
  }
});

watch(visible, (newVal) => {
  emit('update:modelValue', newVal);
});

watch(() => props.value, () => {
  if (visible.value) {
    displayValue.value = formattedValue.value;
  }
});

// 方法
const handleClose = () => {
  visible.value = false;
};

const handleInput = (value: string) => {
  if (!props.readonly) {
    emit('update:value', value);
  }
};

const handleConfirm = () => {
  if (!props.readonly) {
    emit('confirm', displayValue.value);
  }
  handleClose();
};
</script>

<style scoped>
.param-value-viewer {
  min-height: 300px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
