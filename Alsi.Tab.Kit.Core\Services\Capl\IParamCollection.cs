﻿using Alsi.Tab.Kit.Core.Services.Capl.Params;

namespace Alsi.Tab.Kit.Core.Services.Capl
{
    public interface IParamCollection
    {
        ParamCollection.Ecu GetOrAddEcu(string ecuNode);

        void AddValue(string ecuNode, string key, string value, string source, int order);

        string GetValue(string ecuNode, string regularParamKey);

        ParamCollection.Ecu[] GetEcuNodes();

        void AddEcuAlias(string ecuNameA, string ecuNameB);

        string[] GetEcuAliases(string ecuName);
    }
}
