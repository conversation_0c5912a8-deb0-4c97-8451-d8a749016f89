﻿using Alsi.Common.Parsers.Arxml.Models;
using Alsi.Common.Utils;
using Alsi.Common.Utils.Linq;
using Alsi.Tab.Kit.Core.Services.Capl.Core;
using System.Collections.Generic;
using System.Linq;
using ArxmlModel = AUTOSAR;


namespace Alsi.Tab.Kit.Core.Services.Capl.Params
{
    internal class E2ECanFramesLoader : ArxmlLoaderBase
    {
        public E2ECanFramesLoader(ArxmlLoaderContext context)
            : base(context)
        {
        }

        public class MyE2E
        {
            public string signalGroupName;
            public E2EFrame e2eFrame;
            public Direction dir;
            public string cluster;
            public MyE2E(string signalGroupName, E2EFrame e2eFrame, Direction dir, string cluster)
            {
                this.signalGroupName = signalGroupName;
                this.e2eFrame = e2eFrame;
                this.dir = dir;
                this.cluster = cluster;
            }
        }
        private Dictionary<string, (string, Direction)> canClusterFramesDictionary;

        public override void Load()
        {
            canClusterFramesDictionary = GetCanClusterFramesDictionary(arxmlModel);
            var systemSignalGroupNames = arxmlModel.ARPACKAGES.Where(x => x.SHORTNAME.Value == "EndToEnds")
                .SelectManyEx(x => x.ELEMENTS)
                .OfType<ENDTOENDPROTECTIONSET>()
                .SelectMany(x => x.ENDTOENDPROTECTIONS)
                .Select(x => x.SHORTNAME.Value.Split(new char[] { '_' }).FirstOrDefault())
                .ToArray();

            List<MyE2E> myE2E = new List<MyE2E>();
            foreach (var systemSignalGroupName in systemSignalGroupNames)
            {
                if (LoaderUtils.TryLoadE2ECanFrameFromArxml(arxmlModel, systemSignalGroupName, false, out var e2eFrames))
                {
                    foreach (var e2eFrame in e2eFrames)
                    {
                        var dir = TryGetCommDirectionByFrameName(e2eFrame.frameName);
                        var clusterName = TryGetClusterByFrameName(e2eFrame.frameName);
                        myE2E.Add(new MyE2E(systemSignalGroupName, e2eFrame, dir, clusterName));
                    }
                }
            }

            var e2eTxFrames = new List<ClusterE2EFrame>();
            var e2eRxFrames = new List<ClusterE2EFrame>();

            var clusters = myE2E.GroupBy(x => x.cluster);
            foreach (var cluster in clusters)
            {
                List<E2EFrame> txFrames = new List<E2EFrame>();
                List<E2EFrame> rxFrames = new List<E2EFrame>();
                foreach (var frame in cluster)
                {
                    if (frame.dir == Direction.Tx)
                    {
                        txFrames.Add(frame.e2eFrame);
                    }
                    else if (frame.dir == Direction.Rx)
                    {
                        rxFrames.Add(frame.e2eFrame);
                    }
                    else
                    {
                        continue;
                    }
                }
                if (txFrames.Any())
                    e2eTxFrames.Add(new ClusterE2EFrame(cluster.Key, txFrames.ToArray()));
                if (rxFrames.Any())
                    e2eRxFrames.Add(new ClusterE2EFrame(cluster.Key, rxFrames.ToArray()));
            }
            //foreach (var systemSignalGroupName in systemSignalGroupNames)
            //{
            //    if (TryLoadE2ECanFrameFromArxml(arxmlModel, systemSignalGroupName, false, out var e2eFrame))
            //    {
            //        if (frameDictionary.TryGetValue(e2eFrame.frameName, out var existedFrame))
            //        {
            //            var list = existedFrame.signalGroups.ToList();
            //            list.AddRange(e2eFrame.signalGroups);
            //            existedFrame.signalGroups = list.OrderBy(x => x.dataId).ToArray();
            //        }
            //        else
            //        {
            //            frameDictionary.Add(e2eFrame.frameName, e2eFrame);
            //        }
            //    }
            //}

            //var e2eFrames = frameDictionary.Values.OrderBy(x => x.frameID).ToArray();

            foreach (var ecuInstance in arxmlModel.EcuInstances)
            {
                var node = ecuInstance.SHORTNAME.Value;

                var value = JsonUtils.Serialize(e2eTxFrames);
                var key = CaplParamConsts.E2ECanFrameTxList;
                collection.AddValue(node, key, value, source, order);

                value = JsonUtils.Serialize(e2eRxFrames);
                key = CaplParamConsts.E2ECanFrameRxList;
                collection.AddValue(node, key, value, source, order);
            }
        }
        private Dictionary<string, (string, Direction)> GetCanClusterFramesDictionary(ArxmlModel arxmlModel)
        {
            var clusterFrameMapping = new Dictionary<string, (string, Direction)>();
            var clusters = arxmlModel.ARPACKAGES.Where(x => x.SHORTNAME.Value == "CommunicationClusters")
                                .SelectManyEx(x => x.ELEMENTS)
                                .OfType<CANCLUSTER>()
                                .ToArray();
            foreach (var cluster in clusters)
            {
                var clusterName = cluster.SHORTNAME.Value;
                var frames = arxmlModel.GetFrameTriggeringsByCanChannelName(clusterName);
                foreach (var frame in frames)
                {
                    var frameName = frame.SHORTNAME.Value;
                    var frameDir = frame?.CANFRAMETXBEHAVIOR == null ? Direction.Rx : Direction.Tx;
                    clusterFrameMapping.Add(frameName, (clusterName, frameDir));
                }
            }
            return clusterFrameMapping;
        }
        //TBD
        public Direction TryGetCommDirectionByFrameName(string frameName)
        {
            return canClusterFramesDictionary.Where(x => x.Key.EndsWith(frameName))
                                             .Select(x => x.Value.Item2)
                                             .FirstOrDefault();
        }
        public string TryGetClusterByFrameName(string frameName)
        {
            return canClusterFramesDictionary.Where(x => x.Key.EndsWith(frameName))
                                             .Select(x => x.Value.Item1)
                                             .FirstOrDefault();
        }
    }
}
