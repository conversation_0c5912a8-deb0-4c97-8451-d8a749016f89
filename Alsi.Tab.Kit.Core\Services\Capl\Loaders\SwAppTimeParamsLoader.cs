﻿using Alsi.Common.Parsers.Sddb;
using System.Globalization;
using System.Linq;


namespace Alsi.Tab.Kit.Core.Services.Capl.Params
{
    internal class SwAppTimeParamsLoader
    {
        public void Load(string ecuNode, string source, SW sw, IParamCollection collection, int order)
        {
            // DtcStatusAvailabilityMask
            if (byte.TryParse(sw.DTCStatusAvailabilityMask, NumberStyles.HexNumber, null, out var DtcStatusAvailabilityMask))
            {
                var key = CaplParamConsts.DtcStatusAvailabilityMask;
                collection.AddValue(ecuNode, key, DtcStatusAvailabilityMask.ToString(), source, order);
            }

            const string defaultSessionName = "DefaultSession";
            var defaultSession = sw.SessionLayer.Session.FirstOrDefault(x => x.Name == defaultSessionName);
            if (defaultSession == null)
            {
                return;
            }

            // P2Time
            if (int.TryParse(defaultSession.P2ServerMax_default, out var P2ServerMax))
            {
                var key = CaplParamConsts.P2Time;
                collection.AddValue(ecuNode, key, P2ServerMax.ToString(), source, order);

                key = CaplParamConsts.BackgroundTestP2Time;
                collection.AddValue(ecuNode, key, P2ServerMax.ToString(), source, order);
            }

            // P2ExtTime
            if (int.TryParse(defaultSession.P2StarServerMax_default, out var P2ExtTime))
            {
                var key = CaplParamConsts.P2ExtTime;
                collection.AddValue(ecuNode, key, P2ExtTime.ToString(), source, order);

                key = CaplParamConsts.BackgroundTestP2ExtTime;
                collection.AddValue(ecuNode, key, P2ExtTime.ToString(), source, order);
            }

            // S3Server
            if (int.TryParse(defaultSession.S3Server, out var S3Server))
            {
                var key = CaplParamConsts.S3ServerTime;
                collection.AddValue(ecuNode, key, S3Server.ToString(), source, order);
            }

            defaultSession = sw.TransportLayers.TransportLayer.Sessions.Session.FirstOrDefault(x => x.Name == defaultSessionName);
            if (defaultSession == null)
            {
                return;
            }

            // N_As
            if (int.TryParse(defaultSession.N_As_Timeout, out var N_As))
            {
                var key = CaplParamConsts.N_As;
                collection.AddValue(ecuNode, key, N_As.ToString(), source, order);
            }

            // N_Ar
            if (int.TryParse(defaultSession.N_Ar_Timeout, out var N_Ar))
            {
                var key = CaplParamConsts.N_Ar;
                collection.AddValue(ecuNode, key, N_Ar.ToString(), source, order);
            }

            // N_Bs
            if (int.TryParse(defaultSession.N_Bs_Timeout, out var N_Bs))
            {
                var key = CaplParamConsts.N_Bs;
                collection.AddValue(ecuNode, key, N_Bs.ToString(), source, order);
            }

            // N_Cr
            if (int.TryParse(defaultSession.N_Cr_Timeout, out var N_Cr))
            {
                var key = CaplParamConsts.N_Cr;
                collection.AddValue(ecuNode, key, N_Cr.ToString(), source, order);
            }
        }
    }
}
