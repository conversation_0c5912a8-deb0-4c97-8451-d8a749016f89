﻿++解决方案 'Alsi.Tab.Kit' ‎ (11 个项目，共 11 个)
i:{00000000-0000-0000-0000-000000000000}:Alsi.Tab.Kit.sln
++Alsi.Tab.Kit.Core
i:{00000000-0000-0000-0000-000000000000}:Alsi.Tab.Kit.Core
i:{d7d76682-65bb-461c-b1e9-af038aae0d22}:
i:{9e8c78a1-6d4a-4d48-9d98-9829354e0c44}:>1157
++Properties
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.core\properties\
i:{d7d76682-65bb-461c-b1e9-af038aae0d22}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.web\properties\
i:{32f4afd7-a5c8-4d09-a996-c849891054dc}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit\properties\
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.database\properties\
i:{5f059b1d-fa34-4752-8dae-0b78e6ddf378}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.log\properties\
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\properties\
i:{9f3c92dc-e277-4374-b637-7c6df32fceb0}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.edge\properties\
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\properties\
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\properties\
++引用
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:
i:{d7d76682-65bb-461c-b1e9-af038aae0d22}:
i:{32f4afd7-a5c8-4d09-a996-c849891054dc}:
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:
i:{5f059b1d-fa34-4752-8dae-0b78e6ddf378}:
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{9f3c92dc-e277-4374-b637-7c6df32fceb0}:
i:{2bf46d86-9704-494a-8998-a478b601df80}:
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
++Models
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.core\models\
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\arxml\models\
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\models\
++Services
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.core\services\
++Capl
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.core\services\capl\
++CaplParamTypes
++CaplParamTypes.cs
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.core\services\capl\caplparamtypes.cs
++Loaders
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.core\services\capl\loaders\
++ArxmlLoader.cs
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.core\services\capl\loaders\arxmlloader.cs
++BusTypeLoader.cs
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.core\services\capl\loaders\bustypeloader.cs
++BusTypeMapLoader.cs
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.core\services\capl\loaders\bustypemaploader.cs
++CarConfigFrameIdLoader.cs
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.core\services\capl\loaders\carconfigframeidloader.cs
++CarConfigParamsLoader.cs
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.core\services\capl\loaders\carconfigparamsloader.cs
++ClusterDiagFramesLoader.cs
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.core\services\capl\loaders\clusterdiagframesloader.cs
++DiagRoutingLoader.cs
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.core\services\capl\loaders\diagroutingloader.cs
++Dids0x2ELoader.cs
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.core\services\capl\loaders\dids0x2eloader.cs
++DidsLoader.cs
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.core\services\capl\loaders\didsloader.cs
++DtcExtLoader.cs
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.core\services\capl\loaders\dtcextloader.cs
++DtcsLoader.cs
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.core\services\capl\loaders\dtcsloader.cs
++E2ECanFramesLoader.cs
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.core\services\capl\loaders\e2ecanframesloader.cs
++E2ECarUsageModeInfoLoader.cs
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.core\services\capl\loaders\e2ecarusagemodeinfoloader.cs
++E2EFrameExtension.cs
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.core\services\capl\loaders\e2eframeextension.cs
++E2ELinFrameLoader.cs
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.core\services\capl\loaders\e2elinframeloader.cs
++E2EUbatSimulationFrameLoader.cs
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.core\services\capl\loaders\e2eubatsimulationframeloader.cs
++E2EVehicleSpeedFrameLoader.cs
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.core\services\capl\loaders\e2evehiclespeedframeloader.cs
++LdfLoader.cs
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.core\services\capl\loaders\ldfloader.cs
++LoaderUtils.cs
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.core\services\capl\loaders\loaderutils.cs
++P4ServerTimeLoader.cs
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.core\services\capl\loaders\p4servertimeloader.cs
++PncInfoLoader.cs
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.core\services\capl\loaders\pncinfoloader.cs
++SecurityLevelsLoader.cs
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.core\services\capl\loaders\securitylevelsloader.cs
++SignalRoutingLoader.cs
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.core\services\capl\loaders\signalroutingloader.cs
++SwAppTimeParamsLoader.cs
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.core\services\capl\loaders\swapptimeparamsloader.cs
++SwPblTimeParamsLoader.cs
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.core\services\capl\loaders\swpbltimeparamsloader.cs
++SwsLoader.cs
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.core\services\capl\loaders\swsloader.cs
++VfcInfoLoader.cs
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.core\services\capl\loaders\vfcinfoloader.cs
++VoltageRelatedDtcLoader.cs
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.core\services\capl\loaders\voltagerelateddtcloader.cs
++CaplParamConsts.cs
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.core\services\capl\caplparamconsts.cs
++CaplUtils.cs
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.core\services\capl\caplutils.cs
++IParamCollection.cs
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.core\services\capl\iparamcollection.cs
++CinParameterService.cs
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.core\services\cinparameterservice.cs
++CinTemplatesService.cs
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.core\services\cintemplatesservice.cs
++DataLogConvertService.cs
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.core\services\datalogconvertservice.cs
++ExternalAppsService.cs
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.core\services\externalappsservice.cs
++app.config
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.core\app.config
i:{d7d76682-65bb-461c-b1e9-af038aae0d22}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.web\app.config
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.database\app.config
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\app.config
i:{9f3c92dc-e277-4374-b637-7c6df32fceb0}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.edge\app.config
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\app.config
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\app.config
++AssemblyInfo.cs
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.core\properties\assemblyinfo.cs
i:{d7d76682-65bb-461c-b1e9-af038aae0d22}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.web\properties\assemblyinfo.cs
i:{32f4afd7-a5c8-4d09-a996-c849891054dc}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit\properties\assemblyinfo.cs
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.database\properties\assemblyinfo.cs
i:{5f059b1d-fa34-4752-8dae-0b78e6ddf378}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.log\properties\assemblyinfo.cs
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\properties\assemblyinfo.cs
i:{9f3c92dc-e277-4374-b637-7c6df32fceb0}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.edge\properties\assemblyinfo.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\properties\assemblyinfo.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\properties\assemblyinfo.cs
++Alsi.App
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:
i:{d7d76682-65bb-461c-b1e9-af038aae0d22}:
i:{32f4afd7-a5c8-4d09-a996-c849891054dc}:
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{9f3c92dc-e277-4374-b637-7c6df32fceb0}:
i:{02ea681e-c7d8-13c7-8484-4ac65e1b71e8}:Alsi.App
++Alsi.Common.Log
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:
i:{02ea681e-c7d8-13c7-8484-4ac65e1b71e8}:Alsi.Common.Log
++Alsi.Common.Parsers
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:
i:{d7d76682-65bb-461c-b1e9-af038aae0d22}:
i:{02ea681e-c7d8-13c7-8484-4ac65e1b71e8}:Alsi.Common.Parsers
++Alsi.Common.Utils
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:
i:{d7d76682-65bb-461c-b1e9-af038aae0d22}:
i:{32f4afd7-a5c8-4d09-a996-c849891054dc}:
i:{5f059b1d-fa34-4752-8dae-0b78e6ddf378}:
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
i:{02ea681e-c7d8-13c7-8484-4ac65e1b71e8}:Alsi.Common.Utils
++Microsoft.CSharp
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:
i:{d7d76682-65bb-461c-b1e9-af038aae0d22}:
i:{32f4afd7-a5c8-4d09-a996-c849891054dc}:
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:
i:{5f059b1d-fa34-4752-8dae-0b78e6ddf378}:
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{9f3c92dc-e277-4374-b637-7c6df32fceb0}:
i:{2bf46d86-9704-494a-8998-a478b601df80}:
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
++System
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:
i:{d7d76682-65bb-461c-b1e9-af038aae0d22}:
i:{32f4afd7-a5c8-4d09-a996-c849891054dc}:
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:
i:{5f059b1d-fa34-4752-8dae-0b78e6ddf378}:
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{9f3c92dc-e277-4374-b637-7c6df32fceb0}:
i:{2bf46d86-9704-494a-8998-a478b601df80}:
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.dll
++System.Core
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:
i:{d7d76682-65bb-461c-b1e9-af038aae0d22}:
i:{32f4afd7-a5c8-4d09-a996-c849891054dc}:
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:
i:{5f059b1d-fa34-4752-8dae-0b78e6ddf378}:
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{9f3c92dc-e277-4374-b637-7c6df32fceb0}:
i:{2bf46d86-9704-494a-8998-a478b601df80}:
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.core.dll
++System.Data
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:
i:{d7d76682-65bb-461c-b1e9-af038aae0d22}:
i:{32f4afd7-a5c8-4d09-a996-c849891054dc}:
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:
i:{5f059b1d-fa34-4752-8dae-0b78e6ddf378}:
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{9f3c92dc-e277-4374-b637-7c6df32fceb0}:
i:{2bf46d86-9704-494a-8998-a478b601df80}:
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.data.dll
++System.Data.DataSetExtensions
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:
i:{d7d76682-65bb-461c-b1e9-af038aae0d22}:
i:{32f4afd7-a5c8-4d09-a996-c849891054dc}:
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:
i:{5f059b1d-fa34-4752-8dae-0b78e6ddf378}:
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{9f3c92dc-e277-4374-b637-7c6df32fceb0}:
i:{2bf46d86-9704-494a-8998-a478b601df80}:
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
++System.Memory
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:
++System.Numerics
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:
i:{d7d76682-65bb-461c-b1e9-af038aae0d22}:
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{2bf46d86-9704-494a-8998-a478b601df80}:
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.numerics.dll
++System.Runtime.CompilerServices.Unsafe
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:
i:{d7d76682-65bb-461c-b1e9-af038aae0d22}:
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
i:{2bf46d86-9704-494a-8998-a478b601df80}:
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
++System.Text.Encoding.CodePages
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:
++System.Windows.Forms
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:
++System.Xml
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:
i:{d7d76682-65bb-461c-b1e9-af038aae0d22}:
i:{32f4afd7-a5c8-4d09-a996-c849891054dc}:
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:
i:{5f059b1d-fa34-4752-8dae-0b78e6ddf378}:
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{9f3c92dc-e277-4374-b637-7c6df32fceb0}:
i:{2bf46d86-9704-494a-8998-a478b601df80}:
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.xml.dll
++System.Xml.Linq
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:
i:{d7d76682-65bb-461c-b1e9-af038aae0d22}:
i:{32f4afd7-a5c8-4d09-a996-c849891054dc}:
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:
i:{5f059b1d-fa34-4752-8dae-0b78e6ddf378}:
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{9f3c92dc-e277-4374-b637-7c6df32fceb0}:
i:{2bf46d86-9704-494a-8998-a478b601df80}:
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.xml.linq.dll
++CinParameter.cs
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.core\models\cinparameter.cs
++CinTemplate.cs
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.core\models\cintemplate.cs
++DataLogModels.cs
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.core\models\datalogmodels.cs
++ExternalApp.cs
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.core\models\externalapp.cs
++Alsi.Tab.Kit.Web
i:{00000000-0000-0000-0000-000000000000}:Alsi.Tab.Kit.Web
i:{32f4afd7-a5c8-4d09-a996-c849891054dc}:
++Controllers
i:{d7d76682-65bb-461c-b1e9-af038aae0d22}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.web\controllers\
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\controllers\
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\controllers\
++Dto
i:{d7d76682-65bb-461c-b1e9-af038aae0d22}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.web\dto\
++Mapping
i:{d7d76682-65bb-461c-b1e9-af038aae0d22}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.web\mapping\
++build-web.ps1
i:{d7d76682-65bb-461c-b1e9-af038aae0d22}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.web\build-web.ps1
++MapperEnv.cs
i:{d7d76682-65bb-461c-b1e9-af038aae0d22}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.web\mapperenv.cs
++TabKitWebAssembly.cs
i:{d7d76682-65bb-461c-b1e9-af038aae0d22}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.web\tabkitwebassembly.cs
++web.zip
i:{d7d76682-65bb-461c-b1e9-af038aae0d22}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.web\web.zip
++Alsi.App.Desktop
i:{d7d76682-65bb-461c-b1e9-af038aae0d22}:
i:{32f4afd7-a5c8-4d09-a996-c849891054dc}:
i:{02ea681e-c7d8-13c7-8484-4ac65e1b71e8}:Alsi.App.Desktop
++AutoMapper
i:{d7d76682-65bb-461c-b1e9-af038aae0d22}:
++Microsoft.AspNet.WebApi.Owin
i:{d7d76682-65bb-461c-b1e9-af038aae0d22}:
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
++Newtonsoft.Json
i:{d7d76682-65bb-461c-b1e9-af038aae0d22}:
i:{2bf46d86-9704-494a-8998-a478b601df80}:
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
++CinParameterController.cs
i:{d7d76682-65bb-461c-b1e9-af038aae0d22}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.web\controllers\cinparametercontroller.cs
++DataLogConvertController.cs
i:{d7d76682-65bb-461c-b1e9-af038aae0d22}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.web\controllers\datalogconvertcontroller.cs
++ExternalAppsController.cs
i:{d7d76682-65bb-461c-b1e9-af038aae0d22}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.web\controllers\externalappscontroller.cs
++TestController.cs
i:{d7d76682-65bb-461c-b1e9-af038aae0d22}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.web\controllers\testcontroller.cs
++WebControllerBase.cs
i:{d7d76682-65bb-461c-b1e9-af038aae0d22}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.web\controllers\webcontrollerbase.cs
++CinParameterDto.cs
i:{d7d76682-65bb-461c-b1e9-af038aae0d22}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.web\dto\cinparameterdto.cs
++ExternalAppDto.cs
i:{d7d76682-65bb-461c-b1e9-af038aae0d22}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.web\dto\externalappdto.cs
++TestModeDto.cs
i:{d7d76682-65bb-461c-b1e9-af038aae0d22}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.web\dto\testmodedto.cs
++MappingProfile.cs
i:{d7d76682-65bb-461c-b1e9-af038aae0d22}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.web\mapping\mappingprofile.cs
++Alsi.Tab.Kit
i:{00000000-0000-0000-0000-000000000000}:Alsi.Tab.Kit
++Connected Services
i:{32f4afd7-a5c8-4d09-a996-c849891054dc}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit\connected services\
++Views
i:{32f4afd7-a5c8-4d09-a996-c849891054dc}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit\views\
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\views\
i:{9f3c92dc-e277-4374-b637-7c6df32fceb0}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.edge\views\
++App.config
i:{32f4afd7-a5c8-4d09-a996-c849891054dc}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit\app.config
++app.manifest
i:{32f4afd7-a5c8-4d09-a996-c849891054dc}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit\app.manifest
++App.xaml
i:{32f4afd7-a5c8-4d09-a996-c849891054dc}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit\app.xaml
++binlog.dll
i:{32f4afd7-a5c8-4d09-a996-c849891054dc}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit\binlog.dll
++favicon.ico
i:{32f4afd7-a5c8-4d09-a996-c849891054dc}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit\favicon.ico
++OperateBlf.dll
i:{32f4afd7-a5c8-4d09-a996-c849891054dc}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit\operateblf.dll
++packages.config
i:{32f4afd7-a5c8-4d09-a996-c849891054dc}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit\packages.config
++Resources.resx
i:{32f4afd7-a5c8-4d09-a996-c849891054dc}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit\properties\resources.resx
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\properties\resources.resx
i:{9f3c92dc-e277-4374-b637-7c6df32fceb0}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.edge\properties\resources.resx
++Settings.settings
i:{32f4afd7-a5c8-4d09-a996-c849891054dc}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit\properties\settings.settings
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\properties\settings.settings
i:{9f3c92dc-e277-4374-b637-7c6df32fceb0}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.edge\properties\settings.settings
++Alsi.App.Database
i:{32f4afd7-a5c8-4d09-a996-c849891054dc}:
i:{02ea681e-c7d8-13c7-8484-4ac65e1b71e8}:Alsi.App.Database
++Alsi.App.Edge
i:{32f4afd7-a5c8-4d09-a996-c849891054dc}:
i:{02ea681e-c7d8-13c7-8484-4ac65e1b71e8}:Alsi.App.Edge
++FreeSql
i:{32f4afd7-a5c8-4d09-a996-c849891054dc}:
++FreeSql.Provider.Sqlite
i:{32f4afd7-a5c8-4d09-a996-c849891054dc}:
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:
++PresentationCore
i:{32f4afd7-a5c8-4d09-a996-c849891054dc}:
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{9f3c92dc-e277-4374-b637-7c6df32fceb0}:
++PresentationFramework
i:{32f4afd7-a5c8-4d09-a996-c849891054dc}:
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{9f3c92dc-e277-4374-b637-7c6df32fceb0}:
++System.Data.SQLite
i:{32f4afd7-a5c8-4d09-a996-c849891054dc}:
++System.Xaml
i:{32f4afd7-a5c8-4d09-a996-c849891054dc}:
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{9f3c92dc-e277-4374-b637-7c6df32fceb0}:
++WindowsBase
i:{32f4afd7-a5c8-4d09-a996-c849891054dc}:
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{9f3c92dc-e277-4374-b637-7c6df32fceb0}:
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
++MainView.xaml
i:{32f4afd7-a5c8-4d09-a996-c849891054dc}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit\views\mainview.xaml
++App.xaml.cs
i:{32f4afd7-a5c8-4d09-a996-c849891054dc}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit\app.xaml.cs
++Resources.Designer.cs
i:{32f4afd7-a5c8-4d09-a996-c849891054dc}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit\properties\resources.designer.cs
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\properties\resources.designer.cs
i:{9f3c92dc-e277-4374-b637-7c6df32fceb0}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.edge\properties\resources.designer.cs
++Settings.Designer.cs
i:{32f4afd7-a5c8-4d09-a996-c849891054dc}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit\properties\settings.designer.cs
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\properties\settings.designer.cs
i:{9f3c92dc-e277-4374-b637-7c6df32fceb0}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.edge\properties\settings.designer.cs
++MainView.xaml.cs
i:{32f4afd7-a5c8-4d09-a996-c849891054dc}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit\views\mainview.xaml.cs
++UnitTests
i:{00000000-0000-0000-0000-000000000000}:UnitTests
++Alsi.Tab.Kit.UnitTests
i:{9da7597c-820b-49a7-8f6f-7f7861237a5b}:Alsi.Tab.Kit.UnitTests
++依赖项
i:{9e8c78a1-6d4a-4d48-9d98-9829354e0c44}:>1152
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:>1153
++Arxml
i:{9e8c78a1-6d4a-4d48-9d98-9829354e0c44}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.unittests\arxml\
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\arxml\
++Files
i:{9e8c78a1-6d4a-4d48-9d98-9829354e0c44}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.unittests\arxml\files\
++SDB22400_DDM_221218_AR-4.2.2_Unflattened.arxml
i:{9e8c78a1-6d4a-4d48-9d98-9829354e0c44}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.unittests\arxml\files\sdb22400_ddm_221218_ar-4.2.2_unflattened.arxml
++SDB2243301_ZCUDM_240409_AR-4.2.2_Unflattened.arxml
i:{9e8c78a1-6d4a-4d48-9d98-9829354e0c44}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.unittests\arxml\files\sdb2243301_zcudm_240409_ar-4.2.2_unflattened.arxml
++ArxmlUnitTest.cs
i:{9e8c78a1-6d4a-4d48-9d98-9829354e0c44}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.unittests\arxml\arxmlunittest.cs
++ArxmlUnitTestBase.cs
i:{9e8c78a1-6d4a-4d48-9d98-9829354e0c44}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.unittests\arxml\arxmlunittestbase.cs
++SignalUnitTest.cs
i:{9e8c78a1-6d4a-4d48-9d98-9829354e0c44}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.unittests\arxml\signalunittest.cs
++CaplResources
i:{9e8c78a1-6d4a-4d48-9d98-9829354e0c44}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.unittests\caplresources\
++ParamDemo1.cin
i:{9e8c78a1-6d4a-4d48-9d98-9829354e0c44}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.unittests\caplresources\paramdemo1.cin
++ParamDemo2.cin
i:{9e8c78a1-6d4a-4d48-9d98-9829354e0c44}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.unittests\caplresources\paramdemo2.cin
++CaplUtils_ParseTests.cs
i:{9e8c78a1-6d4a-4d48-9d98-9829354e0c44}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.unittests\caplutils_parsetests.cs
++CaplUtils_ReplaceTests.cs
i:{9e8c78a1-6d4a-4d48-9d98-9829354e0c44}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.unittests\caplutils_replacetests.cs
++Usings.cs
i:{9e8c78a1-6d4a-4d48-9d98-9829354e0c44}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.unittests\usings.cs
++包
i:{9e8c78a1-6d4a-4d48-9d98-9829354e0c44}:>1165
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:>1212
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:>1224
++分析器
i:{9e8c78a1-6d4a-4d48-9d98-9829354e0c44}:>1158
++框架
i:{9e8c78a1-6d4a-4d48-9d98-9829354e0c44}:>1163
++项目
i:{9e8c78a1-6d4a-4d48-9d98-9829354e0c44}:>1156
++coverlet.collector (3.1.2)
i:{9e8c78a1-6d4a-4d48-9d98-9829354e0c44}:>1171
++Microsoft.NET.Test.Sdk (17.1.0)
i:{9e8c78a1-6d4a-4d48-9d98-9829354e0c44}:>1167
++Shouldly (4.3.0)
i:{9e8c78a1-6d4a-4d48-9d98-9829354e0c44}:>1166
++System.Text.Encoding.CodePages (5.0.0)
i:{9e8c78a1-6d4a-4d48-9d98-9829354e0c44}:>1168
++xunit (2.4.1)
i:{9e8c78a1-6d4a-4d48-9d98-9829354e0c44}:>1169
++xunit.runner.visualstudio (2.4.3)
i:{9e8c78a1-6d4a-4d48-9d98-9829354e0c44}:>1170
++Microsoft.CodeAnalysis.CSharp.NetAnalyzers
i:{9e8c78a1-6d4a-4d48-9d98-9829354e0c44}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
++Microsoft.CodeAnalysis.NetAnalyzers
i:{9e8c78a1-6d4a-4d48-9d98-9829354e0c44}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
++System.Text.Json.SourceGeneration
i:{9e8c78a1-6d4a-4d48-9d98-9829354e0c44}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\6.0.36\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
++xunit.analyzers
i:{9e8c78a1-6d4a-4d48-9d98-9829354e0c44}:d:\nuget_packages\xunit.analyzers\0.10.0\analyzers\dotnet\cs\xunit.analyzers.dll
++Microsoft.NETCore.App
i:{9e8c78a1-6d4a-4d48-9d98-9829354e0c44}:>1164
++Common
i:{00000000-0000-0000-0000-000000000000}:Common
++Midwares
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.database\midwares\
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\midwares\
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\midwares\
++AppExtensions.cs
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.database\appextensions.cs
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\appextensions.cs
i:{9f3c92dc-e277-4374-b637-7c6df32fceb0}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.edge\appextensions.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\appextensions.cs
++DbEnv.cs
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.database\dbenv.cs
++DbContext.cs
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.database\midwares\dbcontext.cs
++DbContextManager.cs
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.database\midwares\dbcontextmanager.cs
++FreeSqlMidware.cs
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.database\midwares\freesqlmidware.cs
++Blf
i:{5f059b1d-fa34-4752-8dae-0b78e6ddf378}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.log\blf\
++Frames
i:{5f059b1d-fa34-4752-8dae-0b78e6ddf378}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.log\frames\
++IFrame.cs
i:{5f059b1d-fa34-4752-8dae-0b78e6ddf378}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.log\iframe.cs
++ILogWriter.cs
i:{5f059b1d-fa34-4752-8dae-0b78e6ddf378}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.log\ilogwriter.cs
++LogReader.cs
i:{5f059b1d-fa34-4752-8dae-0b78e6ddf378}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.log\logreader.cs
++LogWriter.cs
i:{5f059b1d-fa34-4752-8dae-0b78e6ddf378}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.log\logwriter.cs
++Serilog.Sinks.File
i:{5f059b1d-fa34-4752-8dae-0b78e6ddf378}:
i:{2bf46d86-9704-494a-8998-a478b601df80}:
++System.ComponentModel.DataAnnotations
i:{5f059b1d-fa34-4752-8dae-0b78e6ddf378}:
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
++System.Net.Http
i:{5f059b1d-fa34-4752-8dae-0b78e6ddf378}:
i:{2bf46d86-9704-494a-8998-a478b601df80}:
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
++BlfLogReader.cs
i:{5f059b1d-fa34-4752-8dae-0b78e6ddf378}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.log\blf\blflogreader.cs
++BlfLogWriter.cs
i:{5f059b1d-fa34-4752-8dae-0b78e6ddf378}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.log\blf\blflogwriter.cs
++BlfStructs.cs
i:{5f059b1d-fa34-4752-8dae-0b78e6ddf378}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.log\blf\blfstructs.cs
++CanFdFlags.cs
i:{5f059b1d-fa34-4752-8dae-0b78e6ddf378}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.log\frames\canfdflags.cs
++CanFdFrame.cs
i:{5f059b1d-fa34-4752-8dae-0b78e6ddf378}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.log\frames\canfdframe.cs
++CanFrame.cs
i:{5f059b1d-fa34-4752-8dae-0b78e6ddf378}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.log\frames\canframe.cs
++LinFrame.cs
i:{5f059b1d-fa34-4752-8dae-0b78e6ddf378}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.log\frames\linframe.cs
++TabFrame.cs
i:{5f059b1d-fa34-4752-8dae-0b78e6ddf378}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.log\frames\tabframe.cs
++ExplorerController.cs
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\controllers\explorercontroller.cs
++AppExceptionHandlerMidware.cs
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\midwares\appexceptionhandlermidware.cs
++ProcessMutexMidware.cs
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\midwares\processmutexmidware.cs
++Themes
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\themes\
++Generic.xaml
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\themes\generic.xaml
++Utils
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\utils\
i:{9f3c92dc-e277-4374-b637-7c6df32fceb0}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.edge\utils\
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\utils\
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\utils\
++UiUtils.cs
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\utils\uiutils.cs
++WindowUtils.cs
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\utils\windowutils.cs
++WindowUtils.RemoveIcon.cs
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\utils\windowutils.removeicon.cs
++WindowEx.xaml
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\views\windowex.xaml
++WindowEnv.cs
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\windowenv.cs
++System.Design
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
++System.Drawing
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.drawing.dll
++System.Security
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
++WindowEx.xaml.cs
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\views\windowex.xaml.cs
++EdgeResourceManager.cs
i:{9f3c92dc-e277-4374-b637-7c6df32fceb0}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.edge\utils\edgeresourcemanager.cs
++EdgeBrowser.xaml
i:{9f3c92dc-e277-4374-b637-7c6df32fceb0}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.edge\views\edgebrowser.xaml
++EdgeBrowser.xaml.cs
i:{9f3c92dc-e277-4374-b637-7c6df32fceb0}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.edge\views\edgebrowser.xaml.cs
++EdgeMidware.cs
i:{9f3c92dc-e277-4374-b637-7c6df32fceb0}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.edge\edgemidware.cs
++Core
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\core\
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\openissue\core\
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\td\core\
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\filters\core\
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.core\services\capl\core\
++Log
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\log\
++AppEnv.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\appenv.cs
++AppOptions.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\appoptions.cs
++WebHostApp.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\webhostapp.cs
++App
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\controllers\app\
++ControllerBase.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\controllers\controllerbase.cs
++ErrorData.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\controllers\errordata.cs
++AppMidwareManager.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\core\appmidwaremanager.cs
++IApiAssembly.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\core\iapiassembly.cs
++IAppMidware.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\core\iappmidware.cs
++ILogger.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\log\ilogger.cs
++LogCategory.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\log\logcategory.cs
++LogConsts.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\log\logconsts.cs
++LogEntry.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\log\logentry.cs
++MemoryLogger.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\log\memorylogger.cs
++ApiHost
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\midwares\apihost\
++Serilog
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\midwares\serilog\
++ContentTypeUtils.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\utils\contenttypeutils.cs
++PortUtils.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\utils\portutils.cs
++AppController.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\controllers\app\appcontroller.cs
++AppInfo.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\controllers\app\appinfo.cs
++ApiHostMidware.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\midwares\apihost\apihostmidware.cs
++ApiHostStartup.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\midwares\apihost\apihoststartup.cs
++LoggingHandler.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\midwares\apihost\logginghandler.cs
++SerilogLogger.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\midwares\serilog\seriloglogger.cs
++SerilogMidware.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\midwares\serilog\serilogmidware.cs
++Dbc
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\
++Ldf
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\ldf\
++OpenIssue
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\openissue\
++Sddb
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\sddb\
++Td
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\td\
++Vbf
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\vbf\
++NPOI
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
++SharpZipLib
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
i:{2bf46d86-9704-494a-8998-a478b601df80}:
++System.Configuration
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
++System.Data.OracleClient
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
++System.Net
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
++System.Runtime.Serialization
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.runtime.serialization.dll
++System.ServiceProcess
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
++System.Threading.Tasks.Extensions
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
++System.Transactions
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
++ModelsParsers
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\arxml\modelsparsers\
++ArxmlParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\arxml\arxmlparser.cs
++AUTOSAR_4-2-2.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\arxml\autosar_4-2-2.cs
++AutosarExtension.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\arxml\autosarextension.cs
++AutosarStaticExtension.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\arxml\autosarstaticextension.cs
++Parsers
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\parsers\
++DbcParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\dbcparser.cs
++Model
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\ldf\model\
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\vbf\model\
++LdfParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\ldf\ldfparser.cs
++OpenIssueParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\openissue\openissueparser.cs
++Project.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\sddb\project.cs
++SddbParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\sddb\sddbparser.cs
++TinyProject.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\sddb\tinyproject.cs
++TdParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\td\tdparser.cs
++TdResult.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\td\tdresult.cs
++ColumnExAttribute.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\utils\columnexattribute.cs
++ColumnExInfo.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\utils\columnexinfo.cs
++VbfParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\vbf\vbfparser.cs
++ClusterBusType.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\arxml\models\clusterbustype.cs
++CommunicationCluster.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\arxml\models\communicationcluster.cs
++Direction.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\arxml\models\direction.cs
++Frame.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\arxml\models\frame.cs
++Pdu.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\arxml\models\pdu.cs
++Signal.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\arxml\models\signal.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\models\signal.cs
++CommunicationClusterParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\arxml\modelsparsers\communicationclusterparser.cs
++AttributeDef.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\models\attributedef.cs
++AttributeType.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\models\attributetype.cs
++CustomProperty.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\models\customproperty.cs
++DbcModel.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\models\dbcmodel.cs
++DbcValueType.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\models\dbcvaluetype.cs
++EnvAccessibility.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\models\envaccessibility.cs
++EnvDataType.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\models\envdatatype.cs
++EnvironmentVariable.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\models\environmentvariable.cs
++Message.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\models\message.cs
++Node.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\models\node.cs
++SignalExtension.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\models\signalextension.cs
++SignalGroup.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\models\signalgroup.cs
++StatisticInfo.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\models\statisticinfo.cs
++ValTable.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\models\valtable.cs
++AttributeDefDefParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\parsers\attributedefdefparser.cs
++AttributeDefParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\parsers\attributedefparser.cs
++AttributeParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\parsers\attributeparser.cs
++BaudrateParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\parsers\baudrateparser.cs
++CommentParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\parsers\commentparser.cs
++EnvironmentDataVariableParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\parsers\environmentdatavariableparser.cs
++EnvironmentVariableParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\parsers\environmentvariableparser.cs
++IParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\parsers\iparser.cs
++MessageParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\parsers\messageparser.cs
++NewSymbolParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\parsers\newsymbolparser.cs
++NodeParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\parsers\nodeparser.cs
++ParserContext.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\parsers\parsercontext.cs
++SignalGroupParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\parsers\signalgroupparser.cs
++SignalParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\parsers\signalparser.cs
++SignalValueTypeParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\parsers\signalvaluetypeparser.cs
++ValParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\parsers\valparser.cs
++ValTableParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\parsers\valtableparser.cs
++VersionParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\parsers\versionparser.cs
++LdfFrame.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\ldf\model\ldfframe.cs
++LdfModel.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\ldf\model\ldfmodel.cs
++LdfNode.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\ldf\model\ldfnode.cs
++LdfScheduleTable.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\ldf\model\ldfscheduletable.cs
++LdfSignal.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\ldf\model\ldfsignal.cs
++OpenIssueModel.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\openissue\core\openissuemodel.cs
++OpenIssueRow.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\openissue\core\openissuerow.cs
++TdModel.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\td\core\tdmodel.cs
++TdRow.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\td\core\tdrow.cs
++VbfBlock.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\vbf\model\vbfblock.cs
++VbfErase.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\vbf\model\vbferase.cs
++VbfModel.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\vbf\model\vbfmodel.cs
++VbfOutputModel.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\vbf\model\vbfoutputmodel.cs
++Autosar
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\autosar\
++Consoles
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\consoles\
++Cryptography
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\cryptography\
++Filters
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\filters\
++Linq
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\linq\
++Reflection
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\reflection\
++Threads
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\threads\
++Timers
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\timers\
++AppException.cs
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\appexception.cs
++ArrayUtils.cs
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\arrayutils.cs
++ByteUtils.cs
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\byteutils.cs
++DirExtension.cs
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\dirextension.cs
++DisplayAttribute.cs
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\displayattribute.cs
++EnumUtils.cs
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\enumutils.cs
++EnvUtils.cs
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\envutils.cs
++HashUtils.cs
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\hashutils.cs
++HexExtension.cs
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\hexextension.cs
++JsonUtils.cs
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\jsonutils.cs
++NativeDllUtils.cs
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\nativedllutils.cs
++NumberOptions.cs
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\numberoptions.cs
++NumberUtils.cs
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\numberutils.cs
++PathUtils.cs
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\pathutils.cs
++ProgressValue.cs
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\progressvalue.cs
++StringUtils.cs
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\stringutils.cs
++ValidationUtils.cs
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\validationutils.cs
++XmlUtils.cs
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\xmlutils.cs
++net462
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:>1154
++netstandard2.0
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:>1155
++DlcUtils.cs
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\autosar\dlcutils.cs
++ConsoleEx.cs
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\consoles\consoleex.cs
++Option.cs
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\consoles\option.cs
++AesUtils.cs
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\cryptography\aesutils.cs
++CryptFile.cs
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\cryptography\cryptfile.cs
++CryptFileIndex.cs
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\cryptography\cryptfileindex.cs
++CryptFileUtils.cs
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\cryptography\cryptfileutils.cs
++Sha256Utils.cs
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\cryptography\sha256utils.cs
++FilterUtils.cs
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\filters\filterutils.cs
++IFilter.cs
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\filters\ifilter.cs
++LinqExtension.cs
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\linq\linqextension.cs
++AssemblyLoader.CacheItem.cs
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\reflection\assemblyloader.cacheitem.cs
++AssemblyLoader.cs
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\reflection\assemblyloader.cs
++AssemblyLoader.Dependeny.cs
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\reflection\assemblyloader.dependeny.cs
++TypeUtils.cs
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\reflection\typeutils.cs
++AsyncRelayer.cs
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\threads\asyncrelayer.cs
++ProcessUtils.cs
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\threads\processutils.cs
++ThreadUtils.cs
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\threads\threadutils.cs
++TimeWindow.cs
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\threads\timewindow.cs
++TimeWindowCounter.cs
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\threads\timewindowcounter.cs
++HighResolutionTimer.cs
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\timers\highresolutiontimer.cs
++MultimediaTimer.cs
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\timers\multimediatimer.cs
++NativeMethods.cs
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\timers\nativemethods.cs
++程序集
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:>1214
++CaseIdFilter.cs
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\filters\core\caseidfilter.cs
++FrameIdFilter.cs
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\filters\core\frameidfilter.cs
++FrameIdRange.cs
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\filters\core\frameidrange.cs
++NameFilter.cs
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\filters\core\namefilter.cs
++Newtonsoft.Json (13.0.3)
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:newtonsoft.json/13.0.3
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:>1225
++System.IO.Compression.FileSystem
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.io.compression.filesystem.dll
++NETStandard.Library (2.0.3)
i:{d45049c2-92f3-a823-ad5f-89ee4256d61b}:>1226
++ParamTypeAttribute.cs
++Microsoft.AspNet.WebApi.OwinSelfHost
i:{2bf46d86-9704-494a-8998-a478b601df80}:
++Serilog.Sinks.Console
i:{2bf46d86-9704-494a-8998-a478b601df80}:
++Microsoft.Web.WebView2
i:{9f3c92dc-e277-4374-b637-7c6df32fceb0}:
++MahApps.Metro.IconPacks.Material
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
++Ookii.Dialogs.Wpf
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
++CaplParamAttribute.cs
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.core\services\capl\caplparamattribute.cs
++ParamCollection.cs
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.core\services\capl\paramcollection.cs
++CaplParamUtils.cs
++ArxmlIdsLoader.cs
++NewFolder1
++ArxmlLoader
++ArxmlLoaders
++SddbLoaders
++CaplParamLoader.cs
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.core\services\capl\caplparamloader.cs
++Base
++ArxmlLoaderContext.cs
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.core\services\capl\core\arxmlloadercontext.cs
++ArxmlLoaderBase.cs
i:{eefba350-c69e-4e95-aff2-f5637e80de37}:d:\src\005_tab\2、src\1、source code\alsi.tab.kit\alsi.tab.kit.core\services\capl\core\arxmlloaderbase.cs
