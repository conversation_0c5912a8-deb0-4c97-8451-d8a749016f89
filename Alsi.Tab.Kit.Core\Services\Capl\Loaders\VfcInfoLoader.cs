﻿using Alsi.Common.Utils;
using Alsi.Tab.Kit.Core.Services.Capl.Core;
using System.Collections.Generic;
using System.Linq;


namespace Alsi.Tab.Kit.Core.Services.Capl.Params
{
    internal class VfcInfoLoader : ArxmlLoaderBase
    {
        public VfcInfoLoader(ArxmlLoaderContext context)
            : base(context)
        {
        }

        public override void Load()
        {
            var ecuInstances = arxmlModel.EcuInstances;
            var pncMappings = arxmlModel.PncMappings;
            foreach (var ecuInstance in ecuInstances)
            {
                var ecuNode = ecuInstance.SHORTNAME.Value;

                var vfcPncDictionary = new Dictionary<string, List<int>>();
                foreach (var pncMapping in pncMappings)
                {
                    if (!byte.TryParse(pncMapping.PNCIDENTIFIER.Value, out var byteValue))
                    {
                        continue;
                    }

                    if (pncMapping.VFCIREFS == null)
                    {
                        continue;
                    }

                    foreach (var portGroup in pncMapping.VFCIREFS)
                    {
                        var vfcName = portGroup.TARGETREF.Value.Split(new[] { '/' }).LastOrDefault();
                        if (string.IsNullOrWhiteSpace(vfcName))
                        {
                            continue;
                        }

                        if (!vfcPncDictionary.ContainsKey(vfcName))
                        {
                            vfcPncDictionary[vfcName] = new List<int>();
                        }

                        if (!vfcPncDictionary[vfcName].Contains(byteValue))
                        {
                            vfcPncDictionary[vfcName].Add(byteValue);
                        }
                    }
                }

                var vfcInfo = vfcPncDictionary
                    .Select(x => new Vfc(x.Key, x.Value))
                    .OrderBy(x => x.VfcName)
                    .ToArray();

                var value = JsonUtils.Serialize(vfcInfo);
                var key = CaplParamConsts.VfcInfo;
                collection.AddValue(ecuNode, key, value, source, order);
            }
        }
    }
}
