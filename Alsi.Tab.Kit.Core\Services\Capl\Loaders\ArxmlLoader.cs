﻿using Alsi.Common.Utils;
using Alsi.Tab.Kit.Core.Services.Capl.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using ArxmlModel = AUTOSAR;

namespace Alsi.Tab.Kit.Core.Services.Capl.Params
{
    internal class ArxmlLoader : ArxmlLoaderBase
    {
        public ArxmlLoader(ArxmlLoaderContext context)
            : base(context)
        {

        }

        public override void Load()
        {
            var canFrames = arxmlModel.CanFrames;
            foreach (var canFrame in canFrames)
            {
                if (!int.TryParse(canFrame.FRAMELENGTH.Value, out var length))
                {
                    continue;
                }

                var name = canFrame.SHORTNAME.Value;
                var match = Regex.Match(name, @"([A-Z][a-z]+)BodyNMFr");
                if (!match.Success)
                {
                    continue;
                }

                var ecuNode = match.Groups[1].Value.ToUpper();
                var value = length.ToString();
                var key = CaplParamConsts.BUS_CANNM_DLC;
                collection.AddValue(ecuNode, key, value, source, order);
            }

            var ecuInstances = arxmlModel.EcuInstances;
            foreach (var ecuInstance in ecuInstances)
            {
                var ecuNode = ecuInstance.SHORTNAME.Value;

                var canCommunicationConnectors = arxmlModel.GetCanCommunicationConnectors(ecuInstance);
                var clusterPncGatewayTypes = new List<ClusterPncGatewayType>();
                foreach (var canCommunicationConnector in canCommunicationConnectors)
                {
                    PncGatewayType? pncGatewayType = null;
                    switch (canCommunicationConnector.PNCGATEWAYTYPE?.Value)
                    {
                        case PNCGATEWAYTYPEENUMSIMPLE.ACTIVE:
                            pncGatewayType = PncGatewayType.Active;
                            break;
                        case PNCGATEWAYTYPEENUMSIMPLE.NONE:
                            pncGatewayType = PncGatewayType.None;
                            break;
                        case PNCGATEWAYTYPEENUMSIMPLE.PASSIVE:
                            pncGatewayType = PncGatewayType.Passive;
                            break;
                        default:
                            break;
                    }
                    if (pncGatewayType != null)
                    {
                        clusterPncGatewayTypes.Add(new ClusterPncGatewayType(canCommunicationConnector.SHORTNAME.Value, pncGatewayType.Value));
                    }
                }
                var value = clusterPncGatewayTypes.ToArray();
                var key = CaplParamConsts.PNC_GATEWAY_LIST;
                collection.AddValue(ecuNode, key, JsonUtils.Serialize(value), ecuNode, order);
            }

            var canFrameTriggerings = arxmlModel.CanFrameTriggerings;
            foreach (var canFrameTriggering in canFrameTriggerings)
            {
                if (!uint.TryParse(canFrameTriggering.IDENTIFIER.Value, out var id))
                {
                    continue;
                }

                var name = canFrameTriggering.SHORTNAME.Value;
                var match = Regex.Match(name, @"ToAllFunc\w*DiagReqFrame");
                if (match.Success)
                {
                    var value = id.ToString();
                    var key = CaplParamConsts.CanDiagFuncReqId;

                    // 为所有 ECU 添加诊断请求功能寻址数据
                    var ecus = collection.GetEcuNodes();
                    foreach (var ecu in ecus)
                    {
                        collection.AddValue(ecu.EcuNode, key, value, source, order);
                    }

                    continue;
                }

                match = Regex.Match(name, @"To([A-Z][a-z]+)\w*DiagReqFrame");
                if (match.Success)
                {
                    var ecuNode = match.Groups[1].Value.ToUpper();
                    var value = id.ToString();
                    var key = CaplParamConsts.CanDiagReqId;
                    collection.AddValue(ecuNode, key, value, source, order);
                    continue;
                }

                match = Regex.Match(name, @"([A-Z][a-z]+)To\w*DiagResFrame");
                if (match.Success)
                {
                    var ecuNode = match.Groups[1].Value.ToUpper();
                    var value = id.ToString();
                    var key = CaplParamConsts.CanDiagResId;
                    collection.AddValue(ecuNode, key, value, source, order);
                    continue;
                }

                LoadNmFrameIdByNmPdu(arxmlModel, source, collection, order, canFrameTriggering, id);
            }
        }

        private void LoadNmFrameIdByNmPdu(ArxmlModel arxmlModel, string source, IParamCollection collection, int order,
            CANFRAMETRIGGERING canFrameTriggering, uint id)
        {
            var name = canFrameTriggering.SHORTNAME.Value;
            if (!name.ToUpper().Contains("NM"))
            {
                return;
            }

            if (arxmlModel.TryGetCommunicationDirection((int)id, out var isOut)
                && isOut == true)
            {
                foreach (var pduTriggeringRef in canFrameTriggering.PDUTRIGGERINGS)
                {
                    var pduTriggeringName = pduTriggeringRef.PDUTRIGGERINGREF.GetRefNodeName();
                    var nmPduTriggering = arxmlModel.PduTriggerings.FirstOrDefault(
                        x => x.SHORTNAME.Value == pduTriggeringName && x.IPDUREF.DEST == PDUSUBTYPESENUM.NMPDU);
                    if (nmPduTriggering == null)
                    {
                        continue;
                    }

                    foreach (var framePortRef in canFrameTriggering.FRAMEPORTREFS)
                    {
                        var items = framePortRef.Value.Split(new[] { '/' }, StringSplitOptions.RemoveEmptyEntries);
                        if (items.Length >= 2 && items[0].Equals("EcuInstances", StringComparison.OrdinalIgnoreCase))
                        {
                            var ecuName = items[1];
                            var value = JsonUtils.Serialize(new[] { id });
                            var key = CaplParamConsts.DUT_NM_MSG_ID_LIST;
                            collection.AddValue(ecuName, key, value, source, order);
                            return;
                        }
                    }
                }
            }
        }
    }
}
