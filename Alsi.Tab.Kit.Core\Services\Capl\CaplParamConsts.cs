﻿namespace Alsi.Tab.Kit.Core.Services.Capl.Params
{
    public static class CaplParamConsts
    {
        [CaplParam(typeof(uint))]
        public const string CARCONFIG_FRAMEID = nameof(CARCONFIG_FRAMEID);

        [CaplParam(typeof(uint))]
        public const string CARCONFIGEXTEND_FRAMEID = nameof(CARCONFIGEXTEND_FRAMEID);

        [CaplParam(typeof(uint[]))]
        public const string DUT_NM_MSG_ID_LIST = nameof(DUT_NM_MSG_ID_LIST);

        [CaplParam(typeof(uint))]
        public const string CanDiagReqId = nameof(CanDiagReqId);

        [CaplParam(typeof(uint))]
        public const string CanDiagResId = nameof(CanDiagResId);

        [CaplParam(typeof(uint))]
        public const string CanDiagFuncReqId = nameof(CanDiagFuncReqId);

        [CaplParam(typeof(ClusterPncGatewayType[]))]
        public const string PNC_GATEWAY_LIST = nameof(PNC_GATEWAY_LIST);

        [CaplParam(typeof(byte))]
        public const string BUS_CANNM_DLC = nameof(BUS_CANNM_DLC);

        [CaplParam(typeof(CanBusType))]
        public const string CanBusType = nameof(CanBusType);

        [CaplParam(typeof(BusType))]
        public const string BusType = nameof(BusType);

        [CaplParam(typeof(ClusterBustypeMap[]))]
        public const string ClusterBustypeMaps = nameof(ClusterBustypeMaps);

        [CaplParam(typeof(uint))]
        public const string CARCONFIGDID = nameof(CARCONFIGDID);

        [CaplParam(typeof(uint))]
        public const string DIDCNT = nameof(DIDCNT);

        [CaplParam(typeof(uint))]
        public const string CCSPL_NOTCONFIGDTC = nameof(CCSPL_NOTCONFIGDTC);

        [CaplParam(typeof(uint))]
        public const string CCSPL_INVALIDDTC = nameof(CCSPL_INVALIDDTC);

        [CaplParam(typeof(DiagRouting[]))]
        public const string DiagRouting = nameof(DiagRouting);

        [CaplParam(typeof(STRU_DID[]))]
        public const string MandatoryDIDs0x22 = nameof(MandatoryDIDs0x22);

        [CaplParam(typeof(STRU_DID[]))]
        public const string SupportDIDs0x22 = nameof(SupportDIDs0x22);

        [CaplParam(typeof(STRU_DID[]))]
        public const string SupportDIDs0x2E = nameof(SupportDIDs0x2E);

        [CaplParam(typeof(Sw[]))]
        public const string Sws = nameof(Sws);

        [CaplParam(typeof(uint))]
        public const string BUS_OFF_DTC_CODE = nameof(BUS_OFF_DTC_CODE);

        [CaplParam(typeof(STRU_DTC_EXTDATA[]))]
        public const string DtcExtData = nameof(DtcExtData);

        [CaplParam(typeof(uint[]))]
        public const string Dtcs = nameof(Dtcs);

        [CaplParam(typeof(MissingFrameDtcInformation[]))]
        public const string MissingFrameDtcs = nameof(MissingFrameDtcs);

        [CaplParam(typeof(ClusterE2EFrame[]))]
        public const string E2ECanFrameTxList = nameof(E2ECanFrameTxList);

        [CaplParam(typeof(ClusterE2EFrame[]))]
        public const string E2ECanFrameRxList = nameof(E2ECanFrameRxList);

        [CaplParam(typeof(E2EFrame))]
        public const string CarUsageModeFrame = nameof(CarUsageModeFrame);

        [CaplParam(typeof(E2EFrame[]))]
        public const string E2ELinFrameTxList = nameof(E2ELinFrameTxList);

        [CaplParam(typeof(E2EFrame[]))]
        public const string E2ELinFrameRxList = nameof(E2ELinFrameRxList);

        [CaplParam(typeof(E2EFrame))]
        public const string UbatSimulationFrame = nameof(UbatSimulationFrame);

        [CaplParam(typeof(E2EFrame))]
        public const string VehicleSpeedFrame = nameof(VehicleSpeedFrame);

        [CaplParam(typeof(string))]
        public const string NormalScheduleTableName = nameof(NormalScheduleTableName);

        [CaplParam(typeof(byte))]
        public const string DIAG_LIN_PHYSIC_REQ_NAD = nameof(DIAG_LIN_PHYSIC_REQ_NAD);

        [CaplParam(typeof(bool))]
        public const string IsMasterDUT = nameof(IsMasterDUT);

        [CaplParam(typeof(P4SeverTime[]))]
        public const string P4SeverTimeMatrix = nameof(P4SeverTimeMatrix);

        [CaplParam(typeof(ClusterPncInfo[]))]
        public const string ClusterPncInfos = nameof(ClusterPncInfos);

        [CaplParam(typeof(byte[]))]
        public const string SecurityLevels = nameof(SecurityLevels);

        [CaplParam(typeof(SignalRouting[]))]
        public const string SignalRouting = nameof(SignalRouting);

        [CaplParam(typeof(byte))]
        public const string DtcStatusAvailabilityMask = nameof(DtcStatusAvailabilityMask);

        [CaplParam(typeof(int))]
        public const string P2Time = nameof(P2Time);

        [CaplParam(typeof(int))]
        public const string P2ExtTime = nameof(P2ExtTime);

        [CaplParam(typeof(int))]
        public const string P2ProTime = nameof(P2ProTime);

        [CaplParam(typeof(int))]
        public const string BackgroundTestP2Time = nameof(BackgroundTestP2Time);

        [CaplParam(typeof(int))]
        public const string BackgroundTestP2ExtTime = nameof(BackgroundTestP2ExtTime);

        [CaplParam(typeof(int))]
        public const string BackgroundTestP2ProTime = nameof(BackgroundTestP2ProTime);

        [CaplParam(typeof(int))]
        public const string S3ServerTime = nameof(S3ServerTime);

        [CaplParam(typeof(int))]
        public const string N_As = nameof(N_As);

        [CaplParam(typeof(int))]
        public const string N_Ar = nameof(N_Ar);

        [CaplParam(typeof(int))]
        public const string N_Bs = nameof(N_Bs);

        [CaplParam(typeof(int))]
        public const string N_Cr = nameof(N_Cr);

        [CaplParam(typeof(Vfc[]))]
        public const string VfcInfo = nameof(VfcInfo);

        [CaplParam(typeof(DTCInformation))]
        public const string OverVoltageDtc = nameof(OverVoltageDtc);

        [CaplParam(typeof(DTCInformation))]
        public const string UnderVoltageDtc = nameof(UnderVoltageDtc);

        [CaplParam(typeof(DTCInformation))]
        public const string SupplyVoltageComparedFailureDtc = nameof(SupplyVoltageComparedFailureDtc);
    }
}
