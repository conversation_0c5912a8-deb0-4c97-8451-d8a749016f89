using Alsi.Tab.Kit.Core.Models;
using Alsi.Tab.Kit.Web.Controllers;
using Alsi.Tab.Kit.Web.Dto;
using AutoMapper;

namespace Alsi.Tab.Kit.Web.Mapping
{
    public class MappingProfile : Profile
    {
        public MappingProfile()
        {
            CreateMap<TestMode, TestModeDto>();

            CreateMap<ExternalApp, ExternalAppDto>()
                .ForMember(dest => dest.IconPath, opt => opt.MapFrom(src => src.FullIconPath));

            CreateMap<CinTemplate, CinTemplateDto>();

            CreateMap<SourceFile, SourceFileDto>();
            CreateMap<ParsedParam, ParsedParamDto>();
        }
    }
}
