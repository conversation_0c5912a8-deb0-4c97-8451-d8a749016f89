﻿using Alsi.Common.Utils;
using Alsi.Tab.Kit.Core.Services.Capl.Core;
using System;
using System.Collections.Generic;
using System.Linq;


namespace Alsi.Tab.Kit.Core.Services.Capl.Params
{
    internal class PncInfoLoader : ArxmlLoaderBase
    {
        public PncInfoLoader(ArxmlLoaderContext context)
            : base(context)
        {
        }

        public class MyPncMessage
        {
            public string clusterName;
            public PncMessage pncMessage = new PncMessage();
            public MyPncMessage() { }
            public MyPncMessage(string clusterName, PncMessage pncMessage)
            {
                this.clusterName = clusterName;
                this.pncMessage = pncMessage;
            }
        }

        public override void Load()
        {
            var pncMappings = arxmlModel.PncMappings;
            var signalPduGroups = arxmlModel.SignalPduGroups;
            var ecuInstances = arxmlModel.EcuInstances;
            foreach (var ecuInstance in ecuInstances)
            {
                var ecuNode = ecuInstance.SHORTNAME.Value;
                var clusterPncInfos = new List<ClusterPncInfo>();
                var pncInfo = new PncInfo();
                var MyPncMessageList = new List<MyPncMessage>();

                foreach (var pncMapping in pncMappings)
                {

                    if (!byte.TryParse(pncMapping.PNCIDENTIFIER.Value, out var byteValue))
                    {
                        continue;
                    }

                    var pncGroupNameValues = pncMapping.PNCGROUPREFS.Select(x => x.Value).ToArray();
                    var signalPduClusterDictionary = new Dictionary<string, List<string>>();
                    foreach (var pncGroupNameValue in pncGroupNameValues)
                    {

                        var pncGroupName = pncGroupNameValue.Split(new[] { '/' }, StringSplitOptions.RemoveEmptyEntries).LastOrDefault();
                        var pncGroupNamePart = pncGroupName.Split(new[] { '_' }, StringSplitOptions.RemoveEmptyEntries);
                        var clusterName = string.Join("_", pncGroupNamePart.Skip(1).Take(pncGroupNamePart.Length - 2));
                        if (signalPduClusterDictionary.ContainsKey(clusterName))
                        {
                            signalPduClusterDictionary[clusterName].Add(pncGroupName);
                        }
                        else
                        {
                            signalPduClusterDictionary.Add(clusterName, new List<string>() { pncGroupName });
                        }
                    }

                    foreach (var clusterName in signalPduClusterDictionary)
                    {
                        var myPncMessage = new MyPncMessage();
                        foreach (var signalPduGroupName in clusterName.Value)
                        {
                            myPncMessage.pncMessage.Pnc = byteValue;
                            myPncMessage.clusterName = clusterName.Key;

                            if (string.IsNullOrWhiteSpace(signalPduGroupName))
                            {
                                continue;
                            }

                            var signalPduGroup = signalPduGroups
                                .FirstOrDefault(x => x.SHORTNAME.Value == signalPduGroupName);
                            if (signalPduGroup == null
                                || signalPduGroup.ISIGNALIPDUS == null)
                            {
                                continue;
                            }

                            var inFrameIds = new List<int>();
                            var outFrameIds = new List<int>();
                            foreach (var signalPdu in signalPduGroup.ISIGNALIPDUS)
                            {
                                var pduName = signalPdu.ISIGNALIPDUREF.Value.Split(new[] { '/' }, StringSplitOptions.RemoveEmptyEntries).LastOrDefault();

                                if (!arxmlModel.TryGetFrameIdBySignalPduName(pduName, out var frameId, out _))
                                {
                                    continue;
                                }

                                if (signalPduGroup.COMMUNICATIONDIRECTION.Value == COMMUNICATIONDIRECTIONTYPESIMPLE.IN)
                                {
                                    inFrameIds.Add(frameId);
                                }
                                else if (signalPduGroup.COMMUNICATIONDIRECTION.Value == COMMUNICATIONDIRECTIONTYPESIMPLE.OUT)
                                {
                                    outFrameIds.Add(frameId);
                                }
                            }

                            var inMappingList = myPncMessage.pncMessage.InMappingFrameIds.ToList();
                            inMappingList.AddRange(inFrameIds);
                            myPncMessage.pncMessage.InMappingFrameIds = inMappingList.ToArray();

                            var outMappingList = myPncMessage.pncMessage.OutMappingFrameIds.ToList();
                            outMappingList.AddRange(outFrameIds);
                            myPncMessage.pncMessage.OutMappingFrameIds = outMappingList.ToArray();

                        }
                        MyPncMessageList.Add(myPncMessage);
                    }
                }
                var clusterMyPncMessages = MyPncMessageList.GroupBy(x => x.clusterName).ToArray();

                foreach (var cluster in clusterMyPncMessages)
                {
                    var clusterPncInfo = new ClusterPncInfo();
                    clusterPncInfo.clusterName = cluster.Select(x => x.clusterName).FirstOrDefault();
                    var allInMappingFrameIds = cluster.Select(x => x.pncMessage).ToArray().SelectMany(x => x.InMappingFrameIds).Distinct().ToArray();
                    var allOutMappingFrameIds = cluster.Select(x => x.pncMessage).ToArray().SelectMany(x => x.OutMappingFrameIds).Distinct().ToArray();

                    var pncMessages = new List<PncMessage>();
                    foreach (var pncMessage in cluster.Select(x => x.pncMessage).ToArray())
                    {
                        pncMessage.NotRelatedOutMappingFrameIds = allOutMappingFrameIds.Except(pncMessage.OutMappingFrameIds).ToArray();
                        pncMessages.Add(pncMessage);
                    }
                    clusterPncInfo.pncInfo.PncMessages = pncMessages.ToArray();
                    clusterPncInfo.pncInfo.NoMappingFrameIds = arxmlModel.GetPncInfoNoMappingFrameIdsByCluster(cluster.Select(x => x.clusterName).FirstOrDefault());

                    clusterPncInfos.Add(clusterPncInfo);
                }

                var value = JsonUtils.Serialize(clusterPncInfos.ToArray());
                var key = CaplParamConsts.ClusterPncInfos;
                collection.AddValue(ecuNode, key, value, source, order);
            }
        }
    }
}
