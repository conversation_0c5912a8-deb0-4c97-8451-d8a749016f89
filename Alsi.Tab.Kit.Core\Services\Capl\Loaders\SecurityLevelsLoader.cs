﻿using Alsi.Common.Parsers.Sddb;
using Alsi.Common.Utils;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;


namespace Alsi.Tab.Kit.Core.Services.Capl.Params
{
    internal class SecurityLevelsLoader
    {
        public void Load(string ecuNode, string source, SW[] sws, IParamCollection collection, int order)
        {
            var sid = 0x27;
            var securityLevels = new List<byte>();
            foreach (var sw in sws)
            {
                var service = sw.Services.Service
                    .FirstOrDefault(x => byte.TryParse(x.ID, NumberStyles.HexNumber, null, out var serviceId) && serviceId == sid);
                if (service == null)
                {
                    continue;
                }

                var ids = service.Subfunctions.Subfunction
                    .Where(x => x.Name.Equals("Request Seed", StringComparison.OrdinalIgnoreCase)
                        && x.Sessions.Session.Any(session => int.TryParse(session.ID, out var sessionId) && sessionId == 3))
                    .Where(x => byte.TryParse(x.ID, out _))
                    .Select(x => byte.Parse(x.ID))
                    .ToArray();

                securityLevels.AddRange(ids);
            }

            var value = JsonUtils.Serialize(securityLevels.Distinct().OrderBy(x => x));
            collection.AddValue(ecuNode, CaplParamConsts.SecurityLevels, value, source, order);
        }
    }
}
