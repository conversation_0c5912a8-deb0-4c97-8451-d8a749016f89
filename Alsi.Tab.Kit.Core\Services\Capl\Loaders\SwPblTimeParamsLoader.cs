﻿using Alsi.Common.Parsers.Sddb;
using System.Linq;


namespace Alsi.Tab.Kit.Core.Services.Capl.Params
{
    internal class SwPblTimeParamsLoader
    {
        public void Load(string ecuNode, string source, SW sw, IParamCollection collection, int order)
        {
            const string programmingSessionName = "ProgrammingSession";
            var programmingSession = sw.SessionLayer.Session.FirstOrDefault(x => x.Name == programmingSessionName);
            if (programmingSession == null)
            {
                return;
            }

            // P2TimePro
            if (int.TryParse(programmingSession.P2ServerMax_default, out var P2ServerMax))
            {
                var key = CaplParamConsts.P2ProTime;
                collection.AddValue(ecuNode, key, P2ServerMax.ToString(), source, order);

                key = CaplParamConsts.BackgroundTestP2ProTime;
                collection.AddValue(ecuNode, key, P2ServerMax.ToString(), source, order);
            }
        }
    }
}
