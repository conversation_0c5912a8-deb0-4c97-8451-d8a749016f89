{"RootPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit", "ProjectFileName": "Alsi.Tab.Kit.csproj", "Configuration": "Debug|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "Views\\MainView.xaml.cs"}, {"SourceFile": "App.xaml.cs"}, {"SourceFile": "Properties\\AssemblyInfo.cs"}, {"SourceFile": "Properties\\Resources.Designer.cs"}, {"SourceFile": "Properties\\Settings.Designer.cs"}, {"SourceFile": "obj\\Debug\\.NETFramework,Version=v4.6.2.AssemblyAttributes.cs"}, {"SourceFile": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit\\obj\\Debug\\Views\\MainView.g.cs"}, {"SourceFile": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit\\obj\\Debug\\App.g.cs"}], "References": [{"Reference": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App.Database\\bin\\Debug\\Alsi.App.Database.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App.Database\\bin\\Debug\\Alsi.App.Database.dll"}, {"Reference": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App.Desktop\\bin\\Debug\\Alsi.App.Desktop.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App.Desktop\\bin\\Debug\\Alsi.App.Desktop.dll"}, {"Reference": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App\\bin\\Debug\\Alsi.App.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App\\bin\\Debug\\Alsi.App.dll"}, {"Reference": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App.Edge\\bin\\Debug\\Alsi.App.Edge.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App.Edge\\bin\\Debug\\Alsi.App.Edge.dll"}, {"Reference": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Utils\\bin\\Debug\\net462\\Alsi.Common.Utils.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Utils\\bin\\Debug\\net462\\Alsi.Common.Utils.dll"}, {"Reference": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\bin\\Debug\\Alsi.Tab.Kit.Web.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\bin\\Debug\\Alsi.Tab.Kit.Web.dll"}, {"Reference": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\packages\\FreeSql.3.5.106\\lib\\net451\\FreeSql.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\packages\\FreeSql.Provider.Sqlite.3.5.106\\lib\\net45\\FreeSql.Provider.Sqlite.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\Microsoft.CSharp.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\mscorlib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\PresentationCore.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\PresentationFramework.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\System.Data.DataSetExtensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\System.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\packages\\Stub.System.Data.SQLite.Core.NetFramework.1.0.115.5\\lib\\net46\\System.Data.SQLite.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\System.Xaml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\System.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\System.Xml.Linq.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\WindowsBase.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit\\bin\\Debug\\TabKit.exe", "OutputItemRelativePath": "TabKit.exe"}, {"OutputItemFullPath": "", "OutputItemRelativePath": ""}], "CopyToOutputEntries": []}