﻿using Alsi.Common.Utils;
using System.Linq;
using ArxmlModel = AUTOSAR;


namespace Alsi.Tab.Kit.Core.Services.Capl.Params
{
    internal class E2EUbatSimulationFrameLoader
    {
        public void Load(ArxmlModel arxmlModel, string source, IParamCollection collection, int order)
        {
            var nodes = arxmlModel.EcuInstances
                .Select(x => x.SHORTNAME.Value)
                .ToArray();

            foreach (var node in nodes)
            {
                const string targetSystemSignalGroupName = "VehBattU";
                if (!LoaderUtils.TryLoadE2ECanFrameFromArxml(arxmlModel, targetSystemSignalGroupName, true, out var e2eFrames))
                {
                    continue;
                }
                foreach (var e2eFrame in e2eFrames)
                {
                    var key = CaplParamConsts.UbatSimulationFrame;
                    var value = JsonUtils.Serialize(e2eFrame);
                    collection.AddValue(node, key, value, source, order);
                }
            }
        }
    }
}
