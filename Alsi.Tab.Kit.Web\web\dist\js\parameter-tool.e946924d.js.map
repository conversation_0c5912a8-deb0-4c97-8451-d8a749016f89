{"version": 3, "file": "js/parameter-tool.e946924d.js", "mappings": "0LAEA,MAAMA,EAAa,CCDZC,MAAM,kBDEPC,EAAa,CCAVD,MAAM,gBDCTE,EAAa,CCCRF,MAAM,0BDAXG,EAAa,CCONH,MAAM,0BDNbI,EAAa,CCOJJ,MAAM,iBDNfK,EAAa,CACjBC,IAAK,ECmBMN,MAAM,cDhBbO,EAAa,CCkBJP,MAAM,eDjBfQ,EAAa,CCuBNR,MAAM,oBDtBbS,EAAa,CC4BET,MAAM,oBD3BrBU,EAAc,CAClBJ,IAAK,ECsCwDN,MAAM,oBDnC/DW,EAAc,CAClBL,IAAK,ECwCsBN,MAAM,aDpC7B,SAAUY,EAAOC,EAAUC,EAAYC,EAAYC,EAAYC,EAAWC,GAC9E,MAAMC,GAAyBC,EAAAA,EAAAA,IAAkB,eAC3CC,GAAuBD,EAAAA,EAAAA,IAAkB,aACzCE,GAAwBF,EAAAA,EAAAA,IAAkB,cAC1CG,GAAsBH,EAAAA,EAAAA,IAAkB,YACxCI,GAA6BJ,EAAAA,EAAAA,IAAkB,mBAC/CK,GAAsBL,EAAAA,EAAAA,IAAkB,YACxCM,GAAqBN,EAAAA,EAAAA,IAAkB,WACvCO,GAAsBP,EAAAA,EAAAA,IAAkB,YACxCQ,GAA6BR,EAAAA,EAAAA,IAAkB,mBAC/CS,GAA8BT,EAAAA,EAAAA,IAAkB,oBAChDU,GAA8BV,EAAAA,EAAAA,IAAkB,oBAEtD,OAAQW,EAAAA,EAAAA,OCnCRC,EAAAA,EAAAA,IA4EM,MA5ENjC,EA4EM,EA1EJkC,EAAAA,EAAAA,IAiEM,MAjENhC,EAiEM,EA/DJgC,EAAAA,EAAAA,IA2DM,MA3DN/B,EA2DM,CDzBJY,EAAO,KAAOA,EAAO,ICjCrBmB,EAAAA,EAAAA,IAIM,OAJDjC,MAAM,kBAAgB,EACzBiC,EAAAA,EAAAA,IAAiB,UAAb,aACJA,EAAAA,EAAAA,IACM,OADDjC,MAAM,qBDkCT,KC9BJiC,EAAAA,EAAAA,IAUM,MAVN9B,EAUM,EATJ8B,EAAAA,EAAAA,IAQM,MARN7B,EAQM,CANIS,EAAAqB,WAAWC,OAAS,ID+BvBJ,EAAAA,EAAAA,OChCLK,EAAAA,EAAAA,IAE6CjB,EAAA,CD+BvCb,IAAK,EACL+B,WClCgBxB,EAAAyB,qBDmChB,sBAAuBxB,EAAO,KAAOA,EAAO,GAAMyB,GCnClC1B,EAAAyB,qBAAoBC,GAAEC,KAAK,QAASC,QAAS5B,EAAA6B,gBACpCC,YAAY,YAAYC,MAAA,sCACpDC,SAAQhC,EAAAiC,qBAAsBC,UAAA,IDwC1B,KAAM,EAAG,CAAC,aAAc,UAAW,eACtCC,EAAAA,EAAAA,IAAoB,IAAI,ICvC5BC,EAAAA,EAAAA,IAAuF5B,EAAA,CAA5E6B,KAAK,UAAUV,KAAK,QAASW,QAAOtC,EAAAuC,iBD4C5C,CACDC,SAASC,EAAAA,EAAAA,IC7CqD,IAAWxC,EAAA,KAAAA,EAAA,KD8CvEyC,EAAAA,EAAAA,IC9C4D,kBDgD9DC,EAAG,EACHC,GAAI,CAAC,IACJ,EAAG,CAAC,aCjDPR,EAAAA,EAAAA,IAC0C5B,EAAA,CAD/B6B,KAAK,UAAUV,KAAK,QAASW,QAAOtC,EAAA6C,cAAed,MAAA,uBAC3De,QAAS9C,EAAA+C,YDuDT,CACDP,SAASC,EAAAA,EAAAA,ICxDa,IAAMxC,EAAA,KAAAA,EAAA,KDyD1ByC,EAAAA,EAAAA,ICzDoB,aD2DtBC,EAAG,EACHC,GAAI,CAAC,IACJ,EAAG,CAAC,UAAW,iBCzDtBR,EAAAA,EAAAA,IAAoC3B,EAAA,CAAxBsB,MAAA,mBAGkB/B,EAAAgD,UAAU1B,OAAS,ID2D5CJ,EAAAA,EAAAA,OC3DLC,EAAAA,EAAAA,IAKM,MALN3B,EAKM,EAJJ4C,EAAAA,EAAAA,IAAyG1B,EAAA,CD4DnGc,WC5DaxB,EAAAiD,cD6Db,sBAAuBhD,EAAO,KAAOA,EAAO,GAAMyB,GC7DrC1B,EAAAiD,cAAavB,GAAEC,KAAK,QAAQG,YAAY,YAAYI,UAAA,GAAUH,MAAA,iBDkE1E,KAAM,EAAG,CAAC,gBCjEjBX,EAAAA,EAAAA,IAEM,MAFN1B,EAEM,EADJ0B,EAAAA,EAAAA,IAA0E,YAApE,MAAE8B,EAAAA,EAAAA,IAAGlD,EAAAmD,kBAAkB7B,QAAS,OAAG4B,EAAAA,EAAAA,IAAGlD,EAAAgD,UAAU1B,QAAS,OAAI,SDqEnEa,EAAAA,EAAAA,IAAoB,IAAI,IChE5Bf,EAAAA,EAAAA,IA4BM,MA5BNzB,EA4BM,EA3BJyC,EAAAA,EAAAA,IA0BWtB,EAAA,CA1BAsC,KAAMpD,EAAAmD,kBAAmBE,OAAA,GAAOtB,MAAA,6BAAmCJ,KAAK,SDsEhF,CACDa,SAASC,EAAAA,EAAAA,ICtET,IAA6E,EAA7EL,EAAAA,EAAAA,IAA6EzB,EAAA,CAA5D2C,KAAK,OAAOC,MAAM,MAAMC,MAAM,MAAM,8BACrDpB,EAAAA,EAAAA,IAA4EzB,EAAA,CAA3D2C,KAAK,OAAOC,MAAM,KAAKC,MAAM,MAAM,8BACpDpB,EAAAA,EAAAA,IAWkBzB,EAAA,CAXD4C,MAAM,MAAM,YAAU,ODoFlC,CCnFQf,SAAOC,EAAAA,EAAAA,IAQVgB,GARiB,EACvBrC,EAAAA,EAAAA,IAOM,MAPNxB,EAOM,CANaI,EAAA0D,mBAAmBD,EAAME,ODqFnCzC,EAAAA,EAAAA,OCrFPK,EAAAA,EAAAA,IAGYf,EAAA,CDmFJf,IAAK,ECtFmC4C,KAAK,OAAOV,KAAK,QAC9DW,QAAKZ,GAAE1B,EAAA4D,qBAAqBH,EAAME,KAAMxE,MAAM,eD0FxC,CACDqD,SAASC,EAAAA,EAAAA,IC3F8C,IAE/DxC,EAAA,KAAAA,EAAA,KD0FUyC,EAAAA,EAAAA,IC5FqD,WD8FvDC,EAAG,EACHC,GAAI,CAAC,IACJ,KAAM,CAAC,cACVT,EAAAA,EAAAA,IAAoB,IAAI,IC9F9BC,EAAAA,EAAAA,IAC0E1B,EAAA,CD+FtEc,WChGexB,EAAA6D,gBAAgBJ,EAAME,IAAIG,MDiGzC,sBAAwBpC,GCjGT1B,EAAA6D,gBAAgBJ,EAAME,IAAIG,MAAIpC,EAAGI,YAAY,QAAQH,KAAK,QAC1EK,SAAMN,GAAE1B,EAAA+D,kBAAkBN,EAAME,IAAIG,MAAO3E,MAAM,qBDqG/C,KAAM,EAAG,CAAC,aAAc,sBAAuB,iBAGtDwD,EAAG,KCpGPP,EAAAA,EAAAA,IAUkBzB,EAAA,CAVD4C,MAAM,QAAQC,MAAM,MAAM,4BD0GtC,CCzGQhB,SAAOC,EAAAA,EAAAA,IAMVgB,GANiB,CACZzD,EAAAgE,mBAAmBP,EAAME,IAAIG,QD2GjC5C,EAAAA,EAAAA,OC3GPC,EAAAA,EAAAA,IAKM,MALNtB,EAKM,EAJJuC,EAAAA,EAAAA,IAEUvB,EAAA,MD0GF2B,SAASC,EAAAA,EAAAA,IC3Gf,IAAY,EAAZL,EAAAA,EAAAA,IAAYxB,KD8GN+B,EAAG,KC5GXvB,EAAAA,EAAAA,IAAqD,aAAA8B,EAAAA,EAAAA,IAA5ClD,EAAAgE,mBAAmBP,EAAME,IAAIG,OAAI,QDgHrC5C,EAAAA,EAAAA,OC9GPC,EAAAA,EAAAA,IAA4C,OAA5CrB,EAA+B,aDgH/B6C,EAAG,MAGPA,EAAG,GACF,EAAG,CAAC,cC5GXP,EAAAA,EAAAA,IAAqDrB,EAAA,CAAnCkD,cAAcjE,EAAAkE,mBAAiB,6BAInD9B,EAAAA,EAAAA,IACsCpB,EAAA,CD6GpCQ,WC9GyBxB,EAAAmE,mBD+GzB,sBAAuBlE,EAAO,KAAOA,EAAO,GAAMyB,GC/GzB1B,EAAAmE,mBAAkBzC,GAAG,cAAa1B,EAAAoE,iBAAmB,gBAAepE,EAAAqE,oBAC5FC,cAActE,EAAAuE,mBDkHd,KAAM,EAAG,CAAC,aAAc,cAAe,gBAAiB,mBC/G3DnC,EAAAA,EAAAA,IACoEnB,EAAA,CDgHlEO,WCjHyBxB,EAAAwE,0BDkHzB,sBAAuBvE,EAAO,KAAOA,EAAO,GAAMyB,GClHzB1B,EAAAwE,0BAAyB9C,GAAG+C,MAAOzE,EAAA0E,yBAA2BC,UAAU,EAChGC,MAAO5E,EAAA6E,kBAAoBC,UAAS9E,EAAA+E,2BDsHpC,KAAM,EAAG,CAAC,aAAc,QAAS,QAAS,eAEjD,C,0IEjMA,MAAM7F,EAAa,CAAEC,MAAO,sBACtBC,EAAa,CAAED,MAAO,iBAa5B,OAA4B6F,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,mBACRC,MAAO,CACL1D,WAAY,CAAEa,KAAM8C,SACpBV,MAAO,CAAC,EACRE,SAAU,CAAEtC,KAAM8C,QAAS3C,SAAS,GACpCoC,MAAO,CAAEpC,QAAS,QAEpB4C,MAAO,CAAC,oBAAqB,eAAgB,WAC7CC,KAAAA,CAAMC,GAAgBC,KAAMC,ICc9B,MAAMN,EAAQI,EAMRC,EAAOC,EAOPC,GAAUC,EAAAA,EAAAA,KAAI,GACdC,GAAeD,EAAAA,EAAAA,IAAI,IAGnBE,GAAiBC,EAAAA,EAAAA,IAAS,KAC9B,GAAoB,OAAhBX,EAAMT,YAAkCqB,IAAhBZ,EAAMT,MAChC,MAAO,GAGT,GAA2B,kBAAhBS,EAAMT,MAEf,IACE,MAAMsB,EAASC,KAAKC,MAAMf,EAAMT,OAChC,OAAOuB,KAAKE,UAAUH,EAAQ,KAAM,E,CACpC,MAEA,OAAOb,EAAMT,K,MAIf,IACE,OAAOuB,KAAKE,UAAUhB,EAAMT,MAAO,KAAM,E,CACzC,MACA,OAAO0B,OAAOjB,EAAMT,M,KAM1B2B,EAAAA,EAAAA,IAAM,IAAMlB,EAAM1D,WAAa6E,IAC7BZ,EAAQhB,MAAQ4B,EACZA,IACFV,EAAalB,MAAQmB,EAAenB,UAIxC2B,EAAAA,EAAAA,IAAMX,EAAUY,IACdd,EAAK,oBAAqBc,MAG5BD,EAAAA,EAAAA,IAAM,IAAMlB,EAAMT,MAAO,KACnBgB,EAAQhB,QACVkB,EAAalB,MAAQmB,EAAenB,SAKxC,MAAM6B,EAAcA,KAClBb,EAAQhB,OAAQ,GAGZ8B,EAAe9B,IACdS,EAAMP,UACTY,EAAK,eAAgBd,IAInB+B,EAAgBA,KACftB,EAAMP,UACTY,EAAK,UAAWI,EAAalB,OAE/B6B,KDhBF,MAAO,CAACtG,EAAUC,KAChB,MAAMS,GAAsBH,EAAAA,EAAAA,IAAkB,YACxCC,GAAuBD,EAAAA,EAAAA,IAAkB,aACzCkG,GAAuBlG,EAAAA,EAAAA,IAAkB,aAE/C,OAAQW,EAAAA,EAAAA,OCtGRK,EAAAA,EAAAA,IAyBYkF,EAAA,CD8EVjF,WCtGSiE,EAAAhB,MDuGT,sBAAuBxE,EAAO,KAAOA,EAAO,GAAMyB,GCvGzC+D,EAAOhB,MAAA/C,GACfkD,MAAO5E,EAAA4E,MACRpB,MAAM,MACL,eAAc8C,EACf,wBDwGCI,EAAAA,EAAAA,IAAa,CACdlE,SAASC,EAAAA,EAAAA,ICvGT,IAUM,EAVNrB,EAAAA,EAAAA,IAUM,MAVNlC,EAUM,EATJkD,EAAAA,EAAAA,IAQE1B,EAAA,CDiGEc,WCxGOmE,EAAAlB,MDyGP,sBAAuBxE,EAAO,KAAOA,EAAO,GAAMyB,GCzG3CiE,EAAYlB,MAAA/C,GACrBW,KAAK,WACJsE,KAAM,GACNhC,SAAU3E,EAAA2E,SACV7C,YAAa9B,EAAA2E,SAAW,GAAK,YAC9B5C,MAAA,2CACC6E,QAAOL,GD0GL,KAAM,EAAG,CAAC,aAAc,WAAY,oBAG3C5D,EAAG,GACF,CC1GwB3C,EAAA2E,cDqIrBmB,EAzBA,CACEhC,KC7GK,SD8GL+C,IAAIpE,EAAAA,EAAAA,IC7GR,IAGM,EAHNrB,EAAAA,EAAAA,IAGM,MAHNhC,EAGM,EAFJgD,EAAAA,EAAAA,IAA8C5B,EAAA,CAAlC8B,QAAOgE,GAAW,CD+GtB9D,SAASC,EAAAA,EAAAA,IC/Ge,IAAExC,EAAA,KAAAA,EAAA,KDgHxByC,EAAAA,EAAAA,IChHsB,SDkHxBC,EAAG,EACHC,GAAI,CAAC,MClHbR,EAAAA,EAAAA,IAA+D5B,EAAA,CAApD6B,KAAK,UAAWC,QAAOkE,GDuHzB,CACDhE,SAASC,EAAAA,EAAAA,ICxHgC,IAAExC,EAAA,KAAAA,EAAA,KDyHzCyC,EAAAA,EAAAA,ICzHuC,SD2HzCC,EAAG,EACHC,GAAI,CAAC,SAIXnD,IAAK,OAGT,KAAM,CAAC,aAAc,UAE3B,I,UEtJA,MAAMqH,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,QCLA,MAAM5H,EAAa,CAAEC,MAAO,kBACtBC,EAAa,CAAED,MAAO,kBACtBE,EAAa,CAAEF,MAAO,kBACtBG,EAAa,CAAEH,MAAO,cACtBI,EAAa,CAAEJ,MAAO,oBACtBK,EAAa,CAAC,SACdE,EAAa,CAAEP,MAAO,iBAgB5B,OAA4B6F,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,mBACRC,MAAO,CACL1D,WAAY,CAAEa,KAAM8C,SACpB4B,WAAY,CAAC,EACbC,aAAc,CAAC,GAEjB5B,MAAO,CAAC,oBAAqB,gBAC7BC,KAAAA,CAAMC,GAAgBC,KAAMC,IC2C9B,MAAMN,EAAQI,EAGRC,EAAOC,EAMPC,GAAUC,EAAAA,EAAAA,KAAI,GACduB,GAAavB,EAAAA,EAAAA,IAAI,IACjBwB,GAAcxB,EAAAA,EAAAA,IAAI,IAClByB,GAAiBzB,EAAAA,EAAAA,IAAmB,IACpC0B,GAA0B1B,EAAAA,EAAAA,KAAI,GAC9B2B,GAAoB3B,EAAAA,EAAAA,IAAS,IAC7B4B,GAAa5B,EAAAA,EAAAA,MAGb6B,GAAU1B,EAAAA,EAAAA,IAAS,KACvB,MAAM2B,EAAO,IAAIC,IAAIvC,EAAM8B,aAAaU,IAAIC,GAAKA,EAAEC,UACnD,OAAOC,MAAMC,KAAKN,GAAMO,SAGpBC,GAAcnC,EAAAA,EAAAA,IAAS,KAC3B,IAAKX,EAAM6B,WACT,MAAO,SAGT,MAAMkB,EAAW/C,EAAM6B,WAAWkB,SAClC,MAAO,YAAYA,MAGfC,GAAiBrC,EAAAA,EAAAA,IAAS,KAC9B,IAAIsC,EAASjD,EAAM8B,aAQnB,GALIE,EAAYzC,QACd0D,EAASA,EAAOC,OAAOT,GAAKA,EAAEC,UAAYV,EAAYzC,QAIpDwC,EAAWxC,MAAO,CACpB,MAAM4D,EAASpB,EAAWxC,MAAM6D,cAChCH,EAASA,EAAOC,OAAOT,GACrBA,EAAE7D,KAAKwE,cAAcC,SAASF,IAC9BV,EAAEa,YAAYF,cAAcC,SAASF,G,CAIzC,OAAOF,KAIT/B,EAAAA,EAAAA,IAAM,IAAMlB,EAAM1D,WAAa6E,IAC7BZ,EAAQhB,MAAQ4B,EACZA,IAEFY,EAAWxC,MAAQ,GACnB0C,EAAe1C,MAAQ,GAEnB8C,EAAQ9C,MAAMnD,OAAS,IACzB4F,EAAYzC,MAAQ8C,EAAQ9C,MAAM,IAElCgE,EAAAA,EAAAA,IAAS,KACPC,WAMRtC,EAAAA,EAAAA,IAAMX,EAAUY,IACdd,EAAK,oBAAqBc,MAG5BD,EAAAA,EAAAA,IAAMc,EAAa,MAEjBuB,EAAAA,EAAAA,IAAS,KACPC,QAKJ,MAAMC,EAAkBA,KAEtBD,KAGIA,EAA4BA,KAEhC,GAAIpB,EAAW7C,MAAO,CACpB,MAAMmE,EAAmBV,EAAezD,MACxCmE,EAAiBC,QAAQlF,IACvB2D,EAAW7C,MAAMqE,mBAAmBnF,GAAK,I,GAKzC2C,EAAcA,KAClBb,EAAQhB,OAAQ,GAGZsE,EAAyBC,IAC7B7B,EAAe1C,MAAQuE,GAYnBC,EAAexE,GACL,OAAVA,QAA4BqB,IAAVrB,EACb,GAGF0B,OAAO1B,GAGVyE,EAAkBC,IACtB9B,EAAkB5C,MAAQ0E,EAAM1E,MAChC2C,EAAwB3C,OAAQ,GAG5B2E,EAAsBA,KACU,IAAhCjC,EAAe1C,MAAMnD,QAKzBiE,EAAK,eAAgB4B,EAAe1C,OACpC4E,EAAAA,GAAUC,QAAQ,QAAQnC,EAAe1C,MAAMnD,cAC/CgF,KANE+C,EAAAA,GAAUE,QAAQ,eDnCtB,MAAO,CAACvJ,EAAUC,KAChB,MAAMuJ,GAAuBjJ,EAAAA,EAAAA,IAAkB,aACzCkJ,GAAuBlJ,EAAAA,EAAAA,IAAkB,aACzCM,GAAqBN,EAAAA,EAAAA,IAAkB,WACvCG,GAAsBH,EAAAA,EAAAA,IAAkB,YACxCI,GAA6BJ,EAAAA,EAAAA,IAAkB,mBAC/CC,GAAuBD,EAAAA,EAAAA,IAAkB,aACzCO,GAAsBP,EAAAA,EAAAA,IAAkB,YACxCkG,GAAuBlG,EAAAA,EAAAA,IAAkB,aAE/C,OAAQW,EAAAA,EAAAA,OCnLRK,EAAAA,EAAAA,IA0DYkF,EAAA,CD0HVjF,WCpLkBiE,EAAAhB,MDqLlB,sBAAuBxE,EAAO,KAAOA,EAAO,GAAMyB,GCrLhC+D,EAAOhB,MAAA/C,GAAGkD,MAAOoD,EAAAvD,MAAajB,MAAM,MAAO,eAAc8C,EAAa,uBD0LvF,CC3IUoD,QAAMjH,EAAAA,EAAAA,IACf,IAKM,EALNrB,EAAAA,EAAAA,IAKM,MALN1B,EAKM,EAJJ0C,EAAAA,EAAAA,IAA8C5B,EAAA,CAAlC8B,QAAOgE,GAAW,CD6I5B9D,SAASC,EAAAA,EAAAA,IC7IqB,IAAExC,EAAA,KAAAA,EAAA,KD8I9ByC,EAAAA,EAAAA,IC9I4B,SDgJ9BC,EAAG,EACHC,GAAI,CAAC,MChJPR,EAAAA,EAAAA,IAEY5B,EAAA,CAFD6B,KAAK,UAAWC,QAAO8G,EAAsBO,SAAoC,IAA1BxC,EAAA1C,MAAenD,QDsJ9E,CACDkB,SAASC,EAAAA,EAAAA,ICvJoF,IACvF,EDuJJC,EAAAA,EAAAA,ICxJ2F,WACvFQ,EAAAA,EAAAA,IAAGiE,EAAA1C,MAAenD,QAAS,KACnC,KDwJEqB,EAAG,GACF,EAAG,CAAC,iBAGXH,SAASC,EAAAA,EAAAA,IC/MT,IA4CM,EA5CNrB,EAAAA,EAAAA,IA4CM,MA5CNlC,EA4CM,EA1CJkC,EAAAA,EAAAA,IAiBM,MAjBNhC,EAiBM,EAhBJgD,EAAAA,EAAAA,IAGYqH,EAAA,CD6MRjI,WChNgB0F,EAAAzC,MDiNhB,sBAAuBxE,EAAO,KAAOA,EAAO,GAAMyB,GCjNlCwF,EAAWzC,MAAA/C,GAAEC,KAAK,QAAQG,YAAY,SAASC,MAAA,gBAChEC,SAAQ2G,GDqNN,CACDnG,SAASC,EAAAA,EAAAA,ICrNA,IAAsB,GDsN5BvB,EAAAA,EAAAA,KAAW,ICtNhBC,EAAAA,EAAAA,IAAyEyI,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAAhDtC,EAAA9C,MAAPqF,KDuNJ5I,EAAAA,EAAAA,OCvNdK,EAAAA,EAAAA,IAAyEiI,EAAA,CAAtC/J,IAAKqK,EAAMvG,MAAOuG,EAAMrF,MAAOqF,GD2NzD,KAAM,EAAG,CAAC,QAAS,YACpB,QAENnH,EAAG,GACF,EAAG,CAAC,gBC5NTP,EAAAA,EAAAA,IAMW1B,EAAA,CDwNPc,WC9NeyF,EAAAxC,MD+Nf,sBAAuBxE,EAAO,KAAOA,EAAO,GAAMyB,GC/NnCuF,EAAUxC,MAAA/C,GAAEI,YAAY,YAAYI,UAAA,GAAUP,KAAK,QAAQI,MAAA,iBDoOzE,CCnOQgI,QAAMtH,EAAAA,EAAAA,IACf,IAEU,EAFVL,EAAAA,EAAAA,IAEUvB,EAAA,MDmON2B,SAASC,EAAAA,EAAAA,ICpOX,IAAU,EAAVL,EAAAA,EAAAA,KAAU4H,EAAAA,EAAAA,IAAAC,EAAAA,WDuORtH,EAAG,MAGPA,EAAG,GACF,EAAG,CAAC,gBCtOTvB,EAAAA,EAAAA,IAEM,MAFN/B,EAA4B,SACtB6D,EAAAA,EAAAA,IAAGiE,EAAA1C,MAAenD,QAAS,KAAC4B,EAAAA,EAAAA,IAAGgF,EAAAzD,MAAenD,QAAS,QAC7D,MAIFF,EAAAA,EAAAA,IAmBM,MAnBN9B,EAmBM,EAlBJ8C,EAAAA,EAAAA,IAiBWtB,EAAA,CDmNPoJ,QCpOU,aAAJxE,IAAI4B,EAAclE,KAAM8E,EAAAzD,MAAgB0F,OAAO,MAAOC,kBAAkBrB,EAChF,UAAQ,OAAOpH,KAAK,SD0OjB,CACDa,SAASC,EAAAA,EAAAA,IC1OX,IAA+C,EAA/CL,EAAAA,EAAAA,IAA+CzB,EAAA,CAA9B0B,KAAK,YAAYmB,MAAM,QACxCpB,EAAAA,EAAAA,IAA6EzB,EAAA,CAA5D2C,KAAK,OAAOC,MAAM,MAAMC,MAAM,MAAM,8BACrDpB,EAAAA,EAAAA,IAAiFzB,EAAA,CAAhE2C,KAAK,YAAYC,MAAM,KAAKC,MAAM,MAAM,8BACzDpB,EAAAA,EAAAA,IAWkBzB,EAAA,CAXD2C,KAAK,QAAQC,MAAM,MAAM,YAAU,OD4P7C,CC3PMf,SAAOC,EAAAA,EAAAA,IAChB,EADoBkB,SAAG,EACvBvC,EAAAA,EAAAA,IAOM,MAPN7B,EAOM,EANJ6C,EAAAA,EAAAA,IAEY5B,EAAA,CAFD6B,KAAK,OAAOV,KAAK,QAASW,QAAKZ,GAAEwH,EAAevF,GAAMxE,MAAM,eDiQhE,CACDqD,SAASC,EAAAA,EAAAA,IClQsE,IAErFxC,EAAA,KAAAA,EAAA,KDiQQyC,EAAAA,EAAAA,ICnQ6E,WDqQ/EC,EAAG,EACHC,GAAI,CAAC,IACJ,KAAM,CAAC,aCpQdxB,EAAAA,EAAAA,IAEO,QAFDjC,MAAM,mBAAoByF,MAAOqE,EAAYtF,EAAIc,SDwQhDvB,EAAAA,EAAAA,ICvQF+F,EAAYtF,EAAIc,QAAK,EAAAjF,OD0Q1BmD,EAAG,MAGPA,EAAG,GACF,EAAG,CAAC,cCzPbP,EAAAA,EAAAA,IAA8GiI,EAAA,CD6P1G7I,WC7PuB4F,EAAA3C,MD8PvB,sBAAuBxE,EAAO,KAAOA,EAAO,GAAMyB,GC9P3B0F,EAAuB3C,MAAA/C,GAAG+C,MAAO4C,EAAA5C,MAAoBE,UAAU,EAAMC,MAAM,ODkQjG,KAAM,EAAG,CAAC,aAAc,YAE7BjC,EAAG,GACF,EAAG,CAAC,aAAc,UAEvB,IE3TA,MAAM,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,QCNA,MAAMzD,EAAa,CCDZC,MAAM,sBDEPC,EAAa,CCDVD,MAAM,kBDETE,EAAa,CCARF,MAAM,kBDCXG,EAAa,CCKVH,MAAM,oBDJTI,EAAa,CAAC,SACdC,EAAa,CCcCL,MAAM,QDbpBO,EAAa,CC0BFP,MAAM,kBDxBjB,SAAUY,EAAOC,EAAUC,EAAYC,EAAYC,EAAYC,EAAWC,GAC9E,MAAMG,GAAuBD,EAAAA,EAAAA,IAAkB,aACzC+J,GAAoB/J,EAAAA,EAAAA,IAAkB,UACtCI,GAA6BJ,EAAAA,EAAAA,IAAkB,mBAC/CO,GAAsBP,EAAAA,EAAAA,IAAkB,YAE9C,OAAQW,EAAAA,EAAAA,OCfRC,EAAAA,EAAAA,IAwEM,MAxENjC,EAwEM,EAvEJkC,EAAAA,EAAAA,IAMM,MANNhC,EAMM,CDUJa,EAAO,KAAOA,EAAO,ICfrBmB,EAAAA,EAAAA,IAA8B,UAA1B,yBAAqB,KACzBA,EAAAA,EAAAA,IAGM,MAHN/B,EAGM,EAFJ+C,EAAAA,EAAAA,IAAgF5B,EAAA,CAArEmB,KAAK,QAAQU,KAAK,UAAWC,QAAOtC,EAAAuK,iBDmB5C,CACD/H,SAASC,EAAAA,EAAAA,ICpBqD,IAAIxC,EAAA,KAAAA,EAAA,KDqBhEyC,EAAAA,EAAAA,ICrB4D,WDuB9DC,EAAG,EACHC,GAAI,CAAC,IACJ,EAAG,CAAC,YCxBgE5C,EAAAwK,UAAUlJ,OAAS,ID0BrFJ,EAAAA,EAAAA,OC1BLK,EAAAA,EAAAA,IAA2Gf,EAAA,CD2BrGf,IAAK,EC3BAkC,KAAK,QAAQU,KAAK,SAAUC,QAAOtC,EAAAyK,mBD+BvC,CACDjI,SAASC,EAAAA,EAAAA,IChC8E,IAAExC,EAAA,KAAAA,EAAA,KDiCvFyC,EAAAA,EAAAA,ICjCqF,SDmCvFC,EAAG,EACHC,GAAI,CAAC,IACJ,EAAG,CAAC,cACPT,EAAAA,EAAAA,IAAoB,IAAI,QClChCf,EAAAA,EAAAA,IA8DM,MA9DN9B,EA8DM,EA7DJ8C,EAAAA,EAAAA,IA4DWtB,EAAA,CA5DAsC,KAAMpD,EAAAwK,UAAWnH,OAAA,GAAOtB,MAAA,eAAoBJ,KAAK,SD0CzD,CACDa,SAASC,EAAAA,EAAAA,IC1CT,IAIkB,EAJlBL,EAAAA,EAAAA,IAIkBzB,EAAA,CAJD4C,MAAM,KAAKC,MAAM,MD8C7B,CC7CQhB,SAAOC,EAAAA,EAAAA,IACmGgB,GAD5F,EACvBrB,EAAAA,EAAAA,IAAmHkI,EAAA,CAA1GjI,KAAMrC,EAAA0K,mBAAmBjH,EAAME,IAAIgH,UAAWhJ,KAAK,SDiDvD,CACDa,SAASC,EAAAA,EAAAA,IClDuD,IAAsC,EDmDpGC,EAAAA,EAAAA,KAAiBQ,EAAAA,EAAAA,ICnDgDO,EAAME,IAAIgH,SAASC,eAAW,KDqDjGjI,EAAG,GACF,KAAM,CAAC,WAEZA,EAAG,KCpDPP,EAAAA,EAAAA,IAMkBzB,EAAA,CAND4C,MAAM,MAAM,YAAU,ODyDlC,CCxDQf,SAAOC,EAAAA,EAAAA,IAGVgB,GAHiB,EACvBrC,EAAAA,EAAAA,IAEM,OAFDjC,MAAM,YAAayF,MAAOnB,EAAME,IAAIkH,MD4DpC,EC3DHzJ,EAAAA,EAAAA,IAAkD,OAAlD5B,GAAkD0D,EAAAA,EAAAA,IAA5BO,EAAME,IAAIsE,UAAQ,ID6DrC,EAAG1I,KAERoD,EAAG,KC1DPP,EAAAA,EAAAA,IAIkBzB,EAAA,CAJD4C,MAAM,KAAKC,MAAM,OD+D7B,CC9DQhB,SAAOC,EAAAA,EAAAA,IAC8FgB,GADvF,EACvBrB,EAAAA,EAAAA,IAA8GkI,EAAA,CAArGjI,KAAMrC,EAAA8K,iBAAiBrH,EAAME,IAAIoH,QAASpJ,KAAK,SDkEnD,CACDa,SAASC,EAAAA,EAAAA,ICnEmD,IAAqC,EDoE/FC,EAAAA,EAAAA,KAAiBQ,EAAAA,EAAAA,ICpE4ClD,EAAAgL,cAAcvH,EAAME,IAAIoH,SAAM,KDsE7FpI,EAAG,GACF,KAAM,CAAC,WAEZA,EAAG,KCrEPP,EAAAA,EAAAA,IAsCkBzB,EAAA,CAtCD4C,MAAM,KAAKC,MAAM,OD0E7B,CCzEQhB,SAAOC,EAAAA,EAAAA,IAmCVgB,GAnCiB,EACvBrC,EAAAA,EAAAA,IAkCM,MAlCN1B,EAkCM,CAhCI+D,EAAME,IAAIoH,SAAW/K,EAAAiL,iBAAiBC,SAAWzH,EAAME,IAAIoH,SAAW/K,EAAAiL,iBAAiBE,QD0ExFjK,EAAAA,EAAAA,OC3EPK,EAAAA,EAAAA,IAOYf,EAAA,CDqEJf,IAAK,EC1EX4C,KAAK,UACLV,KAAK,QACJW,QAAKZ,GAAE1B,EAAAoL,cAAc3H,EAAME,IAAI0H,KD4EzB,CACD7I,SAASC,EAAAA,EAAAA,IC5EhB,IAEDxC,EAAA,KAAAA,EAAA,KD2EUyC,EAAAA,EAAAA,IC7ET,aD+EOC,EAAG,EACHC,GAAI,CAAC,IACJ,KAAM,CAAC,cACVT,EAAAA,EAAAA,IAAoB,IAAI,GC7EtBsB,EAAME,IAAIoH,SAAW/K,EAAAiL,iBAAiBK,SD+EvCpK,EAAAA,EAAAA,OChFPK,EAAAA,EAAAA,IAOYf,EAAA,CD0EJf,IAAK,EC/EX4C,KAAK,UACLV,KAAK,QACJW,QAAKZ,GAAE1B,EAAAuL,oBAAoB9H,EAAME,MDiF3B,CACDnB,SAASC,EAAAA,EAAAA,ICjFhB,IAEDxC,EAAA,KAAAA,EAAA,KDgFUyC,EAAAA,EAAAA,IClFT,aDoFOC,EAAG,EACHC,GAAI,CAAC,IACJ,KAAM,CAAC,cACVT,EAAAA,EAAAA,IAAoB,IAAI,ICnF9BC,EAAAA,EAAAA,IAMY5B,EAAA,CALV6B,KAAK,UACLV,KAAK,QACJW,QAAKZ,GAAE1B,EAAAwL,mBAAmB/H,EAAME,IAAIkH,ODqFlC,CACDrI,SAASC,EAAAA,EAAAA,ICrFZ,IAEDxC,EAAA,KAAAA,EAAA,KDoFMyC,EAAAA,EAAAA,ICtFL,cDwFGC,EAAG,EACHC,GAAI,CAAC,IACJ,KAAM,CAAC,aCtFZR,EAAAA,EAAAA,IAMY5B,EAAA,CALV6B,KAAK,SACLV,KAAK,QACJW,QAAKZ,GAAE1B,EAAAyL,eAAehI,EAAME,IAAI0H,KDwF9B,CACD7I,SAASC,EAAAA,EAAAA,ICxFZ,IAEDxC,EAAA,KAAAA,EAAA,KDuFMyC,EAAAA,EAAAA,ICzFL,WD2FGC,EAAG,EACHC,GAAI,CAAC,IACJ,KAAM,CAAC,gBAGdD,EAAG,MAGPA,EAAG,GACF,EAAG,CAAC,YAGb,C,sBClFA,GAAe+I,EAAAA,EAAAA,IAAgB,CAC7B5H,KAAM,kBACNsB,MAAO,CAAC,gBACRC,KAAAA,CAAMH,GAAO,KAAEK,IAEb,MAAMiF,GAAY9E,EAAAA,EAAAA,IAAkB,IAG9BiG,EAAgBC,UACpB,IACE,MAAMC,QAAiBC,EAAAA,GAAOC,aAAaC,iBAC3CxB,EAAU/F,MAAQoH,EAASzI,I,CAC3B,MAAO6I,GACPC,QAAQD,MAAM,YAAaA,GAC3B5C,EAAAA,GAAU4C,MAAM,W,GAId1B,EAAkBqB,UACtB,IACE,MAAMC,QAAiBC,EAAAA,GAAOC,aAAaI,oBACrCC,EAAYP,EAASzI,KAEvBgJ,GAAaA,EAAU9K,OAAS,UAC5B+K,EAAaD,GACnB/C,EAAAA,GAAUC,QAAQ,QAAQ8C,EAAU9K,kB,CAEtC,MAAO2K,GACPC,QAAQD,MAAM,UAAWA,GACzB5C,EAAAA,GAAU4C,MAAM,S,GAIdI,EAAeT,UACnB,IACE,MAAMC,QAAiBC,EAAAA,GAAOC,aAAaO,eAAe,CAAEF,cACtDG,EAAWV,EAASzI,KAY1B,OATAmJ,EAAS1D,QAAQ2D,IACf,MAAMC,EAAgBjC,EAAU/F,MAAMiI,UAAUC,GAAKA,EAAEtB,KAAOmB,EAAKnB,IAC/DoB,GAAiB,EACnBjC,EAAU/F,MAAMgI,GAAiBD,EAEjChC,EAAU/F,MAAMmI,KAAKJ,KAIlBD,C,CACP,MAAON,GAEP,MADAC,QAAQD,MAAM,UAAWA,GACnBA,C,GAIJb,EAAgBQ,UACpB,IAEE,MAAMY,EAAOhC,EAAU/F,MAAMoI,KAAKF,GAAKA,EAAEtB,KAAOyB,GAC5CN,IACFA,EAAKzB,OAASE,EAAAA,GAAiB8B,SAGjC,MAAMlB,QAAiBC,EAAAA,GAAOC,aAAaiB,gBAAgB,CACzDF,WAGIG,EAAcpB,EAASzI,KAGvB8J,EAAQ1C,EAAU/F,MAAMiI,UAAUC,GAAKA,EAAEtB,KAAOyB,GAClDI,GAAS,IACX1C,EAAU/F,MAAMyI,GAASD,GAG3B5D,EAAAA,GAAUC,QAAQ,eAAe2D,EAAYjG,cAAc1F,QAAU,SAGjE2L,EAAYjG,cAAgBiG,EAAYjG,aAAa1F,OAAS,GAChEiE,EAAK,eAAgB0H,EAAaA,EAAYjG,a,CAEhD,MAAOiF,GACPC,QAAQD,MAAM,UAAWA,GACzB5C,EAAAA,GAAU4C,MAAM,UAGhB,MAAMO,EAAOhC,EAAU/F,MAAMoI,KAAKF,GAAKA,EAAEtB,KAAOyB,GAC5CN,IACFA,EAAKzB,OAASE,EAAAA,GAAiBE,M,GAK/BI,EAAuBiB,IACvBA,EAAKxF,cAAgBwF,EAAKxF,aAAa1F,OAAS,EAClDiE,EAAK,eAAgBiH,EAAMA,EAAKxF,cAEhCqC,EAAAA,GAAUE,QAAQ,cAIhBiC,EAAqBI,UACzB,UACQE,EAAAA,GAAOqB,SAASC,aAAaC,E,CACnC,MAAOpB,GACPC,QAAQD,MAAM,WAAYA,GAC1B5C,EAAAA,GAAU4C,MAAM,U,GAIdR,EAAiBG,UACrB,UAEQE,EAAAA,GAAOC,aAAauB,iBAAiB,CAAER,WAG7C,MAAMS,EAAY/C,EAAU/F,MAAMiI,UAAUC,GAAKA,EAAEtB,KAAOyB,GACtDS,GAAa,IACf/C,EAAU/F,MAAM+I,OAAOD,EAAW,GAClClE,EAAAA,GAAUC,QAAQ,W,CAEpB,MAAO2C,GACPC,QAAQD,MAAM,UAAWA,GACzB5C,EAAAA,GAAU4C,MAAM,S,GAIdxB,EAAoBmB,UACxB,UACQ6B,EAAAA,EAAaC,QAAQ,kBAAmB,KAAM,CAClDC,kBAAmB,KACnBC,iBAAkB,KAClBvL,KAAM,kBAIFyJ,EAAAA,GAAOC,aAAa8B,mBAG1BrD,EAAU/F,MAAQ,GAClB4E,EAAAA,GAAUC,QAAQ,c,CAClB,MAAO2C,GACO,WAAVA,IACFC,QAAQD,MAAM,UAAWA,GACzB5C,EAAAA,GAAU4C,MAAM,U,GAMhBvB,EAAsBC,IAC1B,OAAQA,GACN,KAAKmD,EAAAA,GAAeC,MAAO,MAAO,UAClC,KAAKD,EAAAA,GAAeE,KAAM,MAAO,UACjC,KAAKF,EAAAA,GAAeG,IAAK,MAAO,UAChC,QAAS,MAAO,KAIdnD,EAAoBC,IACxB,OAAQA,GACN,KAAKE,EAAAA,GAAiBC,QAAS,MAAO,GACtC,KAAKD,EAAAA,GAAiB8B,QAAS,MAAO,UACtC,KAAK9B,EAAAA,GAAiBK,OAAQ,MAAO,UACrC,KAAKL,EAAAA,GAAiBE,MAAO,MAAO,SACpC,QAAS,MAAO,KAIdH,EAAiBD,IACrB,OAAQA,GACN,KAAKE,EAAAA,GAAiBC,QAAS,MAAO,MACtC,KAAKD,EAAAA,GAAiB8B,QAAS,MAAO,MACtC,KAAK9B,EAAAA,GAAiBK,OAAQ,MAAO,MACrC,KAAKL,EAAAA,GAAiBE,MAAO,MAAO,OACpC,QAAS,OAAOJ,IAKdmD,EAAmBA,KACvBvC,KAQF,OAJAwC,EAAAA,EAAAA,IAAU,KACRxC,MAGK,CACLnB,YACAD,kBACAa,gBACAG,sBACAC,qBACAC,iBACAhB,oBACAC,qBACAI,mBACAE,gBACAkD,mBAEAjD,iBAAgBA,EAAAA,GAEpB,IC3RF,MAAM,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAAS,GAAQ,CAAC,YAAY,qBAEzF,QT0FA,GAAeS,EAAAA,EAAAA,IAAgB,CAC7B5H,KAAM,oBACNsK,WAAY,CACVC,iBAAgB,EAChBC,gBAAe,EACfjE,iBAAgB,EAChBkE,SAAQA,EAAAA,UAEVlJ,KAAAA,GAEE,MAAMmJ,GAAY9I,EAAAA,EAAAA,IAAmB,IAC/B+I,GAAa/I,EAAAA,EAAAA,IAAY,YACzBgJ,GAAmBhJ,EAAAA,EAAAA,IAAY,IAC/BiJ,GAAqBjJ,EAAAA,EAAAA,IAAY,IACjCjE,GAAuBiE,EAAAA,EAAAA,IAAc,IACrC2H,GAAW3H,EAAAA,EAAAA,IAAY,IACvB1C,GAAY0C,EAAAA,EAAAA,IAAoB,IAChC7B,GAAkB6B,EAAAA,EAAAA,IAA+B,CAAC,GAClDkJ,GAAiBlJ,EAAAA,EAAAA,IAAY,IAC7BzC,GAAgByC,EAAAA,EAAAA,IAAY,IAG5BmJ,GAAUnJ,EAAAA,EAAAA,KAAI,GACd3C,GAAa2C,EAAAA,EAAAA,KAAI,GAGjBvB,GAAqBuB,EAAAA,EAAAA,KAAI,GACzBtB,GAAmBsB,EAAAA,EAAAA,IAAuB,MAC1CrB,GAAsBqB,EAAAA,EAAAA,IAAmB,IACzCoJ,GAAmBpJ,EAAAA,EAAAA,IAA+B,CAAC,GAGnDqJ,GAAiBrJ,EAAAA,EAAAA,IAAiB,IAAI+B,KAGtCjD,GAA4BkB,EAAAA,EAAAA,KAAI,GAChChB,GAA2BgB,EAAAA,EAAAA,IAAY,IACvCsJ,GAA0BtJ,EAAAA,EAAAA,IAAY,IACtCb,GAAoBgB,EAAAA,EAAAA,IAAS,IAAM,UAAUmJ,EAAwBvK,SAGrEpD,GAAawE,EAAAA,EAAAA,IAAS,KAC1B,MAAMoJ,EAAc,IAAIxH,IAAI+G,EAAU/J,MAAMiD,IAAIwH,GAAKA,EAAEC,WACvD,OAAOtH,MAAMC,KAAKmH,GAAa7G,OAAOgH,GAAKA,KAGvCvN,GAAkBgE,EAAAA,EAAAA,IAAS,IACxBxE,EAAWoD,MAAMiD,IAAIyH,IAAO,CACjC1K,MAAO0K,EACP5L,MAAO4L,EACPE,SAAUb,EAAU/J,MACjB2D,OAAO8G,GAAKA,EAAEC,WAAaA,GAC3BzH,IAAI4H,IAAO,CACV7K,MAAO6K,EAASjE,GAChB9H,MAAO+L,EAASxL,YAKlByL,GAAoB1J,EAAAA,EAAAA,IAAS,IAC5B6I,EAAiBjK,MACf+J,EAAU/J,MAAM2D,OAAO8G,GAAKA,EAAEC,WAAaT,EAAiBjK,OAD/B,IAIhCtB,GAAoB0C,EAAAA,EAAAA,IAAS,KACjC,IAAK5C,EAAcwB,MACjB,OAAOzB,EAAUyB,MAEnB,MAAM+K,EAAUvM,EAAcwB,MAAM6D,cACpC,OAAOtF,EAAUyB,MAAM2D,OAAOqH,GAC5BA,EAAE3L,KAAKwE,cAAcC,SAASiH,MAI5BE,GAAsB7J,EAAAA,EAAAA,IAAS,IAAMkJ,EAAetK,MAAM9C,MAE1DgO,GAAoB9J,EAAAA,EAAAA,IAAS,IAAMkJ,EAAetK,MAAM9C,KAAO,GAG/DiO,EAAgBhE,UACpB,IACE,MAAMC,QAAiBC,EAAAA,GAAOC,aAAa8D,eAC3CrB,EAAU/J,MAAQoH,EAASzI,KAGvB/B,EAAWoD,MAAMnD,OAAS,IAC5BoN,EAAiBjK,MAAQpD,EAAWoD,MAAM,GAC1CqL,I,CAEF,MAAO7D,GACPC,QAAQD,MAAM,UAAWA,E,GAIvB8D,EAAqBA,KAEzB/M,EAAUyB,MAAQ,GAClBZ,EAAgBY,MAAQ,CAAC,EACzBmK,EAAenK,MAAQ,GACvBsK,EAAetK,MAAMuL,SAGjBF,EAAmBA,KACnBP,EAAkB9K,MAAMnD,OAAS,EACnCqN,EAAmBlK,MAAQ8K,EAAkB9K,MAAM,GAAG4G,GAEtDsD,EAAmBlK,MAAQ,IAIzBlC,EAAkBqJ,UACtB,IACE,MAAMC,QAAiBC,EAAAA,GAAOC,aAAakE,aAC3C,IAAKpE,EAASzI,KACZ,OAIFuL,EAAmBlK,MAAQ,GAC3BiK,EAAiBjK,MAAQ,GACzBhD,EAAqBgD,MAAQ,GAE7B4I,EAAS5I,MAAQoH,EAASzI,WAEpB8M,G,CACN,MAAOjE,GACPC,QAAQD,MAAM,UAAWA,E,GAIvBiE,EAAoBtE,UACxB,GAAKyB,EAAS5I,MAAd,CAEAoK,EAAQpK,OAAQ,EAChB,IACE,MAAM0L,EAAoC,CACxC1B,WAAY,OACZpB,SAAUA,EAAS5I,OAGfoH,QAAiBC,EAAAA,GAAOC,aAAaqE,UAAUD,GAC/CE,EAASxE,EAASzI,KAExBJ,EAAUyB,MAAQ4L,EAAOrN,UACzB4L,EAAenK,MAAQ4L,EAAOzB,eAG9B/K,EAAgBY,MAAQ,CAAC,EACzB4L,EAAOrN,UAAU6F,QAAQyH,IACvBzM,EAAgBY,MAAM6L,EAASxM,MAAQwM,EAAS7L,OAAS,KAG3DsK,EAAetK,MAAMuL,QACrB3G,EAAAA,GAAUC,QAAQ,QAAQ+G,EAAOrN,UAAU1B,a,CAC3C,MAAO2K,GACPC,QAAQD,MAAM,UAAWA,GACzB5C,EAAAA,GAAU4C,MAAM,S,CAClB,QACE4C,EAAQpK,OAAQ,C,CA3BS,GA+BvBxC,EAAwBwC,IACxBA,GAA0B,IAAjBA,EAAMnD,SACjBoN,EAAiBjK,MAAQA,EAAM,GAC/BkK,EAAmBlK,MAAQA,EAAM,GACjC8L,MAIEC,EAA0BrB,GACvBX,EAAU/J,MAAM2D,OAAO8G,GAAKA,EAAEC,WAAaA,GAG9CoB,EAAuB3E,UAC3B,GAAK+C,EAAmBlK,MAAxB,CAEAoK,EAAQpK,OAAQ,EAChB,IACE,MAAM0L,EAAoC,CACxC1B,WAAY,WACZgC,WAAY9B,EAAmBlK,MAC/B4I,SAAU,IAGNxB,QAAiBC,EAAAA,GAAOC,aAAaqE,UAAUD,GAC/CE,EAASxE,EAASzI,KAExBJ,EAAUyB,MAAQ4L,EAAOrN,UACzB4L,EAAenK,MAAQ4L,EAAOzB,eAG9B/K,EAAgBY,MAAQ,CAAC,EACzB4L,EAAOrN,UAAU6F,QAAQyH,IACvBzM,EAAgBY,MAAM6L,EAASxM,MAAQwM,EAAS7L,OAAS,KAG3DsK,EAAetK,MAAMuL,QACrB3G,EAAAA,GAAUC,QAAQ,UAAU+G,EAAOrN,UAAU1B,a,CAC7C,MAAO2K,GACPC,QAAQD,MAAM,UAAWA,GACzB5C,EAAAA,GAAU4C,MAAM,S,CAClB,QACE4C,EAAQpK,OAAQ,C,CA5BmB,GAgCjCV,EAAqB2M,IACzB3B,EAAetK,MAAMkM,IAAID,IAGrB7N,EAAgB+I,UACpB7I,EAAW0B,OAAQ,EACnB,IACE,MAAM0L,EAA+B,CACnC1B,WAAYA,EAAWhK,MACvBgM,WAAiC,aAArBhC,EAAWhK,MAAuBkK,EAAmBlK,WAAQqB,EACzEuH,SAA+B,SAArBoB,EAAWhK,MAAmB4I,EAAS5I,MAAQ,GACzDZ,gBAAiBA,EAAgBY,OAG7BoH,QAAiBC,EAAAA,GAAOC,aAAa6E,YAAYT,GACjDE,EAASxE,EAASzI,KAExBiG,EAAAA,GAAUC,QAAQ,WAClByF,EAAetK,MAAMuL,cAEflE,EAAAA,GAAOqB,SAASC,aAAaiD,EAAOQ,e,CAE1C,MAAO5E,GACPC,QAAQD,MAAM,UAAWA,GACzB5C,EAAAA,GAAU4C,MAAM,S,CAClB,QACElJ,EAAW0B,OAAQ,C,GAKjBP,EAAoBA,CAACsI,EAAkBrE,KAC3C/D,EAAiBK,MAAQ+H,EACzBnI,EAAoBI,MAAQ0D,EAC5BhE,EAAmBM,OAAQ,GAGvBF,EAAqB4D,IACzBA,EAAOU,QAAQM,IAEb,MAAMmH,EAAWtN,EAAUyB,MAAMoI,KAAK4C,GAAKA,EAAE3L,KAAKwE,gBAAkBa,EAAMrF,KAAKwE,eAC3EgI,IAEFzM,EAAgBY,MAAM6L,EAASxM,MAAQqC,OAAOgD,EAAM1E,OAEpDqK,EAAiBrK,MAAM6L,EAASxM,MAAQqF,EAAM2H,OAE9C/B,EAAetK,MAAMkM,IAAIL,EAASxM,SAMtCuF,EAAAA,GAAUC,QAAQ,QAAQnB,EAAO7G,eAG7B0C,EAAsB0M,GACnB5B,EAAiBrK,MAAMiM,GAI1BhN,EAAsB4M,IAC1B,MAAM7L,EAAQZ,EAAgBY,MAAM6L,EAASxM,MAC7C,IAAKW,EAAO,OAAO,EAGnB,MAAMsM,EAAetM,EAAMuM,OAC3B,GAAID,EAAaE,WAAW,MAAQF,EAAaxI,SAAS,KACxD,OAAO,EAIT,GAAIwI,EAAaE,WAAW,MAAQF,EAAaE,WAAW,KAC1D,IAEE,OADAjL,KAAKC,MAAM8K,IACJ,C,CACP,MAEA,OAAOA,EAAaxI,SAAS,MAAQwI,EAAaxI,SAAS,I,CAK/D,MAAMlG,EAAOiO,EAASjO,KAAKiG,cAC3B,OAAOjG,EAAKkG,SAAS,WAAalG,EAAKkG,SAAS,UAAYlG,EAAKkG,SAAS,OAItE3E,EAAwB0M,IAC5BtB,EAAwBvK,MAAQ6L,EAASxM,KACzCY,EAAyBD,MAAQZ,EAAgBY,MAAM6L,EAASxM,OAAS,GACzEU,EAA0BC,OAAQ,GAI9BM,EAA6BN,IAC7BuK,EAAwBvK,QAC1BZ,EAAgBY,MAAMuK,EAAwBvK,OAASA,EACvDV,EAAkBiL,EAAwBvK,SAIxCyM,EAAerG,GACdA,GACEA,EAAKsG,MAAM,SAASC,OADT,GAId1G,EAAsBC,IAC1B,OAAQA,GACN,KAAKmD,EAAAA,GAAeC,MAAO,MAAO,UAClC,KAAKD,EAAAA,GAAeE,KAAM,MAAO,UACjC,KAAKF,EAAAA,GAAeG,IAAK,MAAO,UAChC,QAAS,MAAO,KAIdoD,EAAyB7E,IAE7B,IAAI8E,EAAQ,EAMZ,OALAC,OAAOC,KAAK1C,EAAiBrK,OAAOoE,QAAQ6H,IACtC5B,EAAiBrK,MAAMiM,KAAelE,EAAKvE,UAC7CqJ,MAGGA,GAaT,OAJAnD,EAAAA,EAAAA,IAAUvC,gBACFgE,MAGD,CACLpB,YACAC,aACAC,mBACAC,qBACAlN,uBACA4L,WACArK,YACAa,kBACA+K,iBACA3L,gBACA4L,UACA9L,aACA1B,aACAQ,kBACA0N,oBACApM,oBACAuM,sBACAC,oBACAI,qBACAD,mBACAvN,kBACAN,uBACAuO,yBACAD,uBACAxM,oBACAlB,gBACAqO,cACAxG,qBACA2G,wBAEAlN,qBACAC,mBACAC,sBACAH,oBACAK,oBACAP,qBAEAN,qBACAE,uBACAmB,4BACAP,4BACAE,2BACAG,oBAEA0J,SAAQ,WACRtD,iBAAgBA,EAAAA,GAEpB,IUpeF,MAAM,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASlL,GAAQ,CAAC,YAAY,qBAEzF,O", "sources": ["webpack://tab-kit-web/./src/views/ParameterToolView.vue?bbcd", "webpack://tab-kit-web/./src/views/ParameterToolView.vue", "webpack://tab-kit-web/./src/components/ParamValueViewer.vue?2066", "webpack://tab-kit-web/./src/components/ParamValueViewer.vue", "webpack://tab-kit-web/./src/components/ParamValueViewer.vue?ab66", "webpack://tab-kit-web/./src/components/ParamParseDialog.vue?64b6", "webpack://tab-kit-web/./src/components/ParamParseDialog.vue", "webpack://tab-kit-web/./src/components/ParamParseDialog.vue?7eb6", "webpack://tab-kit-web/./src/components/DataFileManager.vue?eea6", "webpack://tab-kit-web/./src/components/DataFileManager.vue", "webpack://tab-kit-web/./src/components/DataFileManager.vue?b969", "webpack://tab-kit-web/./src/views/ParameterToolView.vue?3f0a"], "sourcesContent": ["import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, createTextVNode as _createTextVNode, withCtx as _withCtx, createVNode as _createVNode, toDisplayString as _toDisplayString, createElementBlock as _createElementBlock } from \"vue\"\n\nconst _hoisted_1 = { class: \"parameter-tool\" }\nconst _hoisted_2 = { class: \"main-content\" }\nconst _hoisted_3 = { class: \"cin-parameters-section\" }\nconst _hoisted_4 = { class: \"cin-operations-content\" }\nconst _hoisted_5 = { class: \"operation-row\" }\nconst _hoisted_6 = {\n  key: 0,\n  class: \"search-bar\"\n}\nconst _hoisted_7 = { class: \"filter-info\" }\nconst _hoisted_8 = { class: \"parameters-table\" }\nconst _hoisted_9 = { class: \"param-value-cell\" }\nconst _hoisted_10 = {\n  key: 0,\n  class: \"parameter-source\"\n}\nconst _hoisted_11 = {\n  key: 1,\n  class: \"no-source\"\n}\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  const _component_el_cascader = _resolveComponent(\"el-cascader\")!\n  const _component_el_button = _resolveComponent(\"el-button\")!\n  const _component_el_divider = _resolveComponent(\"el-divider\")!\n  const _component_el_input = _resolveComponent(\"el-input\")!\n  const _component_el_table_column = _resolveComponent(\"el-table-column\")!\n  const _component_document = _resolveComponent(\"document\")!\n  const _component_el_icon = _resolveComponent(\"el-icon\")!\n  const _component_el_table = _resolveComponent(\"el-table\")!\n  const _component_DataFileManager = _resolveComponent(\"DataFileManager\")!\n  const _component_ParamParseDialog = _resolveComponent(\"ParamParseDialog\")!\n  const _component_ParamValueViewer = _resolveComponent(\"ParamValueViewer\")!\n\n  return (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _createElementVNode(\"div\", _hoisted_2, [\n      _createElementVNode(\"div\", _hoisted_3, [\n        _cache[7] || (_cache[7] = _createElementVNode(\"div\", { class: \"section-header\" }, [\n          _createElementVNode(\"h4\", null, \"CIN 参数列表\"),\n          _createElementVNode(\"div\", { class: \"header-actions\" })\n        ], -1)),\n        _createElementVNode(\"div\", _hoisted_4, [\n          _createElementVNode(\"div\", _hoisted_5, [\n            (_ctx.categories.length > 0)\n              ? (_openBlock(), _createBlock(_component_el_cascader, {\n                  key: 0,\n                  modelValue: _ctx.selectedTemplatePath,\n                  \"onUpdate:modelValue\": _cache[0] || (_cache[0] = ($event: any) => ((_ctx.selectedTemplatePath) = $event)),\n                  size: \"small\",\n                  options: _ctx.cascaderOptions,\n                  placeholder: \"选择 CIN 模板\",\n                  style: {\"width\":\"260px\",\"margin-right\":\"16px\"},\n                  onChange: _ctx.handleTemplateChange,\n                  clearable: \"\"\n                }, null, 8, [\"modelValue\", \"options\", \"onChange\"]))\n              : _createCommentVNode(\"\", true),\n            _createVNode(_component_el_button, {\n              type: \"primary\",\n              size: \"small\",\n              onClick: _ctx.selectLocalFile\n            }, {\n              default: _withCtx(() => _cache[4] || (_cache[4] = [\n                _createTextVNode(\"选择本地 CIN 文件\")\n              ])),\n              _: 1,\n              __: [4]\n            }, 8, [\"onClick\"]),\n            _createVNode(_component_el_button, {\n              type: \"primary\",\n              size: \"small\",\n              onClick: _ctx.exportCinFile,\n              style: {\"margin-left\":\"auto\"},\n              loading: _ctx.processing\n            }, {\n              default: _withCtx(() => _cache[5] || (_cache[5] = [\n                _createTextVNode(\"导出 CIN\")\n              ])),\n              _: 1,\n              __: [5]\n            }, 8, [\"onClick\", \"loading\"])\n          ])\n        ]),\n        _createVNode(_component_el_divider, { style: {\"margin\":\"8px 0\"} }),\n        (_ctx.variables.length > 0)\n          ? (_openBlock(), _createElementBlock(\"div\", _hoisted_6, [\n              _createVNode(_component_el_input, {\n                modelValue: _ctx.searchKeyword,\n                \"onUpdate:modelValue\": _cache[1] || (_cache[1] = ($event: any) => ((_ctx.searchKeyword) = $event)),\n                size: \"small\",\n                placeholder: \"搜索参数名称...\",\n                clearable: \"\",\n                style: {\"width\":\"260px\"}\n              }, null, 8, [\"modelValue\"]),\n              _createElementVNode(\"div\", _hoisted_7, [\n                _createElementVNode(\"span\", null, \"共 \" + _toDisplayString(_ctx.filteredVariables.length) + \" / \" + _toDisplayString(_ctx.variables.length) + \" 个参数\", 1)\n              ])\n            ]))\n          : _createCommentVNode(\"\", true),\n        _createElementVNode(\"div\", _hoisted_8, [\n          _createVNode(_component_el_table, {\n            data: _ctx.filteredVariables,\n            border: \"\",\n            style: {\"width\":\"100%\",\"height\":\"100%\"},\n            size: \"small\"\n          }, {\n            default: _withCtx(() => [\n              _createVNode(_component_el_table_column, {\n                prop: \"name\",\n                label: \"参数名\",\n                width: \"200\",\n                \"show-overflow-tooltip\": \"\"\n              }),\n              _createVNode(_component_el_table_column, {\n                prop: \"type\",\n                label: \"类型\",\n                width: \"200\",\n                \"show-overflow-tooltip\": \"\"\n              }),\n              _createVNode(_component_el_table_column, {\n                label: \"参数值\",\n                \"min-width\": \"200\"\n              }, {\n                default: _withCtx((scope) => [\n                  _createElementVNode(\"div\", _hoisted_9, [\n                    (_ctx.isComplexParameter(scope.row))\n                      ? (_openBlock(), _createBlock(_component_el_button, {\n                          key: 0,\n                          type: \"text\",\n                          size: \"small\",\n                          onClick: ($event: any) => (_ctx.editComplexParameter(scope.row)),\n                          class: \"edit-button\"\n                        }, {\n                          default: _withCtx(() => _cache[6] || (_cache[6] = [\n                            _createTextVNode(\" 编辑 \")\n                          ])),\n                          _: 2,\n                          __: [6]\n                        }, 1032, [\"onClick\"]))\n                      : _createCommentVNode(\"\", true),\n                    _createVNode(_component_el_input, {\n                      modelValue: _ctx.parameterValues[scope.row.name],\n                      \"onUpdate:modelValue\": ($event: any) => ((_ctx.parameterValues[scope.row.name]) = $event),\n                      placeholder: \"输入参数值\",\n                      size: \"small\",\n                      onChange: ($event: any) => (_ctx.onParameterChange(scope.row.name)),\n                      class: \"param-value-input\"\n                    }, null, 8, [\"modelValue\", \"onUpdate:modelValue\", \"onChange\"])\n                  ])\n                ]),\n                _: 1\n              }),\n              _createVNode(_component_el_table_column, {\n                label: \"参数值来源\",\n                width: \"150\",\n                \"show-overflow-tooltip\": \"\"\n              }, {\n                default: _withCtx((scope) => [\n                  (_ctx.getParameterSource(scope.row.name))\n                    ? (_openBlock(), _createElementBlock(\"div\", _hoisted_10, [\n                        _createVNode(_component_el_icon, null, {\n                          default: _withCtx(() => [\n                            _createVNode(_component_document)\n                          ]),\n                          _: 1\n                        }),\n                        _createElementVNode(\"span\", null, _toDisplayString(_ctx.getParameterSource(scope.row.name)), 1)\n                      ]))\n                    : (_openBlock(), _createElementBlock(\"span\", _hoisted_11, \"CIN 文件\"))\n                ]),\n                _: 1\n              })\n            ]),\n            _: 1\n          }, 8, [\"data\"])\n        ])\n      ]),\n      _createVNode(_component_DataFileManager, { onParseResult: _ctx.handleParseResult }, null, 8, [\"onParseResult\"])\n    ]),\n    _createVNode(_component_ParamParseDialog, {\n      modelValue: _ctx.parseDialogVisible,\n      \"onUpdate:modelValue\": _cache[2] || (_cache[2] = ($event: any) => ((_ctx.parseDialogVisible) = $event)),\n      \"source-file\": _ctx.currentParseFile,\n      \"parsed-params\": _ctx.currentParsedParams,\n      onApplyParams: _ctx.applyParsedParams\n    }, null, 8, [\"modelValue\", \"source-file\", \"parsed-params\", \"onApplyParams\"]),\n    _createVNode(_component_ParamValueViewer, {\n      modelValue: _ctx.complexParamDialogVisible,\n      \"onUpdate:modelValue\": _cache[3] || (_cache[3] = ($event: any) => ((_ctx.complexParamDialogVisible) = $event)),\n      value: _ctx.currentComplexParamValue,\n      readonly: false,\n      title: _ctx.complexParamTitle,\n      onConfirm: _ctx.handleComplexParamConfirm\n    }, null, 8, [\"modelValue\", \"value\", \"title\", \"onConfirm\"])\n  ]))\n}", "<template>\n  <div class=\"parameter-tool\">\n    <!-- 参数编辑主区域 -->\n    <div class=\"main-content\">\n      <!-- CIN 参数列表卡片 -->\n      <div class=\"cin-parameters-section\">\n        <div class=\"section-header\">\n          <h4>CIN 参数列表</h4>\n          <div class=\"header-actions\">\n          </div>\n        </div>\n\n        <div class=\"cin-operations-content\">\n          <div class=\"operation-row\">\n            <el-cascader v-model=\"selectedTemplatePath\" size=\"small\" :options=\"cascaderOptions\"\n              v-if=\"categories.length > 0\" placeholder=\"选择 CIN 模板\" style=\"width: 260px; margin-right: 16px;\"\n              @change=\"handleTemplateChange\" clearable />\n\n            <el-button type=\"primary\" size=\"small\" @click=\"selectLocalFile\">选择本地 CIN 文件</el-button>\n            <el-button type=\"primary\" size=\"small\" @click=\"exportCinFile\" style=\"margin-left: auto;\"\n              :loading=\"processing\">导出 CIN</el-button>\n          </div>\n        </div>\n\n        <el-divider style=\"margin: 8px 0\" />\n\n        <!-- 搜索和过滤 -->\n        <div class=\"search-bar\" v-if=\"variables.length > 0\">\n          <el-input v-model=\"searchKeyword\" size=\"small\" placeholder=\"搜索参数名称...\" clearable style=\"width: 260px;\" />\n          <div class=\"filter-info\">\n            <span>共 {{ filteredVariables.length }} / {{ variables.length }} 个参数</span>\n          </div>\n        </div>\n\n        <!-- 参数表格 -->\n        <div class=\"parameters-table\">\n          <el-table :data=\"filteredVariables\" border style=\"width: 100%; height: 100%;\" size=\"small\">\n            <el-table-column prop=\"name\" label=\"参数名\" width=\"200\" show-overflow-tooltip />\n            <el-table-column prop=\"type\" label=\"类型\" width=\"200\" show-overflow-tooltip />\n            <el-table-column label=\"参数值\" min-width=\"200\">\n              <template #default=\"scope\">\n                <div class=\"param-value-cell\">\n                  <el-button v-if=\"isComplexParameter(scope.row)\" type=\"text\" size=\"small\"\n                    @click=\"editComplexParameter(scope.row)\" class=\"edit-button\">\n                    编辑\n                  </el-button>\n                  <el-input v-model=\"parameterValues[scope.row.name]\" placeholder=\"输入参数值\" size=\"small\"\n                    @change=\"onParameterChange(scope.row.name)\" class=\"param-value-input\" />\n                </div>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"参数值来源\" width=\"150\" show-overflow-tooltip>\n              <template #default=\"scope\">\n                <div v-if=\"getParameterSource(scope.row.name)\" class=\"parameter-source\">\n                  <el-icon>\n                    <document />\n                  </el-icon>\n                  <span>{{ getParameterSource(scope.row.name) }}</span>\n                </div>\n                <span v-else class=\"no-source\">CIN 文件</span>\n              </template>\n            </el-table-column>\n          </el-table>\n        </div>\n      </div>\n\n      <!-- 数据文件管理 -->\n      <DataFileManager @parse-result=\"handleParseResult\" />\n    </div>\n\n    <!-- 参数导入结果弹窗 -->\n    <ParamParseDialog v-model=\"parseDialogVisible\" :source-file=\"currentParseFile\" :parsed-params=\"currentParsedParams\"\n      @apply-params=\"applyParsedParams\" />\n\n    <!-- 复杂参数编辑弹窗 -->\n    <ParamValueViewer v-model=\"complexParamDialogVisible\" :value=\"currentComplexParamValue\" :readonly=\"false\"\n      :title=\"complexParamTitle\" @confirm=\"handleComplexParamConfirm\" />\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { defineComponent, ref, computed, onMounted } from \"vue\";\nimport {\n  appApi,\n  CinTemplate,\n  CaplVariable,\n  CinParameterParseRequest,\n  CinParameterRequest,\n  SourceFile,\n  SourceFileType,\n  SourceFileStatus,\n  ParsedParam\n} from \"@/api/appApi\";\nimport { ElMessage } from \"element-plus\";\nimport { Document } from '@element-plus/icons-vue';\nimport ParamParseDialog from \"@/components/ParamParseDialog.vue\";\nimport DataFileManager from \"@/components/DataFileManager.vue\";\nimport ParamValueViewer from \"@/components/ParamValueViewer.vue\";\n\nexport default defineComponent({\n  name: \"ParameterToolView\",\n  components: {\n    ParamParseDialog,\n    DataFileManager,\n    ParamValueViewer,\n    Document\n  },\n  setup() {\n    // 数据\n    const templates = ref<CinTemplate[]>([]);\n    const sourceType = ref<string>(\"template\");\n    const selectedCategory = ref<string>(\"\");\n    const selectedTemplateId = ref<string>(\"\");\n    const selectedTemplatePath = ref<string[]>([]);\n    const filePath = ref<string>(\"\");\n    const variables = ref<CaplVariable[]>([]);\n    const parameterValues = ref<{ [key: string]: string }>({});\n    const sourceFilePath = ref<string>(\"\");\n    const searchKeyword = ref<string>(\"\");\n\n    // 状态\n    const parsing = ref(false);\n    const processing = ref(false);\n\n    // 源文件管理相关\n    const parseDialogVisible = ref(false);\n    const currentParseFile = ref<SourceFile | null>(null);\n    const currentParsedParams = ref<ParsedParam[]>([]);\n    const parameterSources = ref<{ [key: string]: string }>({});\n\n    // 新增状态\n    const modifiedParams = ref<Set<string>>(new Set());\n\n    // 复杂参数编辑相关\n    const complexParamDialogVisible = ref(false);\n    const currentComplexParamValue = ref<string>('');\n    const currentComplexParamName = ref<string>('');\n    const complexParamTitle = computed(() => `编辑参数 - ${currentComplexParamName.value}`);\n\n    // 计算属性\n    const categories = computed(() => {\n      const categorySet = new Set(templates.value.map(t => t.category));\n      return Array.from(categorySet).filter(c => c);\n    });\n\n    const cascaderOptions = computed(() => {\n      return categories.value.map(category => ({\n        value: category,\n        label: category,\n        children: templates.value\n          .filter(t => t.category === category)\n          .map(template => ({\n            value: template.id,\n            label: template.name\n          }))\n      }));\n    });\n\n    const filteredTemplates = computed(() => {\n      if (!selectedCategory.value) return [];\n      return templates.value.filter(t => t.category === selectedCategory.value);\n    });\n\n    const filteredVariables = computed(() => {\n      if (!searchKeyword.value) {\n        return variables.value;\n      }\n      const keyword = searchKeyword.value.toLowerCase();\n      return variables.value.filter(v =>\n        v.name.toLowerCase().includes(keyword)\n      );\n    });\n\n    const modifiedParamsCount = computed(() => modifiedParams.value.size);\n\n    const hasUnsavedChanges = computed(() => modifiedParams.value.size > 0);\n\n    // 方法\n    const loadTemplates = async () => {\n      try {\n        const response = await appApi.cinParameter.getTemplates();\n        templates.value = response.data;\n\n        // 自动选择第一个类别和模板\n        if (categories.value.length > 0) {\n          selectedCategory.value = categories.value[0];\n          onCategoryChange();\n        }\n      } catch (error) {\n        console.error('加载模板失败:', error);\n      }\n    };\n\n    const onSourceTypeChange = () => {\n      // 清空之前的数据\n      variables.value = [];\n      parameterValues.value = {};\n      sourceFilePath.value = \"\";\n      modifiedParams.value.clear();\n    };\n\n    const onCategoryChange = () => {\n      if (filteredTemplates.value.length > 0) {\n        selectedTemplateId.value = filteredTemplates.value[0].id;\n      } else {\n        selectedTemplateId.value = \"\";\n      }\n    };\n\n    const selectLocalFile = async () => {\n      try {\n        const response = await appApi.cinParameter.selectFile();\n        if (!response.data) {\n          return;\n        }\n\n        // 清空之前的选择\n        selectedTemplateId.value = '';\n        selectedCategory.value = '';\n        selectedTemplatePath.value = [];\n\n        filePath.value = response.data;\n        // 自动解析选择的文件\n        await parseSelectedFile();\n      } catch (error) {\n        console.error('选择文件失败:', error);\n      }\n    };\n\n    const parseSelectedFile = async () => {\n      if (!filePath.value) return;\n\n      parsing.value = true;\n      try {\n        const request: CinParameterParseRequest = {\n          sourceType: \"file\",\n          filePath: filePath.value\n        };\n\n        const response = await appApi.cinParameter.parseFile(request);\n        const result = response.data;\n\n        variables.value = result.variables;\n        sourceFilePath.value = result.sourceFilePath;\n\n        // 初始化参数值\n        parameterValues.value = {};\n        result.variables.forEach(variable => {\n          parameterValues.value[variable.name] = variable.value || \"\";\n        });\n\n        modifiedParams.value.clear();\n        ElMessage.success(`成功解析 ${result.variables.length} 个参数`);\n      } catch (error) {\n        console.error('解析文件失败:', error);\n        ElMessage.error('解析文件失败');\n      } finally {\n        parsing.value = false;\n      }\n    };\n\n    const handleTemplateChange = (value: string[]) => {\n      if (value && value.length === 2) {\n        selectedCategory.value = value[0];\n        selectedTemplateId.value = value[1];\n        loadSelectedTemplate();\n      }\n    };\n\n    const getTemplatesByCategory = (category: string) => {\n      return templates.value.filter(t => t.category === category);\n    };\n\n    const loadSelectedTemplate = async () => {\n      if (!selectedTemplateId.value) return;\n\n      parsing.value = true;\n      try {\n        const request: CinParameterParseRequest = {\n          sourceType: \"template\",\n          templateId: selectedTemplateId.value,\n          filePath: \"\"\n        };\n\n        const response = await appApi.cinParameter.parseFile(request);\n        const result = response.data;\n\n        variables.value = result.variables;\n        sourceFilePath.value = result.sourceFilePath;\n\n        // 初始化参数值\n        parameterValues.value = {};\n        result.variables.forEach(variable => {\n          parameterValues.value[variable.name] = variable.value || \"\";\n        });\n\n        modifiedParams.value.clear();\n        ElMessage.success(`成功加载模板 ${result.variables.length} 个参数`);\n      } catch (error) {\n        console.error('加载模板失败:', error);\n        ElMessage.error('加载模板失败');\n      } finally {\n        parsing.value = false;\n      }\n    };\n\n    const onParameterChange = (paramName: string) => {\n      modifiedParams.value.add(paramName);\n    };\n\n    const exportCinFile = async () => {\n      processing.value = true;\n      try {\n        const request: CinParameterRequest = {\n          sourceType: sourceType.value,\n          templateId: sourceType.value === \"template\" ? selectedTemplateId.value : undefined,\n          filePath: sourceType.value === \"file\" ? filePath.value : \"\",\n          parameterValues: parameterValues.value\n        };\n\n        const response = await appApi.cinParameter.processFile(request);\n        const result = response.data;\n\n        ElMessage.success(`文件导出成功！`);\n        modifiedParams.value.clear();\n\n        await appApi.explorer.openExplorer(result.outputFilePath);\n\n      } catch (error) {\n        console.error('导出文件失败:', error);\n        ElMessage.error('导出文件失败');\n      } finally {\n        processing.value = false;\n      }\n    };\n\n    // 源文件管理相关方法\n    const handleParseResult = (file: SourceFile, params: ParsedParam[]) => {\n      currentParseFile.value = file;\n      currentParsedParams.value = params;\n      parseDialogVisible.value = true;\n    };\n\n    const applyParsedParams = (params: ParsedParam[]) => {\n      params.forEach(param => {\n        // 查找对应的变量（不区分大小写）\n        const variable = variables.value.find(v => v.name.toLowerCase() === param.name.toLowerCase());\n        if (variable) {\n          // 应用参数值\n          parameterValues.value[variable.name] = String(param.value);\n          // 记录参数来源\n          parameterSources.value[variable.name] = param.source;\n          // 标记为已修改\n          modifiedParams.value.add(variable.name);\n        }\n      });\n\n      // 注意：文件列表现在由DataFileManager组件管理\n\n      ElMessage.success(`成功应用 ${params.length} 个参数`);\n    };\n\n    const getParameterSource = (paramName: string) => {\n      return parameterSources.value[paramName];\n    };\n\n    // 判断参数是否为复杂类型\n    const isComplexParameter = (variable: CaplVariable) => {\n      const value = parameterValues.value[variable.name];\n      if (!value) return false;\n\n      // 检查是否是结构体类型（包含大括号）\n      const trimmedValue = value.trim();\n      if (trimmedValue.startsWith('{') && trimmedValue.includes(',')) {\n        return true;\n      }\n\n      // 检查是否是JSON格式\n      if (trimmedValue.startsWith('{') || trimmedValue.startsWith('[')) {\n        try {\n          JSON.parse(trimmedValue);\n          return true;\n        } catch {\n          // 不是有效的JSON，但可能是CIN格式的结构体\n          return trimmedValue.includes(',') || trimmedValue.includes('{');\n        }\n      }\n\n      // 检查变量类型是否为复杂类型\n      const type = variable.type.toLowerCase();\n      return type.includes('struct') || type.includes('array') || type.includes('[]');\n    };\n\n    // 编辑复杂参数\n    const editComplexParameter = (variable: CaplVariable) => {\n      currentComplexParamName.value = variable.name;\n      currentComplexParamValue.value = parameterValues.value[variable.name] || '';\n      complexParamDialogVisible.value = true;\n    };\n\n    // 处理复杂参数编辑确认\n    const handleComplexParamConfirm = (value: string) => {\n      if (currentComplexParamName.value) {\n        parameterValues.value[currentComplexParamName.value] = value;\n        onParameterChange(currentComplexParamName.value);\n      }\n    };\n\n    const getFileName = (path: string) => {\n      if (!path) return '';\n      return path.split(/[\\\\/]/).pop() || '';\n    };\n\n    const getFileTypeTagType = (fileType: SourceFileType) => {\n      switch (fileType) {\n        case SourceFileType.Arxml: return 'primary';\n        case SourceFileType.Sddb: return 'success';\n        case SourceFileType.Ldf: return 'warning';\n        default: return '';\n      }\n    };\n\n    const getAppliedParamsCount = (file: SourceFile) => {\n      // 计算该文件已应用的参数数量\n      let count = 0;\n      Object.keys(parameterSources.value).forEach(paramName => {\n        if (parameterSources.value[paramName] === file.fileName) {\n          count++;\n        }\n      });\n      return count;\n    };\n\n\n\n\n\n\n\n    onMounted(async () => {\n      await loadTemplates();\n    });\n\n    return {\n      templates,\n      sourceType,\n      selectedCategory,\n      selectedTemplateId,\n      selectedTemplatePath,\n      filePath,\n      variables,\n      parameterValues,\n      sourceFilePath,\n      searchKeyword,\n      parsing,\n      processing,\n      categories,\n      cascaderOptions,\n      filteredTemplates,\n      filteredVariables,\n      modifiedParamsCount,\n      hasUnsavedChanges,\n      onSourceTypeChange,\n      onCategoryChange,\n      selectLocalFile,\n      handleTemplateChange,\n      getTemplatesByCategory,\n      loadSelectedTemplate,\n      onParameterChange,\n      exportCinFile,\n      getFileName,\n      getFileTypeTagType,\n      getAppliedParamsCount,\n      // 源文件管理相关\n      parseDialogVisible,\n      currentParseFile,\n      currentParsedParams,\n      handleParseResult,\n      applyParsedParams,\n      getParameterSource,\n      // 复杂参数编辑相关\n      isComplexParameter,\n      editComplexParameter,\n      handleComplexParamConfirm,\n      complexParamDialogVisible,\n      currentComplexParamValue,\n      complexParamTitle,\n      // 图标和枚举\n      Document,\n      SourceFileStatus\n    };\n  },\n});\n</script>\n\n<style scoped>\n.parameter-tool {\n  height: 100vh;\n  display: flex;\n  flex-direction: column;\n  background-color: var(--el-bg-color-page);\n}\n\n.toolbar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 20px;\n  background: var(--el-bg-color);\n  border-bottom: 1px solid var(--el-border-color-base);\n  flex-shrink: 0;\n}\n\n.toolbar-left {\n  display: flex;\n  align-items: center;\n}\n\n.toolbar-right {\n  display: flex;\n  gap: 8px;\n}\n\n.current-file-info {\n  padding: 8px 20px;\n  background: var(--el-color-primary-light-9);\n  border-bottom: 1px solid var(--el-border-color-lighter);\n  flex-shrink: 0;\n}\n\n.file-info-content {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.file-icon {\n  color: var(--el-color-primary);\n}\n\n.file-name {\n  font-weight: 500;\n  color: var(--el-text-color-primary);\n}\n\n.main-content {\n  flex: 1;\n  overflow: hidden;\n  display: flex;\n  flex-direction: column;\n  padding: 20px;\n  min-height: 0;\n  /* 允许flex子元素收缩 */\n}\n\n.search-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.filter-info {\n  display: flex;\n  gap: 16px;\n  font-size: 14px;\n  color: var(--el-text-color-secondary);\n}\n\n.import-info {\n  color: var(--el-color-primary);\n}\n\n\n\n.current-value {\n  color: var(--el-text-color-regular);\n  font-style: italic;\n}\n\n.parameter-source {\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  font-size: 12px;\n  color: var(--el-color-primary);\n}\n\n.param-value-cell {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.param-value-input {\n  flex: 1;\n}\n\n.edit-button {\n  flex-shrink: 0;\n  padding: 0 4px;\n}\n\n.no-source {\n  color: var(--el-text-color-placeholder);\n  font-style: italic;\n}\n\n/* CIN 参数列表卡片样式 */\n.cin-parameters-section {\n  margin-bottom: 16px;\n  padding: 16px;\n  background: var(--el-bg-color);\n  border: 1px solid var(--el-border-color);\n  border-radius: 6px;\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  min-height: 0;\n  /* 允许flex子元素收缩 */\n}\n\n.cin-operations-content {\n  margin-top: 12px;\n  flex-shrink: 0;\n  /* 不允许收缩 */\n}\n\n.operation-row {\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n  gap: 8px;\n}\n\n.search-bar {\n  margin-bottom: 12px;\n  flex-shrink: 0;\n  /* 不允许收缩 */\n}\n\n.parameters-table {\n  flex: 1;\n  overflow: hidden;\n  min-height: 0;\n  /* 允许flex子元素收缩 */\n}\n\n\n\n.imported-files-section {\n  margin-top: 16px;\n  padding: 16px;\n  background: var(--el-bg-color);\n  border: 1px solid var(--el-border-color);\n  border-radius: 6px;\n}\n\n.imported-files-section h4 {\n  margin: 0 0 12px 0;\n  color: var(--el-text-color-primary);\n  font-size: 14px;\n  font-weight: 600;\n}\n\n.imported-files-list {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.imported-file-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 8px 12px;\n  background: var(--el-fill-color-light);\n  border-radius: 4px;\n}\n\n.imported-file-item .file-name {\n  font-weight: 500;\n}\n\n.param-count {\n  font-size: 12px;\n  color: var(--el-text-color-secondary);\n}\n\n.file-actions {\n  margin-left: auto;\n  display: flex;\n  gap: 8px;\n}\n\n.template-selection {\n  display: flex;\n}\n\n.category-list {\n  width: 200px;\n  border-right: 1px solid var(--el-border-color-lighter);\n  overflow-y: auto;\n}\n\n.category-item {\n  padding: 12px 16px;\n  cursor: pointer;\n  border-bottom: 1px solid var(--el-border-color-lighter);\n}\n\n.category-item:hover {\n  background: var(--el-fill-color-light);\n}\n\n.category-item.active {\n  background: var(--el-color-primary-light-9);\n  color: var(--el-color-primary);\n  font-weight: 500;\n}\n\n.template-list {\n  flex: 1;\n  overflow-y: auto;\n  padding: 8px;\n}\n\n.template-item {\n  padding: 12px;\n  margin-bottom: 8px;\n  border: 1px solid var(--el-border-color-lighter);\n  border-radius: 4px;\n  cursor: pointer;\n}\n\n.template-item:hover {\n  border-color: var(--el-color-primary);\n}\n\n.template-item.active {\n  border-color: var(--el-color-primary);\n  background: var(--el-color-primary-light-9);\n}\n\n.template-name {\n  font-weight: 500;\n  color: var(--el-text-color-primary);\n  margin-bottom: 4px;\n}\n\n.template-desc {\n  font-size: 12px;\n  color: var(--el-text-color-secondary);\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .toolbar {\n    flex-direction: column;\n    gap: 8px;\n    align-items: stretch;\n  }\n\n  .toolbar-left,\n  .toolbar-right {\n    justify-content: center;\n  }\n\n  .search-bar {\n    flex-direction: column;\n    gap: 8px;\n    align-items: stretch;\n  }\n\n  .template-selection {\n    flex-direction: column;\n    height: auto;\n  }\n\n  .category-list {\n    width: auto;\n    border-right: none;\n    border-bottom: 1px solid var(--el-border-color-lighter);\n    max-height: 150px;\n  }\n}\n</style>\n", "import { defineComponent as _defineComponent } from 'vue'\nimport { resolveComponent as _resolveComponent, createVNode as _createVNode, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, withCtx as _withCtx, createSlots as _createSlots, openBlock as _openBlock, createBlock as _createBlock } from \"vue\"\n\nconst _hoisted_1 = { class: \"param-value-viewer\" }\nconst _hoisted_2 = { class: \"dialog-footer\" }\n\nimport { ref, computed, watch } from 'vue';\n\n// Props\ninterface Props {\n  modelValue: boolean;\n  value: any;\n  readonly?: boolean;\n  title?: string;\n}\n\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'ParamValueViewer',\n  props: {\n    modelValue: { type: Boolean },\n    value: {},\n    readonly: { type: Boolean, default: false },\n    title: { default: '参数值' }\n  },\n  emits: [\"update:modelValue\", \"update:value\", \"confirm\"],\n  setup(__props: any, { emit: __emit }) {\n\nconst props = __props;\n\n// Emits\nconst emit = __emit;\n\n// 数据\nconst visible = ref(false);\nconst displayValue = ref('');\n\n// 计算属性\nconst formattedValue = computed(() => {\n  if (props.value === null || props.value === undefined) {\n    return '';\n  }\n  \n  if (typeof props.value === 'string') {\n    // 如果是字符串，尝试格式化JSON\n    try {\n      const parsed = JSON.parse(props.value);\n      return JSON.stringify(parsed, null, 2);\n    } catch {\n      // 如果不是有效的JSON，直接返回原值\n      return props.value;\n    }\n  } else {\n    // 如果是对象，序列化为JSON\n    try {\n      return JSON.stringify(props.value, null, 2);\n    } catch {\n      return String(props.value);\n    }\n  }\n});\n\n// 监听器\nwatch(() => props.modelValue, (newVal) => {\n  visible.value = newVal;\n  if (newVal) {\n    displayValue.value = formattedValue.value;\n  }\n});\n\nwatch(visible, (newVal) => {\n  emit('update:modelValue', newVal);\n});\n\nwatch(() => props.value, () => {\n  if (visible.value) {\n    displayValue.value = formattedValue.value;\n  }\n});\n\n// 方法\nconst handleClose = () => {\n  visible.value = false;\n};\n\nconst handleInput = (value: string) => {\n  if (!props.readonly) {\n    emit('update:value', value);\n  }\n};\n\nconst handleConfirm = () => {\n  if (!props.readonly) {\n    emit('confirm', displayValue.value);\n  }\n  handleClose();\n};\n\nreturn (_ctx: any,_cache: any) => {\n  const _component_el_input = _resolveComponent(\"el-input\")!\n  const _component_el_button = _resolveComponent(\"el-button\")!\n  const _component_el_dialog = _resolveComponent(\"el-dialog\")!\n\n  return (_openBlock(), _createBlock(_component_el_dialog, {\n    modelValue: visible.value,\n    \"onUpdate:modelValue\": _cache[1] || (_cache[1] = ($event: any) => ((visible).value = $event)),\n    title: _ctx.title,\n    width: \"60%\",\n    \"before-close\": handleClose,\n    \"destroy-on-close\": \"\"\n  }, _createSlots({\n    default: _withCtx(() => [\n      _createElementVNode(\"div\", _hoisted_1, [\n        _createVNode(_component_el_input, {\n          modelValue: displayValue.value,\n          \"onUpdate:modelValue\": _cache[0] || (_cache[0] = ($event: any) => ((displayValue).value = $event)),\n          type: \"textarea\",\n          rows: 15,\n          readonly: _ctx.readonly,\n          placeholder: _ctx.readonly ? '' : '请输入参数值...',\n          style: {\"font-family\":\"'Courier New', monospace\"},\n          onInput: handleInput\n        }, null, 8, [\"modelValue\", \"readonly\", \"placeholder\"])\n      ])\n    ]),\n    _: 2\n  }, [\n    (!_ctx.readonly)\n      ? {\n          name: \"footer\",\n          fn: _withCtx(() => [\n            _createElementVNode(\"div\", _hoisted_2, [\n              _createVNode(_component_el_button, { onClick: handleClose }, {\n                default: _withCtx(() => _cache[2] || (_cache[2] = [\n                  _createTextVNode(\"取消\")\n                ])),\n                _: 1,\n                __: [2]\n              }),\n              _createVNode(_component_el_button, {\n                type: \"primary\",\n                onClick: handleConfirm\n              }, {\n                default: _withCtx(() => _cache[3] || (_cache[3] = [\n                  _createTextVNode(\"确定\")\n                ])),\n                _: 1,\n                __: [3]\n              })\n            ])\n          ]),\n          key: \"0\"\n        }\n      : undefined\n  ]), 1032, [\"modelValue\", \"title\"]))\n}\n}\n\n})", "<template>\n  <el-dialog\n    v-model=\"visible\"\n    :title=\"title\"\n    width=\"60%\"\n    :before-close=\"handleClose\"\n    destroy-on-close\n  >\n    <div class=\"param-value-viewer\">\n      <el-input\n        v-model=\"displayValue\"\n        type=\"textarea\"\n        :rows=\"15\"\n        :readonly=\"readonly\"\n        :placeholder=\"readonly ? '' : '请输入参数值...'\"\n        style=\"font-family: 'Courier New', monospace;\"\n        @input=\"handleInput\"\n      />\n    </div>\n\n    <template #footer v-if=\"!readonly\">\n      <div class=\"dialog-footer\">\n        <el-button @click=\"handleClose\">取消</el-button>\n        <el-button type=\"primary\" @click=\"handleConfirm\">确定</el-button>\n      </div>\n    </template>\n  </el-dialog>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref, computed, watch } from 'vue';\n\n// Props\ninterface Props {\n  modelValue: boolean;\n  value: any;\n  readonly?: boolean;\n  title?: string;\n}\n\nconst props = withDefaults(defineProps<Props>(), {\n  readonly: false,\n  title: '参数值'\n});\n\n// Emits\nconst emit = defineEmits<{\n  'update:modelValue': [value: boolean];\n  'update:value': [value: string];\n  'confirm': [value: string];\n}>();\n\n// 数据\nconst visible = ref(false);\nconst displayValue = ref('');\n\n// 计算属性\nconst formattedValue = computed(() => {\n  if (props.value === null || props.value === undefined) {\n    return '';\n  }\n  \n  if (typeof props.value === 'string') {\n    // 如果是字符串，尝试格式化JSON\n    try {\n      const parsed = JSON.parse(props.value);\n      return JSON.stringify(parsed, null, 2);\n    } catch {\n      // 如果不是有效的JSON，直接返回原值\n      return props.value;\n    }\n  } else {\n    // 如果是对象，序列化为JSON\n    try {\n      return JSON.stringify(props.value, null, 2);\n    } catch {\n      return String(props.value);\n    }\n  }\n});\n\n// 监听器\nwatch(() => props.modelValue, (newVal) => {\n  visible.value = newVal;\n  if (newVal) {\n    displayValue.value = formattedValue.value;\n  }\n});\n\nwatch(visible, (newVal) => {\n  emit('update:modelValue', newVal);\n});\n\nwatch(() => props.value, () => {\n  if (visible.value) {\n    displayValue.value = formattedValue.value;\n  }\n});\n\n// 方法\nconst handleClose = () => {\n  visible.value = false;\n};\n\nconst handleInput = (value: string) => {\n  if (!props.readonly) {\n    emit('update:value', value);\n  }\n};\n\nconst handleConfirm = () => {\n  if (!props.readonly) {\n    emit('confirm', displayValue.value);\n  }\n  handleClose();\n};\n</script>\n\n<style scoped>\n.param-value-viewer {\n  min-height: 300px;\n}\n\n.dialog-footer {\n  display: flex;\n  justify-content: flex-end;\n  gap: 12px;\n}\n</style>\n", "import script from \"./ParamValueViewer.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./ParamValueViewer.vue?vue&type=script&setup=true&lang=ts\"\n\nimport \"./ParamValueViewer.vue?vue&type=style&index=0&id=089a4f20&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-089a4f20\"]])\n\nexport default __exports__", "import { defineComponent as _defineComponent } from 'vue'\nimport { renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, resolveComponent as _resolveComponent, createBlock as _createBlock, withCtx as _withCtx, createVNode as _createVNode, unref as _unref, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode } from \"vue\"\n\nconst _hoisted_1 = { class: \"dialog-content\" }\nconst _hoisted_2 = { class: \"filter-section\" }\nconst _hoisted_3 = { class: \"selection-info\" }\nconst _hoisted_4 = { class: \"param-list\" }\nconst _hoisted_5 = { class: \"param-value-cell\" }\nconst _hoisted_6 = [\"title\"]\nconst _hoisted_7 = { class: \"dialog-footer\" }\n\nimport { ref, computed, watch, nextTick } from 'vue';\nimport { ElMessage } from 'element-plus';\nimport { Search } from '@element-plus/icons-vue';\nimport { SourceFile, ParsedParam, SourceFileType, ParamType } from '@/api/appApi';\nimport ParamValueViewer from './ParamValueViewer.vue';\n\n// Props\ninterface Props {\n  modelValue: boolean;\n  sourceFile: SourceFile | null;\n  parsedParams: ParsedParam[];\n}\n\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'ParamParseDialog',\n  props: {\n    modelValue: { type: Boolean },\n    sourceFile: {},\n    parsedParams: {}\n  },\n  emits: [\"update:modelValue\", \"apply-params\"],\n  setup(__props: any, { emit: __emit }) {\n\nconst props = __props;\n\n// Emits\nconst emit = __emit;\n\n// 数据\nconst visible = ref(false);\nconst searchText = ref('');\nconst selectedEcu = ref('');\nconst selectedParams = ref<ParsedParam[]>([]);\nconst paramValueDialogVisible = ref(false);\nconst currentParamValue = ref<any>('');\nconst paramTable = ref();\n\n// 计算属性\nconst ecuList = computed(() => {\n  const ecus = new Set(props.parsedParams.map(p => p.ecuName));\n  return Array.from(ecus).sort();\n});\n\nconst dialogTitle = computed(() => {\n  if (!props.sourceFile) {\n    return '参数解析结果';\n  }\n\n  const fileName = props.sourceFile.fileName;\n  return `参数解析结果 - ${fileName}`;\n});\n\nconst filteredParams = computed(() => {\n  let params = props.parsedParams;\n\n  // 按ECU筛选\n  if (selectedEcu.value) {\n    params = params.filter(p => p.ecuName === selectedEcu.value);\n  }\n\n  // 按名称搜索\n  if (searchText.value) {\n    const search = searchText.value.toLowerCase();\n    params = params.filter(p =>\n      p.name.toLowerCase().includes(search) ||\n      p.description.toLowerCase().includes(search)\n    );\n  }\n\n  return params;\n});\n\n// 监听器\nwatch(() => props.modelValue, (newVal) => {\n  visible.value = newVal;\n  if (newVal) {\n    // 重置状态\n    searchText.value = '';\n    selectedParams.value = [];\n    // 默认选择第一个ECU\n    if (ecuList.value.length > 0) {\n      selectedEcu.value = ecuList.value[0];\n      // 默认全选当前ECU的参数\n      nextTick(() => {\n        selectAllCurrentEcuParams();\n      });\n    }\n  }\n});\n\nwatch(visible, (newVal) => {\n  emit('update:modelValue', newVal);\n});\n\nwatch(selectedEcu, () => {\n  // ECU切换时默认全选当前ECU的参数\n  nextTick(() => {\n    selectAllCurrentEcuParams();\n  });\n});\n\n// 方法\nconst handleEcuChange = () => {\n  // 切换ECU时默认全选\n  selectAllCurrentEcuParams();\n};\n\nconst selectAllCurrentEcuParams = () => {\n  // 全选当前ECU的参数\n  if (paramTable.value) {\n    const currentEcuParams = filteredParams.value;\n    currentEcuParams.forEach(row => {\n      paramTable.value.toggleRowSelection(row, true);\n    });\n  }\n};\n\nconst handleClose = () => {\n  visible.value = false;\n};\n\nconst handleSelectionChange = (selection: ParsedParam[]) => {\n  selectedParams.value = selection;\n};\n\nconst getFileTypeTagType = (fileType?: SourceFileType) => {\n  switch (fileType) {\n    case SourceFileType.Arxml: return 'primary';\n    case SourceFileType.Sddb: return 'success';\n    case SourceFileType.Ldf: return 'warning';\n    default: return '';\n  }\n};\n\nconst formatValue = (value: any) => {\n  if (value === null || value === undefined) {\n    return '';\n  }\n\n  return String(value);\n};\n\nconst showParamValue = (param: ParsedParam) => {\n  currentParamValue.value = param.value;\n  paramValueDialogVisible.value = true;\n};\n\nconst applySelectedParams = () => {\n  if (selectedParams.value.length === 0) {\n    ElMessage.warning('请先选择要应用的参数');\n    return;\n  }\n\n  emit('apply-params', selectedParams.value);\n  ElMessage.success(`成功应用 ${selectedParams.value.length} 个参数`);\n  handleClose();\n};\n\nreturn (_ctx: any,_cache: any) => {\n  const _component_el_option = _resolveComponent(\"el-option\")!\n  const _component_el_select = _resolveComponent(\"el-select\")!\n  const _component_el_icon = _resolveComponent(\"el-icon\")!\n  const _component_el_input = _resolveComponent(\"el-input\")!\n  const _component_el_table_column = _resolveComponent(\"el-table-column\")!\n  const _component_el_button = _resolveComponent(\"el-button\")!\n  const _component_el_table = _resolveComponent(\"el-table\")!\n  const _component_el_dialog = _resolveComponent(\"el-dialog\")!\n\n  return (_openBlock(), _createBlock(_component_el_dialog, {\n    modelValue: visible.value,\n    \"onUpdate:modelValue\": _cache[3] || (_cache[3] = ($event: any) => ((visible).value = $event)),\n    title: dialogTitle.value,\n    width: \"80%\",\n    \"before-close\": handleClose,\n    \"destroy-on-close\": \"\"\n  }, {\n    footer: _withCtx(() => [\n      _createElementVNode(\"div\", _hoisted_7, [\n        _createVNode(_component_el_button, { onClick: handleClose }, {\n          default: _withCtx(() => _cache[5] || (_cache[5] = [\n            _createTextVNode(\"取消\")\n          ])),\n          _: 1,\n          __: [5]\n        }),\n        _createVNode(_component_el_button, {\n          type: \"primary\",\n          onClick: applySelectedParams,\n          disabled: selectedParams.value.length === 0\n        }, {\n          default: _withCtx(() => [\n            _createTextVNode(\" 应用参数 (\" + _toDisplayString(selectedParams.value.length) + \") \", 1)\n          ]),\n          _: 1\n        }, 8, [\"disabled\"])\n      ])\n    ]),\n    default: _withCtx(() => [\n      _createElementVNode(\"div\", _hoisted_1, [\n        _createElementVNode(\"div\", _hoisted_2, [\n          _createVNode(_component_el_select, {\n            modelValue: selectedEcu.value,\n            \"onUpdate:modelValue\": _cache[0] || (_cache[0] = ($event: any) => ((selectedEcu).value = $event)),\n            size: \"small\",\n            placeholder: \"选择 ECU\",\n            style: {\"width\":\"100px\"},\n            onChange: handleEcuChange\n          }, {\n            default: _withCtx(() => [\n              (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(ecuList.value, (ecu) => {\n                return (_openBlock(), _createBlock(_component_el_option, {\n                  key: ecu,\n                  label: ecu,\n                  value: ecu\n                }, null, 8, [\"label\", \"value\"]))\n              }), 128))\n            ]),\n            _: 1\n          }, 8, [\"modelValue\"]),\n          _createVNode(_component_el_input, {\n            modelValue: searchText.value,\n            \"onUpdate:modelValue\": _cache[1] || (_cache[1] = ($event: any) => ((searchText).value = $event)),\n            placeholder: \"搜索参数名称...\",\n            clearable: \"\",\n            size: \"small\",\n            style: {\"width\":\"260px\"}\n          }, {\n            prefix: _withCtx(() => [\n              _createVNode(_component_el_icon, null, {\n                default: _withCtx(() => [\n                  _createVNode(_unref(Search))\n                ]),\n                _: 1\n              })\n            ]),\n            _: 1\n          }, 8, [\"modelValue\"]),\n          _createElementVNode(\"div\", _hoisted_3, \" 已选择 \" + _toDisplayString(selectedParams.value.length) + \"/\" + _toDisplayString(filteredParams.value.length) + \" 个参数 \", 1)\n        ]),\n        _createElementVNode(\"div\", _hoisted_4, [\n          _createVNode(_component_el_table, {\n            ref_key: \"paramTable\",\n            ref: paramTable,\n            data: filteredParams.value,\n            height: \"400\",\n            onSelectionChange: handleSelectionChange,\n            \"row-key\": \"name\",\n            size: \"small\"\n          }, {\n            default: _withCtx(() => [\n              _createVNode(_component_el_table_column, {\n                type: \"selection\",\n                width: \"55\"\n              }),\n              _createVNode(_component_el_table_column, {\n                prop: \"name\",\n                label: \"参数名\",\n                width: \"200\",\n                \"show-overflow-tooltip\": \"\"\n              }),\n              _createVNode(_component_el_table_column, {\n                prop: \"paramType\",\n                label: \"类型\",\n                width: \"200\",\n                \"show-overflow-tooltip\": \"\"\n              }),\n              _createVNode(_component_el_table_column, {\n                prop: \"value\",\n                label: \"参数值\",\n                \"min-width\": \"200\"\n              }, {\n                default: _withCtx(({ row }) => [\n                  _createElementVNode(\"div\", _hoisted_5, [\n                    _createVNode(_component_el_button, {\n                      type: \"text\",\n                      size: \"small\",\n                      onClick: ($event: any) => (showParamValue(row)),\n                      class: \"view-button\"\n                    }, {\n                      default: _withCtx(() => _cache[4] || (_cache[4] = [\n                        _createTextVNode(\" 查看 \")\n                      ])),\n                      _: 2,\n                      __: [4]\n                    }, 1032, [\"onClick\"]),\n                    _createElementVNode(\"span\", {\n                      class: \"param-value-text\",\n                      title: formatValue(row.value)\n                    }, _toDisplayString(formatValue(row.value)), 9, _hoisted_6)\n                  ])\n                ]),\n                _: 1\n              })\n            ]),\n            _: 1\n          }, 8, [\"data\"])\n        ])\n      ]),\n      _createVNode(ParamValueViewer, {\n        modelValue: paramValueDialogVisible.value,\n        \"onUpdate:modelValue\": _cache[2] || (_cache[2] = ($event: any) => ((paramValueDialogVisible).value = $event)),\n        value: currentParamValue.value,\n        readonly: true,\n        title: \"参数值\"\n      }, null, 8, [\"modelValue\", \"value\"])\n    ]),\n    _: 1\n  }, 8, [\"modelValue\", \"title\"]))\n}\n}\n\n})", "<template>\n  <el-dialog v-model=\"visible\" :title=\"dialogTitle\" width=\"80%\" :before-close=\"handleClose\" destroy-on-close>\n    <div class=\"dialog-content\">\n      <!-- ECU选择和搜索 -->\n      <div class=\"filter-section\">\n        <el-select v-model=\"selectedEcu\" size=\"small\" placeholder=\"选择 ECU\" style=\"width: 100px;\"\n          @change=\"handleEcuChange\">\n          <el-option v-for=\"ecu in ecuList\" :key=\"ecu\" :label=\"ecu\" :value=\"ecu\" />\n        </el-select>\n\n        <el-input v-model=\"searchText\" placeholder=\"搜索参数名称...\" clearable size=\"small\" style=\"width: 260px\">\n          <template #prefix>\n            <el-icon>\n              <Search />\n            </el-icon>\n          </template>\n        </el-input>\n\n        <div class=\"selection-info\">\n          已选择 {{ selectedParams.length }}/{{ filteredParams.length }} 个参数\n        </div>\n      </div>\n\n      <!-- 参数列表 -->\n      <div class=\"param-list\">\n        <el-table ref=\"paramTable\" :data=\"filteredParams\" height=\"400\" @selection-change=\"handleSelectionChange\"\n          row-key=\"name\" size=\"small\">\n          <el-table-column type=\"selection\" width=\"55\" />\n          <el-table-column prop=\"name\" label=\"参数名\" width=\"200\" show-overflow-tooltip />\n          <el-table-column prop=\"paramType\" label=\"类型\" width=\"200\" show-overflow-tooltip />\n          <el-table-column prop=\"value\" label=\"参数值\" min-width=\"200\">\n            <template #default=\"{ row }\">\n              <div class=\"param-value-cell\">\n                <el-button type=\"text\" size=\"small\" @click=\"showParamValue(row)\" class=\"view-button\">\n                  查看\n                </el-button>\n                <span class=\"param-value-text\" :title=\"formatValue(row.value)\">\n                  {{ formatValue(row.value) }}\n                </span>\n              </div>\n            </template>\n          </el-table-column>\n        </el-table>\n      </div>\n\n\n    </div>\n\n    <template #footer>\n      <div class=\"dialog-footer\">\n        <el-button @click=\"handleClose\">取消</el-button>\n        <el-button type=\"primary\" @click=\"applySelectedParams\" :disabled=\"selectedParams.length === 0\">\n          应用参数 ({{ selectedParams.length }})\n        </el-button>\n      </div>\n    </template>\n\n    <!-- 参数值查看弹窗 -->\n    <ParamValueViewer v-model=\"paramValueDialogVisible\" :value=\"currentParamValue\" :readonly=\"true\" title=\"参数值\" />\n  </el-dialog>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref, computed, watch, nextTick } from 'vue';\nimport { ElMessage } from 'element-plus';\nimport { Search } from '@element-plus/icons-vue';\nimport { SourceFile, ParsedParam, SourceFileType, ParamType } from '@/api/appApi';\nimport ParamValueViewer from './ParamValueViewer.vue';\n\n// Props\ninterface Props {\n  modelValue: boolean;\n  sourceFile: SourceFile | null;\n  parsedParams: ParsedParam[];\n}\n\nconst props = defineProps<Props>();\n\n// Emits\nconst emit = defineEmits<{\n  'update:modelValue': [value: boolean];\n  'apply-params': [params: ParsedParam[]];\n}>();\n\n// 数据\nconst visible = ref(false);\nconst searchText = ref('');\nconst selectedEcu = ref('');\nconst selectedParams = ref<ParsedParam[]>([]);\nconst paramValueDialogVisible = ref(false);\nconst currentParamValue = ref<any>('');\nconst paramTable = ref();\n\n// 计算属性\nconst ecuList = computed(() => {\n  const ecus = new Set(props.parsedParams.map(p => p.ecuName));\n  return Array.from(ecus).sort();\n});\n\nconst dialogTitle = computed(() => {\n  if (!props.sourceFile) {\n    return '参数解析结果';\n  }\n\n  const fileName = props.sourceFile.fileName;\n  return `参数解析结果 - ${fileName}`;\n});\n\nconst filteredParams = computed(() => {\n  let params = props.parsedParams;\n\n  // 按ECU筛选\n  if (selectedEcu.value) {\n    params = params.filter(p => p.ecuName === selectedEcu.value);\n  }\n\n  // 按名称搜索\n  if (searchText.value) {\n    const search = searchText.value.toLowerCase();\n    params = params.filter(p =>\n      p.name.toLowerCase().includes(search) ||\n      p.description.toLowerCase().includes(search)\n    );\n  }\n\n  return params;\n});\n\n// 监听器\nwatch(() => props.modelValue, (newVal) => {\n  visible.value = newVal;\n  if (newVal) {\n    // 重置状态\n    searchText.value = '';\n    selectedParams.value = [];\n    // 默认选择第一个ECU\n    if (ecuList.value.length > 0) {\n      selectedEcu.value = ecuList.value[0];\n      // 默认全选当前ECU的参数\n      nextTick(() => {\n        selectAllCurrentEcuParams();\n      });\n    }\n  }\n});\n\nwatch(visible, (newVal) => {\n  emit('update:modelValue', newVal);\n});\n\nwatch(selectedEcu, () => {\n  // ECU切换时默认全选当前ECU的参数\n  nextTick(() => {\n    selectAllCurrentEcuParams();\n  });\n});\n\n// 方法\nconst handleEcuChange = () => {\n  // 切换ECU时默认全选\n  selectAllCurrentEcuParams();\n};\n\nconst selectAllCurrentEcuParams = () => {\n  // 全选当前ECU的参数\n  if (paramTable.value) {\n    const currentEcuParams = filteredParams.value;\n    currentEcuParams.forEach(row => {\n      paramTable.value.toggleRowSelection(row, true);\n    });\n  }\n};\n\nconst handleClose = () => {\n  visible.value = false;\n};\n\nconst handleSelectionChange = (selection: ParsedParam[]) => {\n  selectedParams.value = selection;\n};\n\nconst getFileTypeTagType = (fileType?: SourceFileType) => {\n  switch (fileType) {\n    case SourceFileType.Arxml: return 'primary';\n    case SourceFileType.Sddb: return 'success';\n    case SourceFileType.Ldf: return 'warning';\n    default: return '';\n  }\n};\n\nconst formatValue = (value: any) => {\n  if (value === null || value === undefined) {\n    return '';\n  }\n\n  return String(value);\n};\n\nconst showParamValue = (param: ParsedParam) => {\n  currentParamValue.value = param.value;\n  paramValueDialogVisible.value = true;\n};\n\nconst applySelectedParams = () => {\n  if (selectedParams.value.length === 0) {\n    ElMessage.warning('请先选择要应用的参数');\n    return;\n  }\n\n  emit('apply-params', selectedParams.value);\n  ElMessage.success(`成功应用 ${selectedParams.value.length} 个参数`);\n  handleClose();\n};\n</script>\n\n<style scoped>\n.dialog-content {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n\n.filter-section {\n  display: flex;\n  gap: 16px;\n  align-items: center;\n}\n\n.param-list {\n  border: 1px solid var(--el-border-color-base);\n  border-radius: 6px;\n}\n\n.selection-info {\n  font-size: 12px;\n  color: var(--el-text-color-regular);\n  margin-left: auto;\n}\n\n.param-value-cell {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.param-value-text {\n  flex: 1;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.view-button {\n  flex-shrink: 0;\n  padding: 0 4px;\n}\n\n.dialog-footer {\n  display: flex;\n  justify-content: flex-end;\n  gap: 8px;\n}\n</style>\n", "import script from \"./ParamParseDialog.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./ParamParseDialog.vue?vue&type=script&setup=true&lang=ts\"\n\nimport \"./ParamParseDialog.vue?vue&type=style&index=0&id=80c01486&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-80c01486\"]])\n\nexport default __exports__", "import { createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, createElementBlock as _createElementBlock } from \"vue\"\n\nconst _hoisted_1 = { class: \"data-files-section\" }\nconst _hoisted_2 = { class: \"section-header\" }\nconst _hoisted_3 = { class: \"header-actions\" }\nconst _hoisted_4 = { class: \"data-files-table\" }\nconst _hoisted_5 = [\"title\"]\nconst _hoisted_6 = { class: \"name\" }\nconst _hoisted_7 = { class: \"action-buttons\" }\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  const _component_el_button = _resolveComponent(\"el-button\")!\n  const _component_el_tag = _resolveComponent(\"el-tag\")!\n  const _component_el_table_column = _resolveComponent(\"el-table-column\")!\n  const _component_el_table = _resolveComponent(\"el-table\")!\n\n  return (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _createElementVNode(\"div\", _hoisted_2, [\n      _cache[2] || (_cache[2] = _createElementVNode(\"h4\", null, \"数据文件 (ARXML/SDDB/LDF)\", -1)),\n      _createElementVNode(\"div\", _hoisted_3, [\n        _createVNode(_component_el_button, {\n          size: \"small\",\n          type: \"primary\",\n          onClick: _ctx.selectDataFiles\n        }, {\n          default: _withCtx(() => _cache[0] || (_cache[0] = [\n            _createTextVNode(\"添加文件\")\n          ])),\n          _: 1,\n          __: [0]\n        }, 8, [\"onClick\"]),\n        (_ctx.dataFiles.length > 0)\n          ? (_openBlock(), _createBlock(_component_el_button, {\n              key: 0,\n              size: \"small\",\n              type: \"danger\",\n              onClick: _ctx.clearAllDataFiles\n            }, {\n              default: _withCtx(() => _cache[1] || (_cache[1] = [\n                _createTextVNode(\"清空\")\n              ])),\n              _: 1,\n              __: [1]\n            }, 8, [\"onClick\"]))\n          : _createCommentVNode(\"\", true)\n      ])\n    ]),\n    _createElementVNode(\"div\", _hoisted_4, [\n      _createVNode(_component_el_table, {\n        data: _ctx.dataFiles,\n        border: \"\",\n        style: {\"width\":\"100%\"},\n        size: \"small\"\n      }, {\n        default: _withCtx(() => [\n          _createVNode(_component_el_table_column, {\n            label: \"类型\",\n            width: \"80\"\n          }, {\n            default: _withCtx((scope) => [\n              _createVNode(_component_el_tag, {\n                type: _ctx.getFileTypeTagType(scope.row.fileType),\n                size: \"small\"\n              }, {\n                default: _withCtx(() => [\n                  _createTextVNode(_toDisplayString(scope.row.fileType.toUpperCase()), 1)\n                ]),\n                _: 2\n              }, 1032, [\"type\"])\n            ]),\n            _: 1\n          }),\n          _createVNode(_component_el_table_column, {\n            label: \"文件名\",\n            \"min-width\": \"200\"\n          }, {\n            default: _withCtx((scope) => [\n              _createElementVNode(\"div\", {\n                class: \"file-name\",\n                title: scope.row.path\n              }, [\n                _createElementVNode(\"span\", _hoisted_6, _toDisplayString(scope.row.fileName), 1)\n              ], 8, _hoisted_5)\n            ]),\n            _: 1\n          }),\n          _createVNode(_component_el_table_column, {\n            label: \"状态\",\n            width: \"100\"\n          }, {\n            default: _withCtx((scope) => [\n              _createVNode(_component_el_tag, {\n                type: _ctx.getStatusTagType(scope.row.status),\n                size: \"small\"\n              }, {\n                default: _withCtx(() => [\n                  _createTextVNode(_toDisplayString(_ctx.getStatusText(scope.row.status)), 1)\n                ]),\n                _: 2\n              }, 1032, [\"type\"])\n            ]),\n            _: 1\n          }),\n          _createVNode(_component_el_table_column, {\n            label: \"操作\",\n            width: \"300\"\n          }, {\n            default: _withCtx((scope) => [\n              _createElementVNode(\"div\", _hoisted_7, [\n                (scope.row.status === _ctx.SourceFileStatus.Pending || scope.row.status === _ctx.SourceFileStatus.Error)\n                  ? (_openBlock(), _createBlock(_component_el_button, {\n                      key: 0,\n                      type: \"primary\",\n                      size: \"small\",\n                      onClick: ($event: any) => (_ctx.parseDataFile(scope.row.id))\n                    }, {\n                      default: _withCtx(() => _cache[3] || (_cache[3] = [\n                        _createTextVNode(\" 解析参数 \")\n                      ])),\n                      _: 2,\n                      __: [3]\n                    }, 1032, [\"onClick\"]))\n                  : _createCommentVNode(\"\", true),\n                (scope.row.status === _ctx.SourceFileStatus.Parsed)\n                  ? (_openBlock(), _createBlock(_component_el_button, {\n                      key: 1,\n                      type: \"success\",\n                      size: \"small\",\n                      onClick: ($event: any) => (_ctx.viewDataFileDetails(scope.row))\n                    }, {\n                      default: _withCtx(() => _cache[4] || (_cache[4] = [\n                        _createTextVNode(\" 查看结果 \")\n                      ])),\n                      _: 2,\n                      __: [4]\n                    }, 1032, [\"onClick\"]))\n                  : _createCommentVNode(\"\", true),\n                _createVNode(_component_el_button, {\n                  type: \"warning\",\n                  size: \"small\",\n                  onClick: ($event: any) => (_ctx.openDataFileFolder(scope.row.path))\n                }, {\n                  default: _withCtx(() => _cache[5] || (_cache[5] = [\n                    _createTextVNode(\" 打开文件夹 \")\n                  ])),\n                  _: 2,\n                  __: [5]\n                }, 1032, [\"onClick\"]),\n                _createVNode(_component_el_button, {\n                  type: \"danger\",\n                  size: \"small\",\n                  onClick: ($event: any) => (_ctx.removeDataFile(scope.row.id))\n                }, {\n                  default: _withCtx(() => _cache[6] || (_cache[6] = [\n                    _createTextVNode(\" 移除 \")\n                  ])),\n                  _: 2,\n                  __: [6]\n                }, 1032, [\"onClick\"])\n              ])\n            ]),\n            _: 1\n          })\n        ]),\n        _: 1\n      }, 8, [\"data\"])\n    ])\n  ]))\n}", "<template>\n  <div class=\"data-files-section\">\n    <div class=\"section-header\">\n      <h4>数据文件 (ARXML/SDDB/LDF)</h4>\n      <div class=\"header-actions\">\n        <el-button size=\"small\" type=\"primary\" @click=\"selectDataFiles\">添加文件</el-button>\n        <el-button size=\"small\" type=\"danger\" @click=\"clearAllDataFiles\" v-if=\"dataFiles.length > 0\">清空</el-button>\n      </div>\n    </div>\n    \n    <div class=\"data-files-table\">\n      <el-table :data=\"dataFiles\" border style=\"width: 100%\" size=\"small\">\n        <el-table-column label=\"类型\" width=\"80\">\n          <template #default=\"scope\">\n            <el-tag :type=\"getFileTypeTagType(scope.row.fileType)\" size=\"small\">{{ scope.row.fileType.toUpperCase() }}</el-tag>\n          </template>\n        </el-table-column>\n        \n        <el-table-column label=\"文件名\" min-width=\"200\">\n          <template #default=\"scope\">\n            <div class=\"file-name\" :title=\"scope.row.path\">\n              <span class=\"name\">{{ scope.row.fileName }}</span>\n            </div>\n          </template>\n        </el-table-column>\n        \n        <el-table-column label=\"状态\" width=\"100\">\n          <template #default=\"scope\">\n            <el-tag :type=\"getStatusTagType(scope.row.status)\" size=\"small\">{{ getStatusText(scope.row.status) }}</el-tag>\n          </template>\n        </el-table-column>\n        \n        <el-table-column label=\"操作\" width=\"300\">\n          <template #default=\"scope\">\n            <div class=\"action-buttons\">\n              <el-button \n                v-if=\"scope.row.status === SourceFileStatus.Pending || scope.row.status === SourceFileStatus.Error\"\n                type=\"primary\" \n                size=\"small\"\n                @click=\"parseDataFile(scope.row.id)\"\n              >\n                解析参数\n              </el-button>\n              \n              <el-button \n                v-if=\"scope.row.status === SourceFileStatus.Parsed\"\n                type=\"success\" \n                size=\"small\"\n                @click=\"viewDataFileDetails(scope.row)\"\n              >\n                查看结果\n              </el-button>\n              \n              <el-button\n                type=\"warning\"\n                size=\"small\"\n                @click=\"openDataFileFolder(scope.row.path)\"\n              >\n                打开文件夹\n              </el-button>\n              \n              <el-button \n                type=\"danger\" \n                size=\"small\"\n                @click=\"removeDataFile(scope.row.id)\"\n              >\n                移除\n              </el-button>\n            </div>\n          </template>\n        </el-table-column>\n      </el-table>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { defineComponent, ref, onMounted } from \"vue\";\nimport {\n  appApi,\n  SourceFile,\n  SourceFileType,\n  SourceFileStatus\n} from \"@/api/appApi\";\nimport { ElMessage, ElMessageBox } from \"element-plus\";\n\nexport default defineComponent({\n  name: \"DataFileManager\",\n  emits: ['parse-result'],\n  setup(props, { emit }) {\n    // 数据\n    const dataFiles = ref<SourceFile[]>([]);\n\n    // 方法\n    const loadDataFiles = async () => {\n      try {\n        const response = await appApi.cinParameter.getSourceFiles();\n        dataFiles.value = response.data;\n      } catch (error) {\n        console.error('加载数据文件失败:', error);\n        ElMessage.error('加载数据文件失败');\n      }\n    };\n\n    const selectDataFiles = async () => {\n      try {\n        const response = await appApi.cinParameter.selectSourceFiles();\n        const filePaths = response.data;\n        \n        if (filePaths && filePaths.length > 0) {\n          await addDataFiles(filePaths);\n          ElMessage.success(`成功添加 ${filePaths.length} 个参数数据文件`);\n        }\n      } catch (error) {\n        console.error('选择文件失败:', error);\n        ElMessage.error('选择文件失败');\n      }\n    };\n\n    const addDataFiles = async (filePaths: string[]) => {\n      try {\n        const response = await appApi.cinParameter.addSourceFiles({ filePaths });\n        const newFiles = response.data;\n        \n        // 更新导入文件列表\n        newFiles.forEach(file => {\n          const existingIndex = dataFiles.value.findIndex(f => f.id === file.id);\n          if (existingIndex >= 0) {\n            dataFiles.value[existingIndex] = file;\n          } else {\n            dataFiles.value.push(file);\n          }\n        });\n        \n        return newFiles;\n      } catch (error) {\n        console.error('添加文件失败:', error);\n        throw error;\n      }\n    };\n\n    const parseDataFile = async (fileId: string) => {\n      try {\n        // 先更新本地状态\n        const file = dataFiles.value.find(f => f.id === fileId);\n        if (file) {\n          file.status = SourceFileStatus.Parsing;\n        }\n\n        const response = await appApi.cinParameter.parseSourceFile({\n          fileId\n        });\n        \n        const updatedFile = response.data;\n        \n        // 更新文件列表中的对应项\n        const index = dataFiles.value.findIndex(f => f.id === fileId);\n        if (index >= 0) {\n          dataFiles.value[index] = updatedFile;\n        }\n        \n        ElMessage.success(`参数解析完成，共解析出 ${updatedFile.parsedParams?.length || 0} 个参数`);\n        \n        // 发送解析结果事件给父组件\n        if (updatedFile.parsedParams && updatedFile.parsedParams.length > 0) {\n          emit('parse-result', updatedFile, updatedFile.parsedParams);\n        }\n      } catch (error) {\n        console.error('解析文件失败:', error);\n        ElMessage.error('参数解析失败');\n        \n        // 恢复状态\n        const file = dataFiles.value.find(f => f.id === fileId);\n        if (file) {\n          file.status = SourceFileStatus.Error;\n        }\n      }\n    };\n\n    const viewDataFileDetails = (file: SourceFile) => {\n      if (file.parsedParams && file.parsedParams.length > 0) {\n        emit('parse-result', file, file.parsedParams);\n      } else {\n        ElMessage.warning('该文件暂无解析结果');\n      }\n    };\n\n    const openDataFileFolder = async (filePath: string) => {\n      try {\n        await appApi.explorer.openExplorer(filePath);\n      } catch (error) {\n        console.error('打开文件夹失败:', error);\n        ElMessage.error('打开文件夹失败');\n      }\n    };\n\n    const removeDataFile = async (fileId: string) => {\n      try {\n        // 调用服务端接口移除文件\n        await appApi.cinParameter.removeSourceFile({ fileId });\n        \n        // 从本地列表中移除文件\n        const fileIndex = dataFiles.value.findIndex(f => f.id === fileId);\n        if (fileIndex >= 0) {\n          dataFiles.value.splice(fileIndex, 1);\n          ElMessage.success('已移除数据文件');\n        }\n      } catch (error) {\n        console.error('移除文件失败:', error);\n        ElMessage.error('移除文件失败');\n      }\n    };\n\n    const clearAllDataFiles = async () => {\n      try {\n        await ElMessageBox.confirm('确定要清空所有参数数据文件吗？', '确认', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        });\n\n        // 调用服务端接口清空所有文件\n        await appApi.cinParameter.clearSourceFiles();\n        \n        // 清空本地文件列表\n        dataFiles.value = [];\n        ElMessage.success('已清空所有参数数据文件');\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('清空文件失败:', error);\n          ElMessage.error('清空文件失败');\n        }\n      }\n    };\n\n    // 辅助方法\n    const getFileTypeTagType = (fileType: SourceFileType) => {\n      switch (fileType) {\n        case SourceFileType.Arxml: return 'primary';\n        case SourceFileType.Sddb: return 'success';\n        case SourceFileType.Ldf: return 'warning';\n        default: return '';\n      }\n    };\n\n    const getStatusTagType = (status: SourceFileStatus) => {\n      switch (status) {\n        case SourceFileStatus.Pending: return '';\n        case SourceFileStatus.Parsing: return 'warning';\n        case SourceFileStatus.Parsed: return 'success';\n        case SourceFileStatus.Error: return 'danger';\n        default: return '';\n      }\n    };\n\n    const getStatusText = (status: SourceFileStatus) => {\n      switch (status) {\n        case SourceFileStatus.Pending: return '待解析';\n        case SourceFileStatus.Parsing: return '解析中';\n        case SourceFileStatus.Parsed: return '已解析';\n        case SourceFileStatus.Error: return '解析失败';\n        default: return status;\n      }\n    };\n\n    // 暴露给父组件的方法\n    const refreshDataFiles = () => {\n      loadDataFiles();\n    };\n\n    // 生命周期\n    onMounted(() => {\n      loadDataFiles();\n    });\n\n    return {\n      dataFiles,\n      selectDataFiles,\n      parseDataFile,\n      viewDataFileDetails,\n      openDataFileFolder,\n      removeDataFile,\n      clearAllDataFiles,\n      getFileTypeTagType,\n      getStatusTagType,\n      getStatusText,\n      refreshDataFiles,\n      // 枚举\n      SourceFileStatus\n    };\n  },\n});\n</script>\n\n<style scoped>\n/* 参数数据文件样式 */\n.data-files-section {\n  padding: 16px;\n  background: var(--el-bg-color);\n  border: 1px solid var(--el-border-color);\n  border-radius: 6px;\n  flex-shrink: 0;\n  max-height: 300px;\n  overflow-y: auto;\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n}\n\n.section-header h4 {\n  margin: 0;\n  color: var(--el-text-color-primary);\n  font-size: 14px;\n  font-weight: 600;\n}\n\n.header-actions {\n  display: flex;\n  gap: 8px;\n}\n\n.data-files-table {\n  margin-top: 12px;\n}\n\n.data-files-table .file-name .name {\n  font-weight: 500;\n}\n\n.action-buttons {\n  display: flex;\n  gap: 4px;\n  flex-wrap: wrap;\n}\n</style>\n", "import { render } from \"./DataFileManager.vue?vue&type=template&id=3b908206&scoped=true&ts=true\"\nimport script from \"./DataFileManager.vue?vue&type=script&lang=ts\"\nexport * from \"./DataFileManager.vue?vue&type=script&lang=ts\"\n\nimport \"./DataFileManager.vue?vue&type=style&index=0&id=3b908206&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-3b908206\"]])\n\nexport default __exports__", "import { render } from \"./ParameterToolView.vue?vue&type=template&id=c4417e6e&scoped=true&ts=true\"\nimport script from \"./ParameterToolView.vue?vue&type=script&lang=ts\"\nexport * from \"./ParameterToolView.vue?vue&type=script&lang=ts\"\n\nimport \"./ParameterToolView.vue?vue&type=style&index=0&id=c4417e6e&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-c4417e6e\"]])\n\nexport default __exports__"], "names": ["_hoisted_1", "class", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_hoisted_6", "key", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_hoisted_10", "_hoisted_11", "render", "_ctx", "_cache", "$props", "$setup", "$data", "$options", "_component_el_cascader", "_resolveComponent", "_component_el_button", "_component_el_divider", "_component_el_input", "_component_el_table_column", "_component_document", "_component_el_icon", "_component_el_table", "_component_DataFileManager", "_component_ParamParseDialog", "_component_ParamValueViewer", "_openBlock", "_createElementBlock", "_createElementVNode", "categories", "length", "_createBlock", "modelValue", "selectedTemplatePath", "$event", "size", "options", "cascaderOptions", "placeholder", "style", "onChange", "handleTemplateChange", "clearable", "_createCommentVNode", "_createVNode", "type", "onClick", "selectLocalFile", "default", "_withCtx", "_createTextVNode", "_", "__", "exportCinFile", "loading", "processing", "variables", "searchKeyword", "_toDisplayString", "filteredVariables", "data", "border", "prop", "label", "width", "scope", "isComplexParameter", "row", "editComplexParameter", "parameterValues", "name", "onParameterChange", "getParameterSource", "onParseResult", "handleParseResult", "parseDialogVisible", "currentParseFile", "currentParsedParams", "onApplyParams", "applyParsedParams", "complexParamDialogVisible", "value", "currentComplexParamValue", "readonly", "title", "complexParamTitle", "onConfirm", "handleComplexParamConfirm", "_defineComponent", "__name", "props", "Boolean", "emits", "setup", "__props", "emit", "__emit", "visible", "ref", "displayValue", "formattedValue", "computed", "undefined", "parsed", "JSON", "parse", "stringify", "String", "watch", "newVal", "handleClose", "handleInput", "handleConfirm", "_component_el_dialog", "_createSlots", "rows", "onInput", "fn", "__exports__", "sourceFile", "parsedParams", "searchText", "<PERSON><PERSON><PERSON>", "selectedPara<PERSON>", "paramValueDialogVisible", "currentParamValue", "paramTable", "ecuList", "ecus", "Set", "map", "p", "ecuName", "Array", "from", "sort", "dialogTitle", "fileName", "filteredParams", "params", "filter", "search", "toLowerCase", "includes", "description", "nextTick", "selectAllCurrentEcuParams", "handleEcuChange", "currentEcuParams", "for<PERSON>ach", "toggleRowSelection", "handleSelectionChange", "selection", "formatValue", "showParamValue", "param", "applySelectedParams", "ElMessage", "success", "warning", "_component_el_option", "_component_el_select", "footer", "disabled", "_Fragment", "_renderList", "ecu", "prefix", "_unref", "Search", "ref_key", "height", "onSelectionChange", "ParamValueViewer", "_component_el_tag", "selectDataFiles", "dataFiles", "clearAllDataFiles", "getFileTypeTagType", "fileType", "toUpperCase", "path", "getStatusTagType", "status", "getStatusText", "SourceFileStatus", "Pending", "Error", "parseDataFile", "id", "Parsed", "viewDataFileDetails", "openDataFileFolder", "removeDataFile", "defineComponent", "loadDataFiles", "async", "response", "appApi", "cinParameter", "getSourceFiles", "error", "console", "selectSourceFiles", "filePaths", "addDataFiles", "addSourceFiles", "newFiles", "file", "existingIndex", "findIndex", "f", "push", "find", "fileId", "Parsing", "parseSourceFile", "updatedFile", "index", "explorer", "openExplorer", "filePath", "removeSourceFile", "fileIndex", "splice", "ElMessageBox", "confirm", "confirmButtonText", "cancelButtonText", "clearSourceFiles", "SourceFileType", "Arxml", "Sddb", "Ldf", "refreshDataFiles", "onMounted", "components", "ParamParseDialog", "DataFileManager", "Document", "templates", "sourceType", "selectedCate<PERSON><PERSON>", "selectedTemplateId", "sourceFilePath", "parsing", "parameterSources", "modifiedParams", "currentComplexParamName", "categorySet", "t", "category", "c", "children", "template", "filteredTemplates", "keyword", "v", "modifiedParamsCount", "hasUnsavedChanges", "loadTemplates", "getTemplates", "onCategoryChange", "onSourceTypeChange", "clear", "selectFile", "parseSelectedFile", "request", "parseFile", "result", "variable", "loadSelectedTemplate", "getTemplatesByCategory", "templateId", "paramName", "add", "processFile", "outputFilePath", "source", "trimmedValue", "trim", "startsWith", "getFileName", "split", "pop", "getAppliedParamsCount", "count", "Object", "keys"], "sourceRoot": ""}