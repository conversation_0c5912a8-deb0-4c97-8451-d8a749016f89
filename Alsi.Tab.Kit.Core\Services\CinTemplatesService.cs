using Alsi.Common.Utils;
using Alsi.Tab.Kit.Core.Models;
using System;
using System.IO;
using System.Linq;

namespace Alsi.Tab.Kit.Core.Services
{
    public class CinTemplatesService
    {
        private const string CIN_TEMPLATES_FOLDER = "cin_templates";
        private const string INDEX_FILE_NAME = "cin_templates.json";

        private static CinTemplate[] cache = null;
        private static object cacheLock = new object();
        private static DateTime? cacheTime = null;

        public CinTemplate[] GetCinTemplates()
        {
            lock (cacheLock)
            {
                var modificationTime = GetModificationTime();
                if (cache == null || cacheTime == null || cacheTime < modificationTime)
                {
                    cache = LoadCinTemplates();
                    cacheTime = modificationTime;
                }

                return cache;
            }
        }

        public CinTemplate GetCinTemplateById(Guid templateId)
        {
            var templates = GetCinTemplates();
            return templates.FirstOrDefault(x => x.Id == templateId);
        }

        private DateTime? GetModificationTime()
        {
            var indexFilePath = GetIndexFilePath();
            if (!File.Exists(indexFilePath))
            {
                return null;
            }
            return new FileInfo(indexFilePath).LastWriteTime;
        }

        private CinTemplate[] LoadCinTemplates()
        {
            var indexFilePath = GetIndexFilePath();
            if (!File.Exists(indexFilePath))
            {
                return Array.Empty<CinTemplate>();
            }

            var jsonContent = File.ReadAllText(indexFilePath);
            var templatesIndex = JsonUtils.Deserialize<CinTemplatesIndex>(jsonContent);

            if (templatesIndex?.Templates == null)
            {
                return Array.Empty<CinTemplate>();
            }

            var cinTemplatesFolder = GetCinTemplatesFolder();

            foreach (var template in templatesIndex.Templates)
            {
                template.Id = Guid.NewGuid();
                ProcessTemplatePaths(template, cinTemplatesFolder);
            }

            return templatesIndex.Templates;
        }

        private void ProcessTemplatePaths(CinTemplate template, string cinTemplatesFolder)
        {
            if (!string.IsNullOrWhiteSpace(template.Path))
            {
                template.FullPath = Path.Combine(cinTemplatesFolder, template.Path);
                template.FileExists = File.Exists(template.FullPath);
            }
            else
            {
                template.FileExists = false;
            }

            if (string.IsNullOrWhiteSpace(template.Name) && !string.IsNullOrWhiteSpace(template.Path))
            {
                template.Name = Path.GetFileNameWithoutExtension(template.Path);
            }
        }

        private string GetCinTemplatesFolder()
        {
            return Path.Combine(Directory.GetParent(GetType().Assembly.Location).FullName, CIN_TEMPLATES_FOLDER);
        }

        private string GetIndexFilePath()
        {
            return Path.Combine(GetCinTemplatesFolder(), INDEX_FILE_NAME);
        }
    }
}
