﻿using Alsi.Common.Parsers.Sddb;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using Alsi.Common.Utils;

namespace Alsi.Tab.Kit.Core.Services.Capl.Params
{
    internal class SwsLoader
    {
        public void Load(string ecuNode, string source, SW[] sws, IParamCollection collection, int order)
        {
            var list = new List<Sw>();
            foreach (var sw in sws)
            {
                if (!Enum.TryParse<SwType>(sw.Type, true, out var swType))
                {
                    continue;
                }

                var swServices = new List<SwService>();
                foreach (var service in sw.Services.Service)
                {
                    if (!byte.TryParse(service.ID, NumberStyles.HexNumber, null, out var serviceId))
                    {
                        continue;
                    }

                    var swSessions = ConvertSwSessions(service.Sessions?.Session);

                    var swSubfunctions = service.Subfunctions.Subfunction
                        .Where(x => byte.TryParse(x.ID, NumberStyles.HexNumber, null, out _))
                        .Select(x => new SwSubfunction(
                            byte.Parse(x.ID, NumberStyles.HexNumber, null),
                            x.Name,
                            ConvertSwSessions(x.Sessions.Session),
                            ConvertSwRoutineIdentifiers(x)))
                        .OrderBy(x => x.Id)
                        .ToArray();

                    var swService = new SwService(serviceId, service.Name, swSessions, swSubfunctions);
                    swServices.Add(swService);
                }

                list.Add(new Sw(sw.Name, swType, swServices.OrderBy(x => x.Id).ToArray()));
            }

            var key = CaplParamConsts.Sws;
            var value = JsonUtils.Serialize(list.ToArray());
            collection.AddValue(ecuNode, key, value, source, order);
        }

        private static SwRoutineIdentifier[] ConvertSwRoutineIdentifiers(Subfunction subfunction)
        {
            var routineIdentifiers = subfunction.RoutineIdentifiers?.RoutineIdentifier;
            if (routineIdentifiers == null)
            {
                return Array.Empty<SwRoutineIdentifier>();
            }

            return routineIdentifiers
                .Where(x =>
                    int.TryParse(x.ID, NumberStyles.HexNumber, null, out _)
                    && int.TryParse(x.RoutineType, out _)
                    && int.TryParse(x.RequestLength, out _)
                    && int.TryParse(x.ResponseLength, out _))
                .Select(x => new SwRoutineIdentifier(
                    int.Parse(x.ID, NumberStyles.HexNumber, null),
                    x.Name,
                    int.Parse(x.RoutineType),
                    int.Parse(x.RequestLength),
                    int.Parse(x.ResponseLength),
                    ConvertSwSessions(x.Sessions.Session),
                    ConvertSecurityLevels(x?.SecurityAccessRefs)))
                .ToArray();
        }

        public static byte[] ConvertSecurityLevels(string securityAccessRefs)
        {
            if (string.IsNullOrWhiteSpace(securityAccessRefs))
            {
                return Array.Empty<byte>();
            }

            return securityAccessRefs.Split('/')
                .Where(x => byte.TryParse(x, NumberStyles.HexNumber, null, out _))
                .Select(x => byte.Parse(x, NumberStyles.HexNumber, null))
                .ToArray();
        }

        private static SwSession[] ConvertSwSessions(Session[] sessions)
        {
            if (sessions == null)
            {
                return Array.Empty<SwSession>();
            }

            return sessions
                .Where(x => byte.TryParse(x.ID, out _))
                .Select(x => new SwSession(byte.Parse(x.ID), x.Name))
                .ToArray();
        }
    }
}
