using Alsi.App.Utils;
using Alsi.Tab.Kit.Core.Models;
using Alsi.Tab.Kit.Core.Services;
using Alsi.Tab.Kit.Web.Dto;
using System;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Web.Http;

namespace Alsi.Tab.Kit.Web.Controllers
{
    public class ExternalAppsController : WebControllerBase
    {
        private readonly ExternalAppsService _externalAppsService;

        public ExternalAppsController()
        {
            _externalAppsService = new ExternalAppsService();
        }

        [HttpGet]
        [ActionName("list")]
        public IHttpActionResult GetExternalAppsList()
        {
            var apps = _externalAppsService.GetExternalApps();
            var appDtos = apps.Select(app => _mapper.Map<ExternalApp, ExternalAppDto>(app)).ToList();
            return Ok(appDtos);
        }

        [HttpPost]
        [ActionName("launch")]
        public IHttpActionResult LaunchExternalApp([FromBody] LaunchExternalAppRequest request)
        {
            _externalAppsService.LaunchExternalApp(request.AppId);
            return Ok(new { Success = true, Message = "应用程序启动成功" });
        }

        [HttpGet]
        [ActionName("icon")]
        public HttpResponseMessage GetAppIcon(Guid appId)
        {
            try
            {
                var apps = _externalAppsService.GetExternalApps();
                var app = apps.FirstOrDefault(x => x.Id == appId);

                if (app == null)
                {
                    return new HttpResponseMessage(HttpStatusCode.NotFound);
                }

                // 如果应用有图标且文件存在，返回图标文件
                if (app.IconExists && !string.IsNullOrEmpty(app.FullIconPath) && File.Exists(app.FullIconPath))
                {
                    var iconBytes = File.ReadAllBytes(app.FullIconPath);
                    var response = new HttpResponseMessage(HttpStatusCode.OK)
                    {
                        Content = new ByteArrayContent(iconBytes)
                    };

                    response.Content.Headers.ContentType = new MediaTypeHeaderValue(ContentTypeUtils.GetContentType(app.FullIconPath));
                    return response;
                }

                return new HttpResponseMessage(HttpStatusCode.NotFound);
            }
            catch
            {
                return new HttpResponseMessage(HttpStatusCode.InternalServerError);
            }
        }
    }
}
