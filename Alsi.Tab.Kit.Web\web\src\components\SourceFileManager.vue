<template>
  <div class="source-file-manager">
    <div class="manager-header">
      <h4>参数来源管理</h4>
      <div class="header-actions">
        <el-button type="primary" size="small" @click="selectFiles" :loading="selecting">
          添加文件
        </el-button>
        <el-button type="danger" size="small" @click="clearAllFiles" v-if="sourceFiles.length > 0">
          清空
        </el-button>
      </div>
    </div>

    <div class="file-list" v-if="sourceFiles.length > 0">
      <div class="parameters-table">
        <el-table :data="sourceFiles" border style="width: 100%">
          <el-table-column label="文件名" min-width="200">
            <template #default="scope">
              <div class="file-name" :title="scope.row.path">
                <el-icon class="file-icon">
                  <Document />
                </el-icon>
                <span class="name">{{ scope.row.fileName }}</span>
                <el-tag :type="getFileTypeTagType(scope.row.fileType)" size="small">{{ scope.row.fileType }}</el-tag>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column label="状态" width="100">
            <template #default="scope">
              <el-tag :type="getStatusTagType(scope.row.status)" size="small">{{ getStatusText(scope.row.status) }}</el-tag>
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="350">
            <template #default="scope">
              <!-- 解析中显示进度条 -->
              <div v-if="scope.row.status === SourceFileStatus.Parsing" class="parsing-progress">
                <el-progress 
                  :percentage="100"
                  :indeterminate="true"
                  :duration="3"
                  status="success"
                  :stroke-width="4"
                />
                <span class="progress-text">解析中...</span>
              </div>

              <!-- 其他状态显示操作按钮 -->
              <div v-else class="action-buttons">
                <el-button 
                  v-if="scope.row.status === SourceFileStatus.Pending || scope.row.status === SourceFileStatus.Error"
                  type="primary" 
                  size="small"
                  @click="parseFile(scope.row.id)"
                  :loading="false"
                >
                  解析参数
                </el-button>
                
                <el-button 
                  v-if="scope.row.status === SourceFileStatus.Parsed"
                  type="success" 
                  size="small"
                  @click="showParseResult(scope.row)"
                >
                  查看结果 ({{ scope.row.parsedParams?.length || 0 }})
                </el-button>
                
                <el-button 
                  type="info" 
                  size="small"
                  @click="openFileFolder(scope.row.path)"
                >
                  打开文件夹
                </el-button>
                
                <el-button 
                  type="danger" 
                  size="small"
                  @click="removeFile(scope.row.id)"
                >
                  移除
                </el-button>
              </div>

              <!-- 错误信息 -->
              <div v-if="scope.row.status === SourceFileStatus.Error && scope.row.errorMessage" class="error-message">
                <el-alert 
                  :title="scope.row.errorMessage" 
                  type="error" 
                  :closable="false"
                  :show-icon="false"
                  size="small"
                />
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <div v-else class="empty-state">
      <el-empty description="暂无源文件">
        <el-button type="primary" @click="selectFiles">添加源文件</el-button>
      </el-empty>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, Clock, Delete, Document } from '@element-plus/icons-vue';
import appApi, { SourceFile, SourceFileStatus, SourceFileType, ParsedParam } from '@/api/appApi';

// Props
// 删除了 ecuName prop，因为现在解析不再需要预先指定 ECU 名称

// Emits
const emit = defineEmits<{
  'parse-result': [file: SourceFile, params: ParsedParam[]];
}>();

// 数据
const sourceFiles = ref<SourceFile[]>([]);
const selecting = ref(false);

// 方法
const loadSourceFiles = async () => {
  try {
    const response = await appApi.cinParameter.getSourceFiles();
    sourceFiles.value = response.data;
  } catch (error) {
    console.error('加载源文件失败:', error);
    ElMessage.error('加载源文件失败');
  }
};

const selectFiles = async () => {
  selecting.value = true;
  try {
    const response = await appApi.cinParameter.selectSourceFiles();
    const filePaths = response.data;
    
    if (filePaths && filePaths.length > 0) {
      await addFiles(filePaths);
      ElMessage.success(`成功添加 ${filePaths.length} 个文件`);
    }
  } catch (error) {
    console.error('选择文件失败:', error);
    ElMessage.error('选择文件失败');
  } finally {
    selecting.value = false;
  }
};

const addFiles = async (filePaths: string[]) => {
  try {
    const response = await appApi.cinParameter.addSourceFiles({ filePaths });
    const newFiles = response.data;
    
    // 更新文件列表
    await loadSourceFiles();
    
    return newFiles;
  } catch (error) {
    console.error('添加文件失败:', error);
    throw error;
  }
};

const parseFile = async (fileId: string) => {
  try {
    // 先更新本地状态
    const file = sourceFiles.value.find(f => f.id === fileId);
    if (file) {
      file.status = SourceFileStatus.Parsing;
    }

    const response = await appApi.cinParameter.parseSourceFile({
      fileId
    });
    
    const updatedFile = response.data;
    
    // 更新文件列表中的对应项
    const index = sourceFiles.value.findIndex(f => f.id === fileId);
    if (index >= 0) {
      sourceFiles.value[index] = updatedFile;
    }
    
    ElMessage.success(`参数解析完成，共解析出 ${updatedFile.parsedParams?.length || 0} 个参数`);
  } catch (error) {
    console.error('解析文件失败:', error);
    ElMessage.error('参数解析失败');
    
    // 恢复状态
    const file = sourceFiles.value.find(f => f.id === fileId);
    if (file) {
      file.status = SourceFileStatus.Error;
    }
  }
};

const showParseResult = (file: SourceFile) => {
  if (file.parsedParams && file.parsedParams.length > 0) {
    emit('parse-result', file, file.parsedParams);
  } else {
    ElMessage.warning('该文件暂无解析结果');
  }
};

const removeFile = async (fileId: string) => {
  try {
    await appApi.cinParameter.removeSourceFile({ fileId });
    await loadSourceFiles();
    ElMessage.success('文件已移除');
  } catch (error) {
    console.error('移除文件失败:', error);
    ElMessage.error('移除文件失败');
  }
};

const openFileFolder = async (filePath: string) => {
  try {
    await appApi.explorer.openExplorer(filePath);
  } catch (error) {
    console.error('打开文件夹失败:', error);
    ElMessage.error('打开文件夹失败');
  }
};

const clearAllFiles = async () => {
  try {
    await ElMessageBox.confirm('确定要清空所有源文件吗？', '确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });

    await appApi.cinParameter.clearSourceFiles();
    sourceFiles.value = [];
    ElMessage.success('已清空所有源文件');
  } catch (error) {
    if (error !== 'cancel') {
      console.error('清空文件失败:', error);
      ElMessage.error('清空文件失败');
    }
  }
};

// 辅助方法
const getFileTypeTagType = (fileType: SourceFileType) => {
  switch (fileType) {
    case SourceFileType.Arxml: return 'primary';
    case SourceFileType.Sddb: return 'success';
    case SourceFileType.Ldf: return 'warning';
    default: return '';
  }
};

const getStatusTagType = (status: SourceFileStatus) => {
  switch (status) {
    case SourceFileStatus.Pending: return '';
    case SourceFileStatus.Parsing: return 'warning';
    case SourceFileStatus.Parsed: return 'success';
    case SourceFileStatus.Error: return 'danger';
    default: return '';
  }
};

const getStatusText = (status: SourceFileStatus) => {
  switch (status) {
    case SourceFileStatus.Pending: return '待解析';
    case SourceFileStatus.Parsing: return '解析中';
    case SourceFileStatus.Parsed: return '已解析';
    case SourceFileStatus.Error: return '解析失败';
    default: return status;
  }
};

const formatTime = (time: Date) => {
  const date = new Date(time);
  return date.toLocaleString('zh-CN');
};

// 生命周期
onMounted(() => {
  loadSourceFiles();
});

// 暴露方法给父组件
defineExpose({
  loadSourceFiles,
  addFiles
});
</script>

<style scoped>
.source-file-manager {
  border: 1px solid var(--el-border-color-base);
  border-radius: 6px;
  background: var(--el-bg-color);
}

.manager-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid var(--el-border-color-lighter);
  background: var(--el-bg-color-page);
}

.manager-header h4 {
  margin: 0;
  color: var(--el-text-color-primary);
}

.header-actions {
  display: flex;
  gap: 8px;
}

.file-list {
  margin-top: 15px;
}

.parameters-table {
  margin-top: 15px;
}

.file-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-icon {
  color: var(--el-color-primary);
}

.name {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.add-time {
  font-size: 12px;
  color: var(--el-text-color-placeholder);
}

.parsing-progress {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  min-width: 120px;
}

.progress-text {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.error-message {
  max-width: 200px;
  margin-top: 8px;
}

.empty-state {
  padding: 40px;
  text-align: center;
}
</style>
