{"RootPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Core", "ProjectFileName": "Alsi.Tab.Kit.Core.csproj", "Configuration": "Debug|x64", "FrameworkPath": "", "Sources": [{"SourceFile": "Models\\CinParameter.cs"}, {"SourceFile": "Models\\CinTemplate.cs"}, {"SourceFile": "Models\\DataLogModels.cs"}, {"SourceFile": "Models\\ExternalApp.cs"}, {"SourceFile": "Properties\\AssemblyInfo.cs"}, {"SourceFile": "Services\\Capl\\CaplParamConsts.cs"}, {"SourceFile": "Services\\Capl\\CaplParamLoader.cs"}, {"SourceFile": "Services\\Capl\\CaplParamTypes.cs"}, {"SourceFile": "Services\\Capl\\Loaders\\ArxmlLoader.cs"}, {"SourceFile": "Services\\Capl\\Core\\ArxmlLoaderBase.cs"}, {"SourceFile": "Services\\Capl\\Core\\ArxmlLoaderContext.cs"}, {"SourceFile": "Services\\Capl\\Loaders\\BusTypeLoader.cs"}, {"SourceFile": "Services\\Capl\\Loaders\\BusTypeMapLoader.cs"}, {"SourceFile": "Services\\Capl\\Loaders\\CarConfigFrameIdLoader.cs"}, {"SourceFile": "Services\\Capl\\IParamCollection.cs"}, {"SourceFile": "Services\\Capl\\CaplUtils.cs"}, {"SourceFile": "Services\\Capl\\Loaders\\CarConfigParamsLoader.cs"}, {"SourceFile": "Services\\Capl\\Loaders\\ClusterDiagFramesLoader.cs"}, {"SourceFile": "Services\\Capl\\Loaders\\DiagRoutingLoader.cs"}, {"SourceFile": "Services\\Capl\\Loaders\\Dids0x2ELoader.cs"}, {"SourceFile": "Services\\Capl\\Loaders\\DidsLoader.cs"}, {"SourceFile": "Services\\Capl\\Loaders\\DtcExtLoader.cs"}, {"SourceFile": "Services\\Capl\\Loaders\\DtcsLoader.cs"}, {"SourceFile": "Services\\Capl\\Loaders\\E2ECanFramesLoader.cs"}, {"SourceFile": "Services\\Capl\\Loaders\\E2ECarUsageModeInfoLoader.cs"}, {"SourceFile": "Services\\Capl\\Loaders\\E2EFrameExtension.cs"}, {"SourceFile": "Services\\Capl\\Loaders\\E2ELinFrameLoader.cs"}, {"SourceFile": "Services\\Capl\\Loaders\\E2EUbatSimulationFrameLoader.cs"}, {"SourceFile": "Services\\Capl\\Loaders\\E2EVehicleSpeedFrameLoader.cs"}, {"SourceFile": "Services\\Capl\\Loaders\\LdfLoader.cs"}, {"SourceFile": "Services\\Capl\\Loaders\\LoaderUtils.cs"}, {"SourceFile": "Services\\Capl\\Loaders\\P4ServerTimeLoader.cs"}, {"SourceFile": "Services\\Capl\\Loaders\\PncInfoLoader.cs"}, {"SourceFile": "Services\\Capl\\Loaders\\SecurityLevelsLoader.cs"}, {"SourceFile": "Services\\Capl\\Loaders\\SignalRoutingLoader.cs"}, {"SourceFile": "Services\\Capl\\Loaders\\SwAppTimeParamsLoader.cs"}, {"SourceFile": "Services\\Capl\\Loaders\\SwPblTimeParamsLoader.cs"}, {"SourceFile": "Services\\Capl\\Loaders\\SwsLoader.cs"}, {"SourceFile": "Services\\Capl\\Loaders\\VfcInfoLoader.cs"}, {"SourceFile": "Services\\Capl\\Loaders\\VoltageRelatedDtcLoader.cs"}, {"SourceFile": "Services\\Capl\\CaplParamAttribute.cs"}, {"SourceFile": "Services\\Capl\\ParamCollection.cs"}, {"SourceFile": "Services\\CinParameterService.cs"}, {"SourceFile": "Services\\CinTemplatesService.cs"}, {"SourceFile": "Services\\DataLogConvertService.cs"}, {"SourceFile": "Services\\ExternalAppsService.cs"}, {"SourceFile": "obj\\x64\\Debug\\.NETFramework,Version=v4.6.2.AssemblyAttributes.cs"}], "References": [{"Reference": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App\\bin\\x64\\Debug\\Alsi.App.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App\\bin\\x64\\Debug\\Alsi.App.dll"}, {"Reference": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Log\\bin\\x64\\Debug\\Alsi.Common.Log.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Log\\bin\\x64\\Debug\\Alsi.Common.Log.dll"}, {"Reference": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Parsers\\bin\\x64\\Debug\\Alsi.Common.Parsers.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Parsers\\bin\\x64\\Debug\\Alsi.Common.Parsers.dll"}, {"Reference": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Utils\\bin\\x64\\Debug\\net462\\Alsi.Common.Utils.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Utils\\bin\\x64\\Debug\\net462\\Alsi.Common.Utils.dll"}, {"Reference": "D:\\nuget_packages\\bouncycastle.cryptography\\2.2.1\\lib\\net461\\BouncyCastle.Cryptography.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\nuget_packages\\enums.net\\4.0.1\\lib\\net45\\Enums.NET.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\nuget_packages\\sharpziplib\\1.4.2\\lib\\netstandard2.0\\ICSharpCode.SharpZipLib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\nuget_packages\\mathnet.numerics.signed\\4.15.0\\lib\\net461\\MathNet.Numerics.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\Microsoft.CSharp.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\nuget_packages\\microsoft.io.recyclablememorystream\\2.3.2\\lib\\net462\\Microsoft.IO.RecyclableMemoryStream.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\nuget_packages\\microsoft.owin\\4.2.2\\lib\\net45\\Microsoft.Owin.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\nuget_packages\\microsoft.owin.host.httplistener\\4.2.2\\lib\\net45\\Microsoft.Owin.Host.HttpListener.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\nuget_packages\\microsoft.owin.hosting\\4.2.2\\lib\\net45\\Microsoft.Owin.Hosting.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\Microsoft.Win32.Primitives.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\mscorlib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\netfx.force.conflicts.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\netstandard.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\nuget_packages\\newtonsoft.json.bson\\1.0.2\\lib\\net45\\Newtonsoft.Json.Bson.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\nuget_packages\\newtonsoft.json\\13.0.3\\lib\\net45\\Newtonsoft.Json.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\nuget_packages\\npoi\\2.6.2\\lib\\netstandard2.0\\NPOI.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\nuget_packages\\npoi\\2.6.2\\lib\\netstandard2.0\\NPOI.OOXML.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\nuget_packages\\npoi\\2.6.2\\lib\\netstandard2.0\\NPOI.OpenXml4Net.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\nuget_packages\\npoi\\2.6.2\\lib\\netstandard2.0\\NPOI.OpenXmlFormats.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\nuget_packages\\owin\\1.0.0\\lib\\net40\\Owin.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\nuget_packages\\serilog\\2.10.0\\lib\\net46\\Serilog.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\nuget_packages\\serilog.sinks.console\\4.1.0\\lib\\net45\\Serilog.Sinks.Console.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\nuget_packages\\serilog.sinks.file\\5.0.0\\lib\\net45\\Serilog.Sinks.File.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\nuget_packages\\sixlabors.fonts\\1.0.0\\lib\\netstandard2.0\\SixLabors.Fonts.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\nuget_packages\\sixlabors.imagesharp\\2.1.4\\lib\\netstandard2.0\\SixLabors.ImageSharp.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.AppContext.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\nuget_packages\\system.buffers\\4.5.1\\ref\\net45\\System.Buffers.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Collections.Concurrent.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Collections.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Collections.NonGeneric.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Collections.Specialized.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\System.ComponentModel.DataAnnotations.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.ComponentModel.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.ComponentModel.EventBasedAsync.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.ComponentModel.Primitives.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.ComponentModel.TypeConverter.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\nuget_packages\\system.configuration.configurationmanager\\6.0.0\\lib\\net461\\System.Configuration.ConfigurationManager.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\System.Configuration.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Console.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Data.Common.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\System.Data.DataSetExtensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\System.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\System.Data.OracleClient.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Diagnostics.Contracts.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Diagnostics.Debug.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Diagnostics.FileVersionInfo.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Diagnostics.Process.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Diagnostics.StackTrace.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Diagnostics.TextWriterTraceListener.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Diagnostics.Tools.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Diagnostics.TraceSource.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Diagnostics.Tracing.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Drawing.Primitives.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Dynamic.Runtime.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Globalization.Calendars.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Globalization.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Globalization.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.IO.Compression.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.IO.Compression.ZipFile.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.IO.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.IO.FileSystem.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.IO.FileSystem.DriveInfo.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.IO.FileSystem.Primitives.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.IO.FileSystem.Watcher.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.IO.IsolatedStorage.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.IO.MemoryMappedFiles.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.IO.Pipes.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.IO.UnmanagedMemoryStream.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Linq.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Linq.Expressions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Linq.Parallel.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Linq.Queryable.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\nuget_packages\\system.memory\\4.5.5\\lib\\net461\\System.Memory.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\System.Net.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Net.Http.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\nuget_packages\\microsoft.aspnet.webapi.client\\6.0.0\\lib\\net45\\System.Net.Http.Formatting.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Net.NameResolution.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Net.NetworkInformation.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Net.Ping.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Net.Primitives.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Net.Requests.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Net.Security.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Net.Sockets.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Net.WebHeaderCollection.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Net.WebSockets.Client.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Net.WebSockets.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\System.Numerics.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\nuget_packages\\system.numerics.vectors\\4.5.0\\ref\\net46\\System.Numerics.Vectors.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.ObjectModel.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Reflection.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Reflection.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Reflection.Primitives.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Resources.Reader.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Resources.ResourceManager.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Resources.Writer.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\nuget_packages\\system.runtime.compilerservices.unsafe\\6.1.1\\lib\\net462\\System.Runtime.CompilerServices.Unsafe.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Runtime.CompilerServices.VisualC.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Runtime.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Runtime.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Runtime.Handles.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Runtime.InteropServices.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net462\\lib\\System.Runtime.InteropServices.RuntimeInformation.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Runtime.Numerics.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\System.Runtime.Serialization.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Runtime.Serialization.Formatters.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Runtime.Serialization.Json.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Runtime.Serialization.Primitives.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Runtime.Serialization.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\nuget_packages\\system.security.accesscontrol\\6.0.0\\lib\\net461\\System.Security.AccessControl.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Security.Claims.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Security.Cryptography.Algorithms.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Security.Cryptography.Csp.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Security.Cryptography.Encoding.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Security.Cryptography.Primitives.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Security.Cryptography.X509Certificates.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\nuget_packages\\system.security.cryptography.xml\\6.0.1\\lib\\net461\\System.Security.Cryptography.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\System.Security.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\nuget_packages\\system.security.permissions\\6.0.0\\lib\\net461\\System.Security.Permissions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Security.Principal.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\nuget_packages\\system.security.principal.windows\\5.0.0\\ref\\net461\\System.Security.Principal.Windows.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Security.SecureString.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\System.ServiceProcess.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\nuget_packages\\system.text.encoding.codepages\\5.0.0\\lib\\net461\\System.Text.Encoding.CodePages.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Text.Encoding.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Text.Encoding.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Text.RegularExpressions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Threading.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Threading.Overlapped.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Threading.Tasks.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\nuget_packages\\system.threading.tasks.extensions\\4.5.4\\lib\\net461\\System.Threading.Tasks.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Threading.Tasks.Parallel.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Threading.Thread.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Threading.ThreadPool.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Threading.Timer.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\System.Transactions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.ValueTuple.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\nuget_packages\\microsoft.aspnet.webapi.core\\5.3.0\\lib\\net45\\System.Web.Http.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\nuget_packages\\microsoft.aspnet.webapi.owin\\5.3.0\\lib\\net45\\System.Web.Http.Owin.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\System.Windows.Forms.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\System.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\System.Xml.Linq.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Xml.ReaderWriter.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Xml.XDocument.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Xml.XmlDocument.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Xml.XmlSerializer.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Xml.XPath.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Microsoft\\Microsoft.NET.Build.Extensions\\net461\\lib\\System.Xml.XPath.XDocument.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\WindowsBase.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Core\\bin\\x64\\Debug\\Alsi.Tab.Kit.Core.dll", "OutputItemRelativePath": "Alsi.Tab.Kit.Core.dll"}, {"OutputItemFullPath": "", "OutputItemRelativePath": ""}], "CopyToOutputEntries": []}