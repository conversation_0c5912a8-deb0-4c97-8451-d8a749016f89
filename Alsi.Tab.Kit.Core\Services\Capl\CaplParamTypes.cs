﻿using Alsi.Common.Parsers.Arxml.Models;
using Alsi.Common.Utils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml.Serialization;

namespace Alsi.Tab.Kit.Core.Services.Capl
{
    public enum PncGatewayType
    {
        None = 0,
        Passive = 1,
        Active = 2
    }

    [Flags]
    public enum BusType
    {
        None = 0b0000_0000,
        Can = 0b0000_0001,
        Lin = 0b0000_0010,
        Ethernet = 0b0000_0100
    }

    public enum CanBusType
    {
        Can = 1,
        CanFd = 2
    }

    public class ClusterBustypeMap
    {
        public string clusterName;
        public ClusterBusType busType;
        public ClusterBustypeMap()
        {
        }
        public ClusterBustypeMap(string clusterName, ClusterBusType busType)
        {
            this.clusterName = clusterName;
            this.busType = busType;
        }
    }


    public class ClusterPncGatewayType
    {
        public string clusterName;
        public PncGatewayType pncGatewayType;

        public ClusterPncGatewayType()
        {
        }

        public ClusterPncGatewayType(string clusterName, PncGatewayType pncGatewayType)
        {
            this.clusterName = clusterName;
            this.pncGatewayType = pncGatewayType;
        }
    }

    public class ClusterDiagFrames
    {
        public ClusterDiagFrames()
        {
        }
        public ClusterDiagFrames(string clusterName, ClusterBusType busType, GwDiagFrame[] frames)
        {
            ClusterName = clusterName;
            BusType = busType;
            Frames = frames;
        }
        public string ClusterName { get; set; }
        public ClusterBusType BusType { get; set; }
        public GwDiagFrame[] Frames { get; set; }
    }
    public class GwDiagFrame
    {
        public GwDiagFrame()
        {
        }
        public GwDiagFrame(string name, uint id, Direction dir)
        {
            Name = name;
            Id = id;
            Dir = dir;
        }
        public string Name { get; set; }
        public uint Id { get; set; }
        public Direction Dir { get; set; }
    }

    public class DiagRouting
    {
        public string srcClusterName;
        public uint srcDiagId;
        public int srcChn = 0;
        public ClusterBusType srcBusType;
        public string dstClusterName;
        public uint dstDiagId;
        public int dstChn = 0;
        public ClusterBusType dstBusType;
        public DiagType diagType;
        public DiagRouting()
        {
        }
        public DiagRouting(string srcClusterName, uint srcDiagId, ClusterBusType srcBusType, string dstClusterName, uint dstDiagId, ClusterBusType dstBusType, DiagType diagType)
        {
            this.srcClusterName = srcClusterName;
            this.srcDiagId = srcDiagId;
            this.srcBusType = srcBusType;
            this.dstClusterName = dstClusterName;
            this.dstDiagId = dstDiagId;
            this.dstBusType = dstBusType;
            this.diagType = diagType;
        }
    }

    public enum DiagType
    {
        req = 1,
        res = 2
    }

    public class STRU_DID
    {
        public STRU_DID()
        {
        }

        public STRU_DID(byte sessionMask, uint did, int valueLength, params byte[] value)
        {
            this.sessionMask = sessionMask;
            this.did = did;
            this.valueLength = valueLength;
            this.value = value;
        }

        public static STRU_DID CreateWithSaLevel(byte sessionMask, uint did, byte saLevel, int valueLength, params byte[] value)
        {
            return new STRU_DID(sessionMask, did, valueLength, value)
            {
                saLevel = saLevel
            };
        }

        public SwType swType;
        public byte sessionMask;
        public uint did;
        public int valueLength;
        public byte[] value = new byte[200];
        public byte saLevel;

        public override string ToString()
        {
            return $"sessionMask={sessionMask:X} did={did:X} saLevel={saLevel:X} length={valueLength} value={value.ToHex()}";
        }
    };

    public enum SwType
    {
        APP = 1,
        PBL = 2,
        SBL = 3,
    }

    /// <summary>
    /// SW 信息
    /// </summary>
    public class Sw
    {
        public Sw()
        {
        }

        public Sw(string name, SwType swType, SwService[] swServices)
        {
            Name = name;
            SwType = swType;
            SwServices = swServices;
        }

        /// <summary>
        /// SW 名称
        /// </summary>
        public string Name { get; set; }
        /// <summary>
        /// SW 类型
        /// </summary>
        public SwType SwType { get; set; }
        /// <summary>
        /// SW 下的所有服务
        /// </summary>
        public SwService[] SwServices { get; set; }
    }

    /// <summary>
    /// 服务信息
    /// </summary>
    public class SwService
    {
        public SwService()
        {
        }

        public SwService(byte id, string name, SwSession[] swSessions, SwSubfunction[] swSubfunctions)
        {
            Id = id;
            Name = name;
            SwSessions = swSessions;
            SwSubfunctions = swSubfunctions;
        }

        /// <summary>
        /// 服务ID
        /// </summary>
        public byte Id { get; set; }
        /// <summary>
        /// 服务名称
        /// </summary>
        public string Name { get; set; }
        /// <summary>
        /// 服务在哪些会话下可用
        /// 在SDDB的文件定义中，一般当某个服务包含 Subfunction 的时候：服务节点的会话信息为空，会话信息挂载在 Subfunction 上
        /// </summary>
        public SwSession[] SwSessions { get; set; } = Array.Empty<SwSession>();
        /// <summary>
        /// 服务下的子服务集合
        /// </summary>
        public SwSubfunction[] SwSubfunctions { get; set; } = Array.Empty<SwSubfunction>();
        /// <summary>
        /// 服务在某个会话下，是否可用
        /// 判定标准为：
        /// 1. 服务在该会话下直接可用
        /// 2. 子服务在该会话下可用
        /// </summary>
        /// <param name="sessionId">会话ID</param>
        /// <returns>是否可用</returns>
        public bool SupportSession(int sessionId)
        {
            return SwSessions.Any(x => x.Id == sessionId)
                || SwSubfunctions.Any(x => x.SupportSession(sessionId));
        }
    }

    /// <summary>
    /// 子服务信息
    /// </summary>
    public class SwSubfunction
    {
        public SwSubfunction()
        {
        }

        public SwSubfunction(byte id, string name, SwSession[] swSessions, SwRoutineIdentifier[] swRoutineIdentifiers)
        {
            Id = id;
            Name = name;
            SwSessions = swSessions;
            SwRoutineIdentifiers = swRoutineIdentifiers;
        }

        /// <summary>
        /// 子服务ID
        /// </summary>
        public byte Id { get; set; }
        /// <summary>
        /// 子服务名称
        /// </summary>
        public string Name { get; set; }
        /// <summary>
        /// 子服务在哪些会话下可用
        /// </summary>
        public SwSession[] SwSessions { get; set; } = Array.Empty<SwSession>();
        /// <summary>
        /// 子服务下的例程
        /// </summary>
        public SwRoutineIdentifier[] SwRoutineIdentifiers { get; set; } = Array.Empty<SwRoutineIdentifier>();

        /// <summary>
        /// 子服务在某个会话下，是否可用
        /// 判定标准为：
        /// 1. 子服务在该会话下直接可用
        /// 2. 子服务中的例程在该会话下可用
        /// </summary>
        /// <param name="sessionId">会话ID</param>
        /// <returns>是否可用</returns>
        public bool SupportSession(int sessionId)
        {
            return SwSessions.Any(x => x.Id == sessionId)
                || SwRoutineIdentifiers.Any(x => x.SupportSession(sessionId));
        }
    }

    /// <summary>
    /// SW 例程
    /// </summary>
    public class SwRoutineIdentifier
    {
        public SwRoutineIdentifier()
        {
        }

        public SwRoutineIdentifier(
            int id,
            string name,
            int routineType,
            int requestLength,
            int responseLength,
            SwSession[] swSessions,
            byte[] securityLevels)
        {
            Id = id;
            Name = name;
            RoutineType = routineType;
            RequestLength = requestLength;
            ResponseLength = responseLength;
            SwSessions = swSessions;
            SecurityLevels = securityLevels;
        }

        /// <summary>
        /// 例程 ID
        /// </summary>
        public int Id { get; set; }
        /// <summary>
        /// 例程名称
        /// </summary>
        public string Name { get; set; }
        /// <summary>
        /// 例程类型
        /// </summary>
        public int RoutineType { get; set; }
        /// <summary>
        /// 请求长度
        /// </summary>
        public int RequestLength { get; set; }
        /// <summary>
        /// 响应长度
        /// </summary>
        public int ResponseLength { get; set; }
        /// <summary>
        /// 例程在哪些会话下可用
        /// </summary>
        public SwSession[] SwSessions { get; set; } = Array.Empty<SwSession>();

        /// <summary>
        /// 安全等级
        /// </summary>
        public byte[] SecurityLevels { get; set; } = Array.Empty<byte>();

        /// <summary>
        /// 例程在某个会话下，是否可用
        /// 判定标准为：
        /// 1. 例程在该会话下直接可用
        /// </summary>
        /// <param name="sessionId">会话ID</param>
        /// <returns>是否可用</returns>
        public bool SupportSession(int sessionId)
        {
            return SwSessions.Any(x => x.Id == sessionId);
        }
    }

    /// <summary>
    /// 会话信息
    /// </summary>
    public class SwSession
    {
        public SwSession()
        {
        }

        public SwSession(byte id, string name)
        {
            Id = id;
            Name = name;
        }

        /// <summary>
        /// 会话ID
        /// </summary>
        public byte Id { get; set; }
        /// <summary>
        /// 会话名称
        /// </summary>
        public string Name { get; set; }
    }

    public class STRU_DTC_EXTDATA
    {
        public byte extDataRecordNum;
        public byte extDataLen;

        public STRU_DTC_EXTDATA()
        {
        }

        public STRU_DTC_EXTDATA(byte extDataRecordNum, byte extDataLen)
        {
            this.extDataRecordNum = extDataRecordNum;
            this.extDataLen = extDataLen;
        }

        public override string ToString()
        {
            return $"0x{extDataRecordNum:X} {extDataLen}";
        }
    }

    public class MissingFrameDtcInformation
    {
        public uint msgId;
        public uint dtc;
        public string nodeName;
        public float testPeriod;
        public int maxValue;
        public int stepUp;
        public int stepDown;
        public int confirmedDTCLimit;
        public int unconfirmedDTCLimit;
        public int agedDTCLimit;
        public int testFailedLimit;

        public MissingFrameDtcInformation()
        {
        }
        public MissingFrameDtcInformation(uint dtc)
        {
            this.dtc = dtc;
        }
        public MissingFrameDtcInformation(
            uint dtc,
            string nodeName,
            float testPeriod,
            int maxValue,
            int stepUp,
            int stepDown,
            int confirmedDTCLimit,
            int agedDTCLimit,
            int testFailedLimit,
            uint msgId = 0)
        {
            this.dtc = dtc;
            this.nodeName = nodeName;
            this.testPeriod = testPeriod;
            this.maxValue = maxValue;
            this.stepUp = stepUp;
            this.stepDown = stepDown;
            this.confirmedDTCLimit = confirmedDTCLimit;
            this.agedDTCLimit = agedDTCLimit;
            this.testFailedLimit = testFailedLimit;
            this.msgId = msgId;
        }
    }

    public enum SigType
    {
        CarMode = 1,
        UsageMode = 2,
        ElPowerLevel = 3,
        UB = 4
    };

    public class E2ESignal
    {
        public string name;
        public uint startBit;
        public byte length;
        public SigType sigType;

        public E2ESignal()
        {
        }

        public E2ESignal(string name, uint startBit, byte length, SigType sigType = 0)
        {
            this.name = name;
            this.startBit = startBit;
            this.length = length;
            this.sigType = sigType;
        }

        public override string ToString()
        {
            return $"{name} - {startBit} - {length} - {sigType}";
        }
    }

    public class E2ESignalGroup
    {
        public uint? dtc;

        [XmlIgnore]
        public uint dataId
        {
            get => _internalXml_DataId ?? 0;
            set => _internalXml_DataId = value;
        }

        /// <summary>
        ///  ※ _internalXml_DataId 仅用于数据初始设置 + XML 序列化，可以为 null
        ///  当其为 null 的时候，UI 上默认不显示 data id 字段
        ///  平时使用的时候，仍使用 dataId 字段不变
        /// </summary>
        [XmlElement("dataId")]
        public uint? _internalXml_DataId { get; set; }

        public E2ESignal[] signals = Array.Empty<E2ESignal>();

        public E2ESignalGroup()
        {
        }

        public E2ESignalGroup(uint? xmlDataId, E2ESignal[] signals, uint? dtc = null)
        {
            this._internalXml_DataId = xmlDataId;
            this.signals = signals;
            this.dtc = dtc;
        }
    }

    public enum E2EByteOrder
    {
        Motorola = 0,
        Intel = 1,
        Opaque = 2
    }

    public class E2EFrame
    {
        public uint frameID;
        public long cycle;
        public int dataLength;
        public E2EByteOrder byteOrder;
        public E2ESignalGroup[] signalGroups = Array.Empty<E2ESignalGroup>();
        public string frameName;

        public E2EFrame()
        {
        }

        public E2EFrame(uint frameID, long cycle, int dataLength, E2EByteOrder byteOrder, E2ESignalGroup[] signalGroups)
        {
            this.frameID = frameID;
            this.cycle = cycle;
            this.dataLength = dataLength;
            this.byteOrder = byteOrder;
            this.signalGroups = signalGroups;
        }
        public E2EFrame(uint frameID, long cycle, int dataLength, E2EByteOrder byteOrder, E2ESignalGroup[] signalGroups, string frameName)
        {
            this.frameID = frameID;
            this.cycle = cycle;
            this.dataLength = dataLength;
            this.byteOrder = byteOrder;
            this.signalGroups = signalGroups;
            this.frameName = frameName;
        }
    }

    public class ClusterE2EFrame
    {
        public string clusterName;
        public E2EFrame[] e2eFrames = Array.Empty<E2EFrame>();
        public ClusterE2EFrame()
        {

        }
        public ClusterE2EFrame(string clusterName, E2EFrame[] e2eFrames)
        {
            this.clusterName = clusterName;
            this.e2eFrames = e2eFrames;
        }
    }

    public class DTCInformation
    {
        public uint msgId;
        public uint dtc;
        public float testPeriod;
        public int maxValue;
        public int stepUp;
        public int stepDown;
        public int confirmedDTCLimit;
        public int unconfirmedDTCLimit;
        public int agedDTCLimit;
        public int testFailedLimit;

        public DTCInformation()
        {
        }

        public DTCInformation(
            uint dtc,
            float testPeriod,
            int maxValue,
            int stepUp,
            int stepDown,
            int confirmedDTCLimit,
            int agedDTCLimit,
            int testFailedLimit,
            uint msgId = 0)
        {
            this.dtc = dtc;
            this.testPeriod = testPeriod;
            this.maxValue = maxValue;
            this.stepUp = stepUp;
            this.stepDown = stepDown;
            this.confirmedDTCLimit = confirmedDTCLimit;
            this.agedDTCLimit = agedDTCLimit;
            this.testFailedLimit = testFailedLimit;
            this.msgId = msgId;
        }
    }

    public class P4SeverTime
    {
        public const int Any = -1;

        public P4SeverTime()
        {

        }

        public P4SeverTime(int sessionId, byte sid, int subfunctionId, int rid, int did, float timeoutMs)
        {
            SessionId = sessionId;
            Sid = sid;
            SubfunctionId = subfunctionId;
            Rid = rid;
            Did = did;
            TimeoutMs = timeoutMs;
        }

        public byte Sid { get; set; }
        public int SubfunctionId { get; set; }
        public int Rid { get; set; }
        public int Did { get; set; }
        public int SessionId { get; set; }
        public float TimeoutMs { get; set; }

        public override string ToString()
        {
            return $"SessionId=0x{SessionId:X} Sid=0x{Sid:X} SubfunctionId=0x{SubfunctionId:X}" +
                $" Rid=0x{Rid:X} Did=0x{Did:X} TimeoutMs={TimeoutMs}ms";
        }
    }

    public class ClusterPncInfo
    {
        public string clusterName;
        public uint channel;
        public PncInfo pncInfo = new PncInfo();
        public ClusterPncInfo()
        {

        }
        public ClusterPncInfo(string clusterName, PncInfo pncInfo)
        {
            this.pncInfo = pncInfo;
            this.clusterName = clusterName;
        }
    }
    public class PncInfo
    {
        public PncMessage[] PncMessages { get; set; } = Array.Empty<PncMessage>();
        public int[] NoMappingFrameIds { get; set; } = Array.Empty<int>();

        public PncInfo()
        {
        }
    }

    public class PncMessage
    {
        public int Pnc { get; set; }
        public int[] InMappingFrameIds { get; set; } = Array.Empty<int>();
        public int[] OutMappingFrameIds { get; set; } = Array.Empty<int>();
        public int[] NotRelatedOutMappingFrameIds { get; set; } = Array.Empty<int>();

        public override string ToString()
        {
            var stringBuilder = new StringBuilder();
            stringBuilder.Append($"PNC {Pnc}:");
            if (InMappingFrameIds.Any())
            {
                stringBuilder.Append($"[InMapping] {string.Join(", ", InMappingFrameIds.Select(x => x.ToHex()))}");
            }

            if (OutMappingFrameIds.Any())
            {
                stringBuilder.Append($"[OutMapping] {string.Join(", ", OutMappingFrameIds.Select(x => x.ToHex()))}");
            }

            if (NotRelatedOutMappingFrameIds.Any())
            {
                stringBuilder.Append($"[NotRelatedOutMapping] {string.Join(", ", NotRelatedOutMappingFrameIds.Select(x => x.ToHex()))}");
            }

            return stringBuilder.ToString();
        }
    }

    public class Vfc
    {
        public string VfcName;
        public int[] PncMapping = { };

        public Vfc()
        {
        }

        public Vfc(string vfcName, IEnumerable<int> pncMapping)
        {
            VfcName = vfcName;
            PncMapping = pncMapping.OrderBy(x => x).ToArray();
        }
    }

    public class SignalRouting
    {
        public SignalRouting()
        {
        }

        public SignalRouting(SignalToRoute sourceSignal, SignalToRoute targetSignal)
        {
            SourceSignal = sourceSignal;
            TargetSignal = targetSignal;
        }

        public SignalToRoute SourceSignal { get; set; }
        public SignalToRoute TargetSignal { get; set; }

    }

    public class SignalToRoute
    {
        public SignalToRoute()
        {
        }

        public SignalToRoute(string signalName, string channelName, ClusterBusType busType, string frameName, uint id, Direction dir, string pduName, string systemSignalName, string signalGroupName, int startPosition, int length, int updateBit)
        {
            SignalName = signalName;
            ChannelName = channelName;
            BusType = busType;
            FrameName = frameName;
            Id = id;
            Dir = dir;
            PduName = pduName;
            SystemSignalName = systemSignalName;
            SignalGroupName = signalGroupName;
            StartPosition = startPosition;
            Length = length;
            UpdateBit = updateBit;
        }
        public string SignalName { get; set; }
        public string ChannelName { get; set; }
        public ClusterBusType BusType { get; set; }
        public string FrameName { get; set; }
        public uint Id { get; set; }
        public Direction Dir { get; set; }
        public string PduName { get; set; }
        public string SystemSignalName { get; set; }
        public string SignalGroupName { get; set; }
        public int StartPosition { get; set; }
        public int Length { get; set; }
        public int UpdateBit { get; set; }
    }
}
