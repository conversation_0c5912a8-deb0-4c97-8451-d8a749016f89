﻿using Alsi.Common.Parsers.Sddb;
using Alsi.Common.Utils;

namespace Alsi.Tab.Kit.Core.Services.Capl.Params
{
    internal class BusTypeLoader
    {
        public void Load(string ecuNode, string source, SW sw, IParamCollection collection, int order)
        {
            var busType = BusType.None;
            CanBusType canBusType = 0;
            if (sw.TransportLayers.TransportLayer.Type.ToUpper() == "CAN")
            {
                canBusType = CanBusType.Can;
                busType = BusType.Can;
            }
            else if (sw.TransportLayers.TransportLayer.Type.ToUpper() == "CANFD")
            {
                canBusType = CanBusType.CanFd;
            }
            else if (sw.TransportLayers.TransportLayer.Type.ToUpper() == "LIN")
            {
                busType = BusType.Lin;
            }

            if (canBusType != 0)
            {
                var value = EnumUtils.GetDisplay(canBusType);
                var key = CaplParamConsts.CanBusType;
                collection.AddValue(ecuNode, key, value, source, order);
            }

            if (busType != BusType.None)
            {
                var value = EnumUtils.GetDisplay(busType);
                var key = CaplParamConsts.BusType;
                collection.AddValue(ecuNode, key, value, source, order);
            }
        }
    }
}
