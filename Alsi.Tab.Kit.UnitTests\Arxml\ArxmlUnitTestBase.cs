﻿using System.Reflection;
namespace Alsi.Tab.Kit.UnitTests;

public class ArxmlUnitTestBase
{
    protected string GetArxmlPath(string arxmlFileName)
    {
        var assemblyLocation = Assembly.GetExecutingAssembly().Location;
        var arxmlPath = Path.Combine(Directory.GetParent(assemblyLocation)!.FullName, $"Arxml\\Files\\{arxmlFileName}");
        if (!File.Exists(arxmlPath))
        {
            throw new Exception($"The file is not exists: {arxmlPath}");
        }

        return arxmlPath;
    }
}
