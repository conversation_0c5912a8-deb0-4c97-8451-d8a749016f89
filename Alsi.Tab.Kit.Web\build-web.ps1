﻿# 定义时间戳文件路径
$timestampFile = ".\build-timestamp.json"
$sourceDirectory = ".\web"
$excludeDirs = @("node_modules", "dist")
$needRebuild = $false

# 获取源文件列表（排除指定目录）
function Get-SourceFiles {
    param([string]$directory, [string[]]$excludeDirs)
    
    return Get-ChildItem -Path $directory -Recurse -File | 
        Where-Object { 
            $fullPath = $_.FullName
            $exclude = $false
            foreach ($dir in $excludeDirs) {
                if ($fullPath -like "*\$dir\*") {
                    $exclude = $true
                    break
                }
            }
            -not $exclude
        } | 
        Select-Object FullName, LastWriteTime
}

# 检查是否需要重新构建
function Check-NeedRebuild {
    # 如果时间戳文件不存在，需要重新构建
    if (-not (Test-Path $timestampFile)) {
        Write-Host "时间戳文件不存在，需要重新构建"
        return $true
    }
    
    try {
        # 读取上次构建的时间戳信息
        $lastBuild = Get-Content $timestampFile -Raw | ConvertFrom-Json
        
        # 获取当前源文件信息
        $currentFiles = Get-SourceFiles -directory $sourceDirectory -excludeDirs $excludeDirs
        
        # 检查文件数量是否变化
        if ($currentFiles.Count -ne $lastBuild.files.Count) {
            Write-Host "文件数量已变化，需要重新构建"
            return $true
        }
        
        # 检查每个文件修改时间
        foreach ($file in $currentFiles) {
            $relativePath = $file.FullName.Replace((Get-Item $sourceDirectory).FullName + "\", "")
            $fileInfo = $lastBuild.files | Where-Object { $_.path -eq $relativePath }
            
            # 如果文件不存在于上次构建记录或文件修改时间与记录不同（不仅是更新的情况）
            if (-not $fileInfo -or $file.LastWriteTime -ne [datetime]::Parse($fileInfo.time)) {
                Write-Host "文件已变化：$relativePath，需要重新构建"
                return $true
            }
        }
        
        # 如果没有变化，不需要重新构建
        return $false
    }
    catch {
        Write-Warning "检查构建状态时出错: $($_.Exception.Message)"
        return $true
    }
}

# 更新构建时间戳
function Update-BuildTimestamp {
    $fileList = @()
    Get-SourceFiles -directory $sourceDirectory -excludeDirs $excludeDirs | 
        ForEach-Object {
            $relativePath = $_.FullName.Replace((Get-Item $sourceDirectory).FullName + "\", "")
            $fileList += @{
                path = $relativePath
                time = $_.LastWriteTime.ToString("o")
            }
        }
    
    $buildInfo = @{
        buildTime = (Get-Date).ToString("o")
        files = $fileList
    }
    
    $buildInfo | ConvertTo-Json -Depth 10 | Set-Content $timestampFile
    Write-Host "已更新构建时间戳文件"
}

# 检查是否需要重建
Write-Host "开始检测源文件是否改变..."
$needRebuild = Check-NeedRebuild

# 进入 web 目录  
cd web

if ($needRebuild) {
    # 构建项目  
    Write-Host "开始构建项目..."
    yarn install
    yarn build
    
    # 返回上级目录并更新时间戳  
    cd ..
    Update-BuildTimestamp
} else {
    Write-Host "源文件未变化，跳过构建步骤"
    cd ..

    # 如果没有变化，直接退出脚本
    Write-Host "脚本执行完毕"
    return
}

# 指定要压缩的目录  
$SourceDirectory = ".\web\dist"  

# 指定压缩文件的名称和路径  
$ZipFilePath = ".\web.zip"

# 检查源目录是否存在，如果不存在则创建  
if (!(Test-Path -Path $SourceDirectory -PathType Container)) {  
    Write-Host "源目录 '$SourceDirectory' 不存在，正在创建..."  
    try {  
        New-Item -ItemType Directory -Path $SourceDirectory -Force  
        Write-Host "源目录 '$SourceDirectory' 创建成功。"  
    }  
    catch {  
        Write-Error "创建源目录 '$SourceDirectory' 时发生错误: $($_.Exception.Message)"  
        exit  # 停止脚本执行  
    }  
}  

# 检查是否已经存在同名的 Zip 文件，如果存在则删除  
if (Test-Path -Path $ZipFilePath) {  
    Write-Warning "已存在文件 '$ZipFilePath'，正在删除..."  
    try {  
        Remove-Item -Path $ZipFilePath -Force  
    }  
    catch {  
        Write-Error "无法删除已存在的文件 '$ZipFilePath'。请检查文件是否被占用，或者是否有足够的权限。"  
        exit  
    }  
}  

# 加载 .NET 的压缩程序集  
Add-Type -AssemblyName "System.IO.Compression.FileSystem"  

# 创建压缩文件  
try {  
    Write-Host "正在创建压缩文件 '$ZipFilePath'..."  
    [System.IO.Compression.ZipFile]::CreateFromDirectory($SourceDirectory, $ZipFilePath)  
    Write-Host "压缩文件 '$ZipFilePath' 创建成功。"  
}  
catch {  
    Write-Error "创建压缩文件时发生错误: $($_.Exception.Message)"  
}