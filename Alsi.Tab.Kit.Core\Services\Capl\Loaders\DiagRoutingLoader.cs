﻿using Alsi.Common.Utils;
using Alsi.Common.Utils.Linq;
using Alsi.Tab.Kit.Core.Services.Capl.Core;
using System.Collections.Generic;
using System.Linq;

namespace Alsi.Tab.Kit.Core.Services.Capl.Params
{
    internal class DiagRoutingLoader : ArxmlLoaderBase
    {
        public DiagRoutingLoader(ArxmlLoaderContext context)
            : base(context)
        {
        }

        public override void Load()
        {
            var clusterBustypes = arxmlModel.ClusterNameBusType;
            var diagRoutingList = new List<DiagRouting>();
            var gatewayInfo = (GATEWAY)arxmlModel.ARPACKAGES.Where(x => x.SHORTNAME.Value == "Communication")
                .SelectMany(x => x.ARPACKAGES)
                .Where(x => x.SHORTNAME.Value == "Gateway")
                .SelectManyEx(x => x.ELEMENTS)
                .FirstOrDefault();
            if (gatewayInfo == null)
            {
                return;
            }
            var tpConfigs = arxmlModel.ARPACKAGES.Where(x => x.SHORTNAME.Value == "TpConfig")
                                      .SelectManyEx(x => x.ELEMENTS)
                                      .ToArray();

            var pduMappings = gatewayInfo.IPDUMAPPINGS;
            if (pduMappings == null)
            {
                return;
            }
            foreach (var pduMapping in pduMappings)
            {
                uint srcDiagId, dstDiagId;
                var srcIpduRef = pduMapping.SOURCEIPDUREF.Value.Split('/').Last();
                var srcClusterName = pduMapping.SOURCEIPDUREF.Value.Split('/')[2];
                var dstIpduRef = pduMapping.TARGETIPDU.TARGETIPDUREF1.Value.Split('/').Last();
                var dstClusterName = pduMapping.TARGETIPDU.TARGETIPDUREF1.Value.Split('/')[2];
                var srcBusType = clusterBustypes.Where(x => x.Name == srcClusterName).Select(x => x.BusType).FirstOrDefault();
                var dstBusType = clusterBustypes.Where(x => x.Name == dstClusterName).Select(x => x.BusType).FirstOrDefault();
                if (!arxmlModel.GetDiagIdByPduName(srcIpduRef, srcBusType, out srcDiagId))
                    continue;
                if (!arxmlModel.GetDiagIdByPduName(dstIpduRef, dstBusType, out dstDiagId))
                    continue;
                var diagPduType = arxmlModel.DcmIPdu.Where(x => x.SHORTNAME.Value == srcIpduRef)
                                                 .Select(x => x.DIAGPDUTYPE.Value)
                                                 .FirstOrDefault();
                var diagType = diagPduType == DIAGPDUTYPESIMPLE.DIAGREQUEST ? DiagType.req : DiagType.res;
                var diagRouting = new DiagRouting(srcClusterName, srcDiagId, srcBusType, dstClusterName, dstDiagId, dstBusType, diagType);
                diagRoutingList.Add(diagRouting);
            }
            foreach (var ecuNode in arxmlModel.EcuInstances)
            {
                var node = ecuNode.SHORTNAME.Value;
                var value = JsonUtils.Serialize(diagRoutingList.ToArray());
                var key = CaplParamConsts.DiagRouting;
                collection.AddValue(node, key, value, source, order);
            }
        }
    }
}
