﻿using Alsi.Common.Parsers.Sddb;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using Alsi.Common.Utils;

namespace Alsi.Tab.Kit.Core.Services.Capl.Params
{
    internal class DidsLoader
    {
        public void Load(
            string ecuNode, string source, SW[] sws, IParamCollection collection, int order, string regularParamKey, byte sid, bool initValue)
        {
            var list = new List<STRU_DID>();
            foreach (var sw in sws)
            {
                var service = sw.Services.Service
                    .FirstOrDefault(x => byte.TryParse(x.ID, NumberStyles.HexNumber, null, out var serviceId)
                    && serviceId == sid);
                if (service == null)
                {
                    continue;
                }

                var sessionMaskOfService = LoaderUtils.GetSessionMask(sw, service);
                if (sessionMaskOfService == 0)
                {
                    continue;
                }

                if (service.DataIdentifiers.DataIdentifier == null)
                {
                    continue;
                }

                foreach (var dataIdentifier in service.DataIdentifiers.DataIdentifier)
                {
                    if (!uint.TryParse(dataIdentifier.ID, NumberStyles.HexNumber, null, out var did))
                    {
                        continue;
                    }

                    if (!int.TryParse(dataIdentifier.Size, out var size))
                    {
                        continue;
                    }

                    var sessionMaskOfDataIdentifier = LoaderUtils.GetSessionMask(dataIdentifier.Sessions?.Session);
                    var sessionMask = sessionMaskOfDataIdentifier != 0 ? sessionMaskOfDataIdentifier : sessionMaskOfService;

                    var bytesValue = initValue
                        ? Enumerable.Range(1, size).Select(x => (byte)0xFF).ToArray()
                        : Array.Empty<byte>();

                    var struDid = new STRU_DID(sessionMask, did, size, bytesValue);
                    if (sw.Type.Equals($"{SwType.APP}", StringComparison.OrdinalIgnoreCase))
                    {
                        struDid.swType = SwType.APP;
                    }
                    else if (sw.Type.Equals($"{SwType.PBL}", StringComparison.OrdinalIgnoreCase))
                    {
                        struDid.swType = SwType.PBL;
                    }
                    else if (sw.Type.Equals($"{SwType.SBL}", StringComparison.OrdinalIgnoreCase))
                    {
                        struDid.swType = SwType.SBL;
                    }

                    var saLevels = SwsLoader.ConvertSecurityLevels(dataIdentifier.SecurityAccessRefs);
                    if (saLevels.Any())
                    {
                        struDid.saLevel = saLevels.First();
                    }

                    list.Add(struDid);
                }
            }

            var array = list.OrderBy(x => x.did).ToArray();
            var value = JsonUtils.Serialize(array);
            collection.AddValue(ecuNode, regularParamKey, value, source, order);
        }
    }
}
