﻿using Alsi.App;
using Alsi.Common.Parsers.Arxml;
using Alsi.Common.Parsers.Ldf;
using Alsi.Common.Parsers.Ldf.Model;
using Alsi.Common.Parsers.Sddb;
using Alsi.Tab.Kit.Core.Services.Capl.Core;
using Alsi.Tab.Kit.Core.Services.Capl.Params;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using ArxmlModel = AUTOSAR;
using SddbModel = Alsi.Common.Parsers.Sddb.Project;

namespace Alsi.Tab.Kit.Core.Services.Capl
{
    public class SourceFileInfo
    {
        public SourceFileInfo(string path)
        {
            Path = path;
            DisplayName = System.IO.Path.GetFileName(Path);
        }

        public string Path { get; set; }
        public string DisplayName { get; set; }
    }

    public class CaplParamLoader
    {
        private Dictionary<SourceFileInfo, ArxmlModel> ArxmlModels { get; set; } = new Dictionary<SourceFileInfo, ArxmlModel>();
        private Dictionary<SourceFileInfo, SddbModel> SddbModels { get; set; } = new Dictionary<SourceFileInfo, SddbModel>();
        private Dictionary<SourceFileInfo, LdfModel> LdfModels { get; set; } = new Dictionary<SourceFileInfo, LdfModel>();

        /// <summary>
        /// 安全执行加载器，捕获异常并记录日志
        /// </summary>
        /// <param name="loaderAction">加载器执行动作</param>
        /// <param name="loaderName">加载器名称，用于日志记录</param>
        private void SafeExecuteLoader(Action loaderAction, string loaderName = null)
        {
            try
            {
                loaderAction?.Invoke();
            }
            catch (Exception ex)
            {
                var message = string.IsNullOrEmpty(loaderName)
                    ? $"Load CAPL param failed: {ex.Message}"
                    : $"Load CAPL param failed in {loaderName}: {ex.Message}";
                AppEnv.Logger.Error(ex, message);
            }
        }

        /// <summary>
        /// 批量安全执行加载器
        /// </summary>
        /// <param name="loaderActions">加载器动作集合</param>
        private void SafeExecuteLoaders(params Action[] loaderActions)
        {
            foreach (var action in loaderActions)
            {
                SafeExecuteLoader(action);
            }
        }

        public IParamCollection Load(params string[] pathArray)
        {
            foreach (var path in pathArray)
            {
                var extension = Path.GetExtension(path);
                if (extension.Equals(".arxml", StringComparison.OrdinalIgnoreCase))
                {
                    ArxmlModels[new SourceFileInfo(path)] = (ArxmlParser.Parse(path));
                }
                else if (extension.Equals(".sddb", StringComparison.OrdinalIgnoreCase))
                {
                    SddbModels[new SourceFileInfo(path)] = (SddbParser.Parse(path));
                }
                else if (extension.Equals(".ldf", StringComparison.OrdinalIgnoreCase))
                {
                    LdfModels[new SourceFileInfo(path)] = (LdfParser.Parse(path));
                }
            }

            var collection = new ParamCollection();

            var order = 20;

            // 从 ARXML 文件里，加载数据
            LoadArxmlData(collection, order);

            // 从 SDDB 文件里，加载数据
            LoadSddbData(collection, order);

            // 从 LDF 文件里，加载数据
            LoadLdfData(collection, order);

            // 加载组合数据
            LoadCombinedData(collection, order);

            return collection;
        }

        /// <summary>
        /// 从 ARXML 文件加载数据
        /// </summary>
        private void LoadArxmlData(IParamCollection collection, int order)
        {
            foreach (var arxmlFileInfo in ArxmlModels.Keys)
            {
                var arxmlModel = ArxmlModels[arxmlFileInfo];
                var source = arxmlFileInfo.DisplayName;

                var arxmlEcuNodes = arxmlModel.EcuInstances.Select(x => x.SHORTNAME.Value).ToArray();
                var context = new ArxmlLoaderContext(arxmlModel, source, collection, order, arxmlEcuNodes);

                foreach (var ecuName in arxmlEcuNodes)
                {
                    collection.GetOrAddEcu(ecuName);
                }

                var loaders = new ArxmlLoaderBase[]
                {
                    new ArxmlLoader(context),
                    new DiagRoutingLoader(context),
                    new E2ECanFramesLoader(context),
                    new PncInfoLoader(context),
                    new CarConfigFrameIdLoader(context),
                    new VfcInfoLoader(context),
                    new SignalRoutingLoader(context),
                    new ClusterDiagFramesLoader(context),
                    new BusTypeMapLoader(context)
                };

                foreach (var loader in loaders)
                {
                    SafeExecuteLoader(() => loader.Load(), loader.GetType().Name);
                }
            }
        }

        /// <summary>
        /// 从 SDDB 文件加载数据
        /// </summary>
        private void LoadSddbData(IParamCollection collection, int order)
        {
            foreach (var sddbFileInfo in SddbModels.Keys)
            {
                var sddbProject = SddbModels[sddbFileInfo];
                var source = sddbFileInfo.DisplayName;

                foreach (var ecu in sddbProject.System.ECUs.ECU)
                {
                    var ecuNode = ecu.Name;

                    collection.GetOrAddEcu(ecuNode);

                    LoadSddbEcuData(ecuNode, source, ecu, collection, order);
                }
            }
        }

        /// <summary>
        /// 加载单个ECU的SDDB数据
        /// </summary>
        private void LoadSddbEcuData(string ecuNode, string source, dynamic ecu, IParamCollection collection, int order)
        {
            // 加载APP SW数据
            var appSW = ((IEnumerable<dynamic>)ecu.SWs.SW).LastOrDefault(x => ((string)x.Type).ToUpper() == "APP");
            if (appSW != null)
            {
                SafeExecuteLoaders(
                    () => new SwAppTimeParamsLoader().Load(ecuNode, source, appSW, collection, order),
                    () => new DtcsLoader().Load(ecuNode, source, appSW, collection, order),
                    () => new BusTypeLoader().Load(ecuNode, source, appSW, collection, order)
                );
            }

            // 加载PBL SW数据
            var pblSW = ((IEnumerable<dynamic>)ecu.SWs.SW).LastOrDefault(x => ((string)x.Type).ToUpper() == "PBL");
            if (pblSW != null)
            {
                SafeExecuteLoader(() => new SwPblTimeParamsLoader().Load(ecuNode, source, pblSW, collection, order));
            }

            // 加载其他SW数据
            SafeExecuteLoaders(
                () => new SwsLoader().Load(ecuNode, source, ecu.SWs.SW, collection, order),
                () => new P4ServerTimeLoader().Load(ecuNode, source, ecu.SWs.SW, collection, order),
                () => new DidsLoader().Load(ecuNode, source, ecu.SWs.SW, collection, order, CaplParamConsts.MandatoryDIDs0x22, 0x22, false),
                () => new DidsLoader().Load(ecuNode, source, ecu.SWs.SW, collection, order, CaplParamConsts.SupportDIDs0x22, 0x22, false),
                () => new DidsLoader().Load(ecuNode, source, ecu.SWs.SW, collection, order, CaplParamConsts.SupportDIDs0x2E, 0x2E, true),
                () => new DtcExtLoader().Load(ecuNode, source, ecu.SWs.SW, collection, order),
                () => new SecurityLevelsLoader().Load(ecuNode, source, ecu.SWs.SW, collection, order),
                () => new CarConfigParamsLoader().Load(ecuNode, source, ecu.SWs.SW, collection, order),
                () => new VoltageRelatedDtcLoader().Load(ecuNode, source, ecu.SWs.SW, collection, order)
            );
        }

        /// <summary>
        /// 从 LDF 文件加载数据
        /// </summary>
        private void LoadLdfData(IParamCollection collection, int order)
        {
            foreach (var ldfFileInfo in LdfModels.Keys)
            {
                var ldfModel = LdfModels[ldfFileInfo];
                var source = ldfFileInfo.DisplayName;

                foreach (var ldfNode in ldfModel.Nodes)
                {
                    collection.GetOrAddEcu(ldfNode.Name);
                }

                SafeExecuteLoader(() => new LdfLoader().Load(ldfModel, source, collection, order));
            }
        }

        /// <summary>
        /// 加载组合数据（需要多个文件类型的数据）
        /// </summary>
        private void LoadCombinedData(IParamCollection collection, int order)
        {
            // 加载LDF和ARXML组合数据
            if (LdfModels.Any())
            {
                var ldfFileInfo = LdfModels.First().Key;
                var ldfModel = LdfModels.First().Value;
                var sourceList = new List<string> { ldfFileInfo.DisplayName };

                ArxmlModel arxmlModel = null;
                if (ArxmlModels.Any())
                {
                    var arxmlPath = ArxmlModels.First().Key;
                    arxmlModel = ArxmlModels.First().Value;
                    sourceList.Add(arxmlPath.DisplayName);
                }

                var source = string.Join(", ", sourceList);
                SafeExecuteLoader(() => new E2ELinFrameLoader().Load(arxmlModel, ldfModel, source, collection, order));
            }

            // 加载纯ARXML数据
            if (ArxmlModels.Any())
            {
                var arxmlModel = ArxmlModels.First().Value;
                var source = ArxmlModels.First().Key.DisplayName;

                SafeExecuteLoaders(
                    () => new E2ECarUsageModeInfoLoader().Load(arxmlModel, source, collection, order),
                    () => new E2EUbatSimulationFrameLoader().Load(arxmlModel, source, collection, order),
                    () => new E2EVehicleSpeedFrameLoader().Load(arxmlModel, source, collection, order)
                );
            }
        }
    }
}
