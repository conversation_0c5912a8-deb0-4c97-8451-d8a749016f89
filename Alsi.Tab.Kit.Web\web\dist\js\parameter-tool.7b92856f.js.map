{"version": 3, "file": "js/parameter-tool.7b92856f.js", "mappings": "0LAEA,MAAMA,EAAa,CCDZC,MAAM,kBDEPC,EAAa,CCAVD,MAAM,gBDCTE,EAAa,CCCRF,MAAM,0BDAXG,EAAa,CCONH,MAAM,0BDNbI,EAAa,CCOJJ,MAAM,iBDNfK,EAAa,CACjBC,IAAK,ECmBMN,MAAM,cDhBbO,EAAa,CCkBJP,MAAM,eDjBfQ,EAAa,CCuBNR,MAAM,oBDtBbS,EAAa,CACjBH,IAAK,ECiDwDN,MAAM,oBD9C/DU,EAAc,CAClBJ,IAAK,ECmDsBN,MAAM,aD/C7B,SAAUW,EAAOC,EAAUC,EAAYC,EAAYC,EAAYC,EAAWC,GAC9E,MAAMC,GAAyBC,EAAAA,EAAAA,IAAkB,eAC3CC,GAAuBD,EAAAA,EAAAA,IAAkB,aACzCE,GAAwBF,EAAAA,EAAAA,IAAkB,cAC1CG,GAAsBH,EAAAA,EAAAA,IAAkB,YACxCI,GAA6BJ,EAAAA,EAAAA,IAAkB,mBAC/CK,GAAsBL,EAAAA,EAAAA,IAAkB,YACxCM,GAAqBN,EAAAA,EAAAA,IAAkB,WACvCO,GAAsBP,EAAAA,EAAAA,IAAkB,YACxCQ,GAA6BR,EAAAA,EAAAA,IAAkB,mBAC/CS,GAA8BT,EAAAA,EAAAA,IAAkB,oBAChDU,GAA8BV,EAAAA,EAAAA,IAAkB,oBAEtD,OAAQW,EAAAA,EAAAA,OClCRC,EAAAA,EAAAA,IAsFM,MAtFNhC,EAsFM,EApFJiC,EAAAA,EAAAA,IA2EM,MA3EN/B,EA2EM,EAzEJ+B,EAAAA,EAAAA,IAqEM,MArEN9B,EAqEM,CDpCJW,EAAO,KAAOA,EAAO,IChCrBmB,EAAAA,EAAAA,IAIM,OAJDhC,MAAM,kBAAgB,EACzBgC,EAAAA,EAAAA,IAAiB,UAAb,aACJA,EAAAA,EAAAA,IACM,OADDhC,MAAM,qBDiCT,KC7BJgC,EAAAA,EAAAA,IAUM,MAVN7B,EAUM,EATJ6B,EAAAA,EAAAA,IAQM,MARN5B,EAQM,CANIQ,EAAAqB,WAAWC,OAAS,ID8BvBJ,EAAAA,EAAAA,OC/BLK,EAAAA,EAAAA,IAE6CjB,EAAA,CD8BvCZ,IAAK,EACL8B,WCjCgBxB,EAAAyB,qBDkChB,sBAAuBxB,EAAO,KAAOA,EAAO,GAAMyB,GClClC1B,EAAAyB,qBAAoBC,GAAEC,KAAK,QAASC,QAAS5B,EAAA6B,gBACpCC,YAAY,YAAYC,MAAA,sCACpDC,SAAQhC,EAAAiC,qBAAsBC,UAAA,IDuC1B,KAAM,EAAG,CAAC,aAAc,UAAW,eACtCC,EAAAA,EAAAA,IAAoB,IAAI,ICtC5BC,EAAAA,EAAAA,IAAuF5B,EAAA,CAA5E6B,KAAK,UAAUV,KAAK,QAASW,QAAOtC,EAAAuC,iBD2C5C,CACDC,SAASC,EAAAA,EAAAA,IC5CqD,IAAWxC,EAAA,KAAAA,EAAA,KD6CvEyC,EAAAA,EAAAA,IC7C4D,kBD+C9DC,EAAG,EACHC,GAAI,CAAC,IACJ,EAAG,CAAC,aChDPR,EAAAA,EAAAA,IAC0C5B,EAAA,CAD/B6B,KAAK,UAAUV,KAAK,QAASW,QAAOtC,EAAA6C,cAAed,MAAA,uBAC3De,QAAS9C,EAAA+C,YDsDT,CACDP,SAASC,EAAAA,EAAAA,ICvDa,IAAMxC,EAAA,KAAAA,EAAA,KDwD1ByC,EAAAA,EAAAA,ICxDoB,aD0DtBC,EAAG,EACHC,GAAI,CAAC,IACJ,EAAG,CAAC,UAAW,iBCxDtBR,EAAAA,EAAAA,IAAoC3B,EAAA,CAAxBsB,MAAA,mBAGkB/B,EAAAgD,UAAU1B,OAAS,ID0D5CJ,EAAAA,EAAAA,OC1DLC,EAAAA,EAAAA,IAKM,MALN1B,EAKM,EAJJ2C,EAAAA,EAAAA,IAAyG1B,EAAA,CD2DnGc,WC3DaxB,EAAAiD,cD4Db,sBAAuBhD,EAAO,KAAOA,EAAO,GAAMyB,GC5DrC1B,EAAAiD,cAAavB,GAAEC,KAAK,QAAQG,YAAY,YAAYI,UAAA,GAAUH,MAAA,iBDiE1E,KAAM,EAAG,CAAC,gBChEjBX,EAAAA,EAAAA,IAEM,MAFNzB,EAEM,EADJyB,EAAAA,EAAAA,IAA0E,YAApE,MAAE8B,EAAAA,EAAAA,IAAGlD,EAAAmD,kBAAkB7B,QAAS,OAAG4B,EAAAA,EAAAA,IAAGlD,EAAAgD,UAAU1B,QAAS,OAAI,SDoEnEa,EAAAA,EAAAA,IAAoB,IAAI,IC/D5Bf,EAAAA,EAAAA,IAsCM,MAtCNxB,EAsCM,EArCJwC,EAAAA,EAAAA,IAoCWtB,EAAA,CApCAsC,KAAMpD,EAAAmD,kBAAmBE,OAAA,GAAOtB,MAAA,6BAAmCJ,KAAK,SDqEhF,CACDa,SAASC,EAAAA,EAAAA,ICrET,IAA6E,EAA7EL,EAAAA,EAAAA,IAA6EzB,EAAA,CAA5D2C,KAAK,OAAOC,MAAM,MAAMC,MAAM,MAAM,8BACrDpB,EAAAA,EAAAA,IAAqFzB,EAAA,CAApE2C,KAAK,OAAOC,MAAM,cAAcC,MAAM,MAAM,8BAC7DpB,EAAAA,EAAAA,IAUkBzB,EAAA,CAVD4C,MAAM,MAAM,YAAU,ODmFlC,CClFQf,SAAOC,EAAAA,EAAAA,IAOdgB,GAPqB,EACvBrB,EAAAA,EAAAA,IAME1B,EAAA,CD8EEc,WCnFOxB,EAAA0D,gBAAgBD,EAAME,IAAIC,MDoFjC,sBAAwBlC,GCpFjB1B,EAAA0D,gBAAgBD,EAAME,IAAIC,MAAIlC,EACvCI,YAAY,QACZH,KAAK,QACJK,SAAMN,GAAE1B,EAAA6D,kBAAkBJ,EAAME,IAAIC,MACpCE,SAAU9D,EAAA+D,mBAAmBN,EAAME,MDqFjC,KAAM,EAAG,CAAC,aAAc,sBAAuB,WAAY,eAEhEhB,EAAG,KCnFPP,EAAAA,EAAAA,IAUkBzB,EAAA,CAVD4C,MAAM,KAAKC,MAAM,KAAKQ,MAAM,UDyFxC,CCxFQxB,SAAOC,EAAAA,EAAAA,IAOJgB,GAPW,EACvBrB,EAAAA,EAAAA,IAMY5B,EAAA,CALV6B,KAAK,OACLV,KAAK,QACJW,QAAKZ,GAAE1B,EAAAiE,qBAAqBR,EAAME,MD0FhC,CACDnB,SAASC,EAAAA,EAAAA,IC1FZ,IAEDxC,EAAA,KAAAA,EAAA,KDyFMyC,EAAAA,EAAAA,IC3FL,WD6FGC,EAAG,EACHC,GAAI,CAAC,IACJ,KAAM,CAAC,cAEZD,EAAG,KC5FPP,EAAAA,EAAAA,IAUkBzB,EAAA,CAVD4C,MAAM,QAAQC,MAAM,MAAM,4BDkGtC,CCjGQhB,SAAOC,EAAAA,EAAAA,IAMVgB,GANiB,CACZzD,EAAAkE,mBAAmBT,EAAME,IAAIC,QDmGjC1C,EAAAA,EAAAA,OCnGPC,EAAAA,EAAAA,IAKM,MALNtB,EAKM,EAJJuC,EAAAA,EAAAA,IAEUvB,EAAA,MDkGF2B,SAASC,EAAAA,EAAAA,ICnGf,IAAY,EAAZL,EAAAA,EAAAA,IAAYxB,KDsGN+B,EAAG,KCpGXvB,EAAAA,EAAAA,IAAqD,aAAA8B,EAAAA,EAAAA,IAA5ClD,EAAAkE,mBAAmBT,EAAME,IAAIC,OAAI,QDwGrC1C,EAAAA,EAAAA,OCtGPC,EAAAA,EAAAA,IAA4C,OAA5CrB,EAA+B,aDwG/B6C,EAAG,MAGPA,EAAG,GACF,EAAG,CAAC,cCpGXP,EAAAA,EAAAA,IAAqDrB,EAAA,CAAnCoD,cAAcnE,EAAAoE,mBAAiB,6BAInDhC,EAAAA,EAAAA,IACsCpB,EAAA,CDqGpCQ,WCtGyBxB,EAAAqE,mBDuGzB,sBAAuBpE,EAAO,KAAOA,EAAO,GAAMyB,GCvGzB1B,EAAAqE,mBAAkB3C,GAAG,cAAa1B,EAAAsE,iBAAmB,gBAAetE,EAAAuE,oBAC5FC,cAAcxE,EAAAyE,mBD0Gd,KAAM,EAAG,CAAC,aAAc,cAAe,gBAAiB,mBCvG3DrC,EAAAA,EAAAA,IACoEnB,EAAA,CDwGlEO,WCzGyBxB,EAAA0E,0BD0GzB,sBAAuBzE,EAAO,KAAOA,EAAO,GAAMyB,GC1GzB1B,EAAA0E,0BAAyBhD,GAAGiD,MAAO3E,EAAA4E,yBAA2Bd,UAAU,EAChGe,MAAO7E,EAAA8E,kBAAoBC,UAAS/E,EAAAgF,2BD8GpC,KAAM,EAAG,CAAC,aAAc,QAAS,QAAS,eAEjD,C,0IEnMA,MAAM7F,EAAa,CAAEC,MAAO,sBACtBC,EAAa,CAAED,MAAO,iBAa5B,OAA4B6F,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,mBACRC,MAAO,CACL3D,WAAY,CAAEa,KAAM+C,SACpBT,MAAO,CAAC,EACRb,SAAU,CAAEzB,KAAM+C,QAAS5C,SAAS,GACpCqC,MAAO,CAAErC,QAAS,QAEpB6C,MAAO,CAAC,oBAAqB,eAAgB,WAC7CC,KAAAA,CAAMC,GAAgBC,KAAMC,ICc9B,MAAMN,EAAQI,EAMRC,EAAOC,EAOPC,GAAUC,EAAAA,EAAAA,KAAI,GACdC,GAAeD,EAAAA,EAAAA,IAAI,IAGnBE,GAAiBC,EAAAA,EAAAA,IAAS,KAC9B,GAAoB,OAAhBX,EAAMR,YAAkCoB,IAAhBZ,EAAMR,MAChC,MAAO,GAGT,GAA2B,kBAAhBQ,EAAMR,MAEf,IACE,MAAMqB,EAASC,KAAKC,MAAMf,EAAMR,OAChC,OAAOsB,KAAKE,UAAUH,EAAQ,KAAM,E,CACpC,MAEA,OAAOb,EAAMR,K,MAIf,IACE,OAAOsB,KAAKE,UAAUhB,EAAMR,MAAO,KAAM,E,CACzC,MACA,OAAOyB,OAAOjB,EAAMR,M,KAM1B0B,EAAAA,EAAAA,IAAM,IAAMlB,EAAM3D,WAAa8E,IAC7BZ,EAAQf,MAAQ2B,EACZA,IACFV,EAAajB,MAAQkB,EAAelB,UAIxC0B,EAAAA,EAAAA,IAAMX,EAAUY,IACdd,EAAK,oBAAqBc,MAG5BD,EAAAA,EAAAA,IAAM,IAAMlB,EAAMR,MAAO,KACnBe,EAAQf,QACViB,EAAajB,MAAQkB,EAAelB,SAKxC,MAAM4B,EAAcA,KAClBb,EAAQf,OAAQ,GAGZ6B,EAAe7B,IACdQ,EAAMrB,UACT0B,EAAK,eAAgBb,IAInB8B,EAAgBA,KACftB,EAAMrB,UACT0B,EAAK,UAAWI,EAAajB,OAE/B4B,KDhBF,MAAO,CAACvG,EAAUC,KAChB,MAAMS,GAAsBH,EAAAA,EAAAA,IAAkB,YACxCC,GAAuBD,EAAAA,EAAAA,IAAkB,aACzCmG,GAAuBnG,EAAAA,EAAAA,IAAkB,aAE/C,OAAQW,EAAAA,EAAAA,OCtGRK,EAAAA,EAAAA,IAyBYmF,EAAA,CD8EVlF,WCtGSkE,EAAAf,MDuGT,sBAAuB1E,EAAO,KAAOA,EAAO,GAAMyB,GCvGzCgE,EAAOf,MAAAjD,GACfmD,MAAO7E,EAAA6E,MACRrB,MAAM,MACL,eAAc+C,EACf,wBDwGCI,EAAAA,EAAAA,IAAa,CACdnE,SAASC,EAAAA,EAAAA,ICvGT,IAUM,EAVNrB,EAAAA,EAAAA,IAUM,MAVNjC,EAUM,EATJiD,EAAAA,EAAAA,IAQE1B,EAAA,CDiGEc,WCxGOoE,EAAAjB,MDyGP,sBAAuB1E,EAAO,KAAOA,EAAO,GAAMyB,GCzG3CkE,EAAYjB,MAAAjD,GACrBW,KAAK,WACJuE,KAAM,GACN9C,SAAU9D,EAAA8D,SACVhC,YAAa9B,EAAA8D,SAAW,GAAK,YAC9B/B,MAAA,2CACC8E,QAAOL,GD0GL,KAAM,EAAG,CAAC,aAAc,WAAY,oBAG3C7D,EAAG,GACF,CC1GwB3C,EAAA8D,cDqIrBiC,EAzBA,CACEnC,KC7GK,SD8GLkD,IAAIrE,EAAAA,EAAAA,IC7GR,IAGM,EAHNrB,EAAAA,EAAAA,IAGM,MAHN/B,EAGM,EAFJ+C,EAAAA,EAAAA,IAA8C5B,EAAA,CAAlC8B,QAAOiE,GAAW,CD+GtB/D,SAASC,EAAAA,EAAAA,IC/Ge,IAAExC,EAAA,KAAAA,EAAA,KDgHxByC,EAAAA,EAAAA,IChHsB,SDkHxBC,EAAG,EACHC,GAAI,CAAC,MClHbR,EAAAA,EAAAA,IAA+D5B,EAAA,CAApD6B,KAAK,UAAWC,QAAOmE,GDuHzB,CACDjE,SAASC,EAAAA,EAAAA,ICxHgC,IAAExC,EAAA,KAAAA,EAAA,KDyHzCyC,EAAAA,EAAAA,ICzHuC,SD2HzCC,EAAG,EACHC,GAAI,CAAC,SAIXlD,IAAK,OAGT,KAAM,CAAC,aAAc,UAE3B,I,UEtJA,MAAMqH,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,QCLA,MAAM5H,EAAa,CAAEC,MAAO,kBACtBC,EAAa,CAAED,MAAO,kBACtBE,EAAa,CAAEF,MAAO,kBACtBG,EAAa,CAAEH,MAAO,cACtBI,EAAa,CAAC,SACdC,EAAa,CAAEL,MAAO,iBAgB5B,OAA4B6F,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,mBACRC,MAAO,CACL3D,WAAY,CAAEa,KAAM+C,SACpB4B,WAAY,CAAC,EACbC,aAAc,CAAC,GAEjB5B,MAAO,CAAC,oBAAqB,gBAC7BC,KAAAA,CAAMC,GAAgBC,KAAMC,IC8C9B,MAAMN,EAAQI,EAGRC,EAAOC,EAMPC,GAAUC,EAAAA,EAAAA,KAAI,GACduB,GAAavB,EAAAA,EAAAA,IAAI,IACjBwB,GAAcxB,EAAAA,EAAAA,IAAI,IAClByB,GAAiBzB,EAAAA,EAAAA,IAAmB,IACpC0B,GAA0B1B,EAAAA,EAAAA,KAAI,GAC9B2B,GAAoB3B,EAAAA,EAAAA,IAAS,IAC7B4B,GAAa5B,EAAAA,EAAAA,MAGb6B,GAAU1B,EAAAA,EAAAA,IAAS,KACvB,MAAM2B,EAAO,IAAIC,IAAIvC,EAAM8B,aAAaU,IAAIC,GAAKA,EAAEC,UACnD,OAAOC,MAAMC,KAAKN,GAAMO,SAGpBC,GAAcnC,EAAAA,EAAAA,IAAS,KAC3B,IAAKX,EAAM6B,WACT,MAAO,SAGT,MAAMkB,EAAW/C,EAAM6B,WAAWkB,SAClC,MAAO,YAAYA,MAGfC,GAAiBrC,EAAAA,EAAAA,IAAS,KAC9B,IAAIsC,EAASjD,EAAM8B,aAQnB,GALIE,EAAYxC,QACdyD,EAASA,EAAOC,OAAOT,GAAKA,EAAEC,UAAYV,EAAYxC,QAIpDuC,EAAWvC,MAAO,CACpB,MAAM2D,EAASpB,EAAWvC,MAAM4D,cAChCH,EAASA,EAAOC,OAAOT,GACrBA,EAAEhE,KAAK2E,cAAcC,SAASF,IAC9BV,EAAEa,YAAYF,cAAcC,SAASF,G,CAIzC,OAAOF,KAIT/B,EAAAA,EAAAA,IAAM,IAAMlB,EAAM3D,WAAa8E,IAC7BZ,EAAQf,MAAQ2B,EACZA,IAEFY,EAAWvC,MAAQ,GACnByC,EAAezC,MAAQ,GAEnB6C,EAAQ7C,MAAMrD,OAAS,IACzB6F,EAAYxC,MAAQ6C,EAAQ7C,MAAM,IAElC+D,EAAAA,EAAAA,IAAS,KACPC,WAMRtC,EAAAA,EAAAA,IAAMX,EAAUY,IACdd,EAAK,oBAAqBc,MAG5BD,EAAAA,EAAAA,IAAMc,EAAa,MAEjBuB,EAAAA,EAAAA,IAAS,KACPC,QAKJ,MAAMC,EAAkBA,KAEtBD,KAGIA,EAA4BA,KAEhC,GAAIpB,EAAW5C,MAAO,CACpB,MAAMkE,EAAmBV,EAAexD,MACxCkE,EAAiBC,QAAQnF,IACvB4D,EAAW5C,MAAMoE,mBAAmBpF,GAAK,I,GAKzC4C,EAAcA,KAClBb,EAAQf,OAAQ,GAGZqE,EAAyBC,IAC7B7B,EAAezC,MAAQsE,GAYnBC,EAAevE,GACL,OAAVA,QAA4BoB,IAAVpB,EACb,GAGFyB,OAAOzB,GAGVwE,EAAkBC,IACtB9B,EAAkB3C,MAAQyE,EAAMzE,MAChC0C,EAAwB1C,OAAQ,GAG5B0E,EAAsBA,KACU,IAAhCjC,EAAezC,MAAMrD,QAKzBkE,EAAK,eAAgB4B,EAAezC,OACpC2E,EAAAA,GAAUC,QAAQ,QAAQnC,EAAezC,MAAMrD,cAC/CiF,KANE+C,EAAAA,GAAUE,QAAQ,eDtCtB,MAAO,CAACxJ,EAAUC,KAChB,MAAMwJ,GAAuBlJ,EAAAA,EAAAA,IAAkB,aACzCmJ,GAAuBnJ,EAAAA,EAAAA,IAAkB,aACzCM,GAAqBN,EAAAA,EAAAA,IAAkB,WACvCG,GAAsBH,EAAAA,EAAAA,IAAkB,YACxCI,GAA6BJ,EAAAA,EAAAA,IAAkB,mBAC/CC,GAAuBD,EAAAA,EAAAA,IAAkB,aACzCO,GAAsBP,EAAAA,EAAAA,IAAkB,YACxCmG,GAAuBnG,EAAAA,EAAAA,IAAkB,aAE/C,OAAQW,EAAAA,EAAAA,OClLRK,EAAAA,EAAAA,IA4DYmF,EAAA,CDuHVlF,WCnLkBkE,EAAAf,MDoLlB,sBAAuB1E,EAAO,KAAOA,EAAO,GAAMyB,GCpLhCgE,EAAOf,MAAAjD,GAAGmD,MAAOoD,EAAAtD,MAAanB,MAAM,MAAO,eAAc+C,EAAa,uBDyLvF,CCxIUoD,QAAMlH,EAAAA,EAAAA,IACf,IAKM,EALNrB,EAAAA,EAAAA,IAKM,MALN3B,EAKM,EAJJ2C,EAAAA,EAAAA,IAA8C5B,EAAA,CAAlC8B,QAAOiE,GAAW,CD0I5B/D,SAASC,EAAAA,EAAAA,IC1IqB,IAAExC,EAAA,KAAAA,EAAA,KD2I9ByC,EAAAA,EAAAA,IC3I4B,SD6I9BC,EAAG,EACHC,GAAI,CAAC,MC7IPR,EAAAA,EAAAA,IAEY5B,EAAA,CAFD6B,KAAK,UAAWC,QAAO+G,EAAsBO,SAAoC,IAA1BxC,EAAAzC,MAAerD,QDmJ9E,CACDkB,SAASC,EAAAA,EAAAA,ICpJoF,IACvF,EDoJJC,EAAAA,EAAAA,ICrJ2F,WACvFQ,EAAAA,EAAAA,IAAGkE,EAAAzC,MAAerD,QAAS,KACnC,KDqJEqB,EAAG,GACF,EAAG,CAAC,iBAGXH,SAASC,EAAAA,EAAAA,IC9MT,IA8CM,EA9CNrB,EAAAA,EAAAA,IA8CM,MA9CNjC,EA8CM,EA5CJiC,EAAAA,EAAAA,IAiBM,MAjBN/B,EAiBM,EAhBJ+C,EAAAA,EAAAA,IAGYsH,EAAA,CD4MRlI,WC/MgB2F,EAAAxC,MDgNhB,sBAAuB1E,EAAO,KAAOA,EAAO,GAAMyB,GChNlCyF,EAAWxC,MAAAjD,GAAEC,KAAK,QAAQG,YAAY,SAASC,MAAA,gBAChEC,SAAQ4G,GDoNN,CACDpG,SAASC,EAAAA,EAAAA,ICpNA,IAAsB,GDqN5BvB,EAAAA,EAAAA,KAAW,ICrNhBC,EAAAA,EAAAA,IAAyE0I,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAAhDtC,EAAA7C,MAAPoF,KDsNJ7I,EAAAA,EAAAA,OCtNdK,EAAAA,EAAAA,IAAyEkI,EAAA,CAAtC/J,IAAKqK,EAAMxG,MAAOwG,EAAMpF,MAAOoF,GD0NzD,KAAM,EAAG,CAAC,QAAS,YACpB,QAENpH,EAAG,GACF,EAAG,CAAC,gBC3NTP,EAAAA,EAAAA,IAMW1B,EAAA,CDuNPc,WC7Ne0F,EAAAvC,MD8Nf,sBAAuB1E,EAAO,KAAOA,EAAO,GAAMyB,GC9NnCwF,EAAUvC,MAAAjD,GAAEI,YAAY,YAAYI,UAAA,GAAUP,KAAK,QAAQI,MAAA,iBDmOzE,CClOQiI,QAAMvH,EAAAA,EAAAA,IACf,IAEU,EAFVL,EAAAA,EAAAA,IAEUvB,EAAA,MDkON2B,SAASC,EAAAA,EAAAA,ICnOX,IAAU,EAAVL,EAAAA,EAAAA,KAAU6H,EAAAA,EAAAA,IAAAC,EAAAA,WDsORvH,EAAG,MAGPA,EAAG,GACF,EAAG,CAAC,gBCrOTvB,EAAAA,EAAAA,IAEM,MAFN9B,EAA4B,SACtB4D,EAAAA,EAAAA,IAAGkE,EAAAzC,MAAerD,QAAS,KAAC4B,EAAAA,EAAAA,IAAGiF,EAAAxD,MAAerD,QAAS,QAC7D,MAIFF,EAAAA,EAAAA,IAqBM,MArBN7B,EAqBM,EApBJ6C,EAAAA,EAAAA,IAmBWtB,EAAA,CDgNPqJ,QCnOU,aAAJxE,IAAI4B,EAAcnE,KAAM+E,EAAAxD,MAAgByF,OAAO,MAAOC,kBAAkBrB,EAChF,UAAQ,OAAOrH,KAAK,SDyOjB,CACDa,SAASC,EAAAA,EAAAA,ICzOX,IAA+C,EAA/CL,EAAAA,EAAAA,IAA+CzB,EAAA,CAA9B0B,KAAK,YAAYmB,MAAM,QACxCpB,EAAAA,EAAAA,IAA6EzB,EAAA,CAA5D2C,KAAK,OAAOC,MAAM,MAAMC,MAAM,MAAM,8BACrDpB,EAAAA,EAAAA,IAAmFzB,EAAA,CAAlE2C,KAAK,YAAYC,MAAM,OAAOC,MAAM,MAAM,8BAC3DpB,EAAAA,EAAAA,IAMkBzB,EAAA,CAND2C,KAAK,QAAQC,MAAM,MAAM,YAAU,OD2P7C,CC1PMf,SAAOC,EAAAA,EAAAA,IAChB,EADoBkB,SAAG,EACvBvC,EAAAA,EAAAA,IAEO,QAFDhC,MAAM,mBAAoByF,MAAOqE,EAAYvF,EAAIgB,SD8PhDzB,EAAAA,EAAAA,IC7PFgG,EAAYvF,EAAIgB,QAAK,EAAAnF,KD+PxBmD,EAAG,KC3PTP,EAAAA,EAAAA,IAMkBzB,EAAA,CAND4C,MAAM,KAAKC,MAAM,KAAKQ,MAAM,UDiQtC,CChQMxB,SAAOC,EAAAA,EAAAA,IAChB,EADoBkB,SAAG,EACvBvB,EAAAA,EAAAA,IAEY5B,EAAA,CAFD6B,KAAK,OAAOV,KAAK,QAASW,QAAKZ,GAAEyH,EAAexF,IDqQpD,CACDnB,SAASC,EAAAA,EAAAA,ICtQkD,IAEjExC,EAAA,KAAAA,EAAA,KDqQQyC,EAAAA,EAAAA,ICvQyD,WDyQ3DC,EAAG,EACHC,GAAI,CAAC,IACJ,KAAM,CAAC,cAEZD,EAAG,MAGPA,EAAG,GACF,EAAG,CAAC,cC5PbP,EAAAA,EAAAA,IAA8GkI,EAAA,CDgQ1G9I,WChQuB6F,EAAA1C,MDiQvB,sBAAuB1E,EAAO,KAAOA,EAAO,GAAMyB,GCjQ3B2F,EAAuB1C,MAAAjD,GAAGiD,MAAO2C,EAAA3C,MAAoBb,UAAU,EAAMe,MAAM,ODqQjG,KAAM,EAAG,CAAC,aAAc,YAE7BlC,EAAG,GACF,EAAG,CAAC,aAAc,UAEvB,IEhUA,MAAM,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,QCNA,MAAMxD,EAAa,CCDZC,MAAM,sBDEPC,EAAa,CCDVD,MAAM,kBDETE,EAAa,CCARF,MAAM,kBDCXG,EAAa,CCKVH,MAAM,oBDJTI,EAAa,CAAC,SACdC,EAAa,CCcCL,MAAM,QDbpBO,EAAa,CC0BFP,MAAM,kBDxBjB,SAAUW,EAAOC,EAAUC,EAAYC,EAAYC,EAAYC,EAAWC,GAC9E,MAAMG,GAAuBD,EAAAA,EAAAA,IAAkB,aACzCgK,GAAoBhK,EAAAA,EAAAA,IAAkB,UACtCI,GAA6BJ,EAAAA,EAAAA,IAAkB,mBAC/CO,GAAsBP,EAAAA,EAAAA,IAAkB,YAE9C,OAAQW,EAAAA,EAAAA,OCfRC,EAAAA,EAAAA,IAwEM,MAxENhC,EAwEM,EAvEJiC,EAAAA,EAAAA,IAMM,MANN/B,EAMM,CDUJY,EAAO,KAAOA,EAAO,ICfrBmB,EAAAA,EAAAA,IAA8B,UAA1B,yBAAqB,KACzBA,EAAAA,EAAAA,IAGM,MAHN9B,EAGM,EAFJ8C,EAAAA,EAAAA,IAAgF5B,EAAA,CAArEmB,KAAK,QAAQU,KAAK,UAAWC,QAAOtC,EAAAwK,iBDmB5C,CACDhI,SAASC,EAAAA,EAAAA,ICpBqD,IAAIxC,EAAA,KAAAA,EAAA,KDqBhEyC,EAAAA,EAAAA,ICrB4D,WDuB9DC,EAAG,EACHC,GAAI,CAAC,IACJ,EAAG,CAAC,YCxBgE5C,EAAAyK,UAAUnJ,OAAS,ID0BrFJ,EAAAA,EAAAA,OC1BLK,EAAAA,EAAAA,IAA2Gf,EAAA,CD2BrGd,IAAK,EC3BAiC,KAAK,QAAQU,KAAK,SAAUC,QAAOtC,EAAA0K,mBD+BvC,CACDlI,SAASC,EAAAA,EAAAA,IChC8E,IAAExC,EAAA,KAAAA,EAAA,KDiCvFyC,EAAAA,EAAAA,ICjCqF,SDmCvFC,EAAG,EACHC,GAAI,CAAC,IACJ,EAAG,CAAC,cACPT,EAAAA,EAAAA,IAAoB,IAAI,QClChCf,EAAAA,EAAAA,IA8DM,MA9DN7B,EA8DM,EA7DJ6C,EAAAA,EAAAA,IA4DWtB,EAAA,CA5DAsC,KAAMpD,EAAAyK,UAAWpH,OAAA,GAAOtB,MAAA,eAAoBJ,KAAK,SD0CzD,CACDa,SAASC,EAAAA,EAAAA,IC1CT,IAIkB,EAJlBL,EAAAA,EAAAA,IAIkBzB,EAAA,CAJD4C,MAAM,KAAKC,MAAM,MD8C7B,CC7CQhB,SAAOC,EAAAA,EAAAA,IACmGgB,GAD5F,EACvBrB,EAAAA,EAAAA,IAAmHmI,EAAA,CAA1GlI,KAAMrC,EAAA2K,mBAAmBlH,EAAME,IAAIiH,UAAWjJ,KAAK,SDiDvD,CACDa,SAASC,EAAAA,EAAAA,IClDuD,IAAsC,EDmDpGC,EAAAA,EAAAA,KAAiBQ,EAAAA,EAAAA,ICnDgDO,EAAME,IAAIiH,SAASC,eAAW,KDqDjGlI,EAAG,GACF,KAAM,CAAC,WAEZA,EAAG,KCpDPP,EAAAA,EAAAA,IAMkBzB,EAAA,CAND4C,MAAM,MAAM,YAAU,ODyDlC,CCxDQf,SAAOC,EAAAA,EAAAA,IAGVgB,GAHiB,EACvBrC,EAAAA,EAAAA,IAEM,OAFDhC,MAAM,YAAayF,MAAOpB,EAAME,IAAImH,MD4DpC,EC3DH1J,EAAAA,EAAAA,IAAkD,OAAlD3B,GAAkDyD,EAAAA,EAAAA,IAA5BO,EAAME,IAAIuE,UAAQ,ID6DrC,EAAG1I,KAERmD,EAAG,KC1DPP,EAAAA,EAAAA,IAIkBzB,EAAA,CAJD4C,MAAM,KAAKC,MAAM,OD+D7B,CC9DQhB,SAAOC,EAAAA,EAAAA,IAC8FgB,GADvF,EACvBrB,EAAAA,EAAAA,IAA8GmI,EAAA,CAArGlI,KAAMrC,EAAA+K,iBAAiBtH,EAAME,IAAIqH,QAASrJ,KAAK,SDkEnD,CACDa,SAASC,EAAAA,EAAAA,ICnEmD,IAAqC,EDoE/FC,EAAAA,EAAAA,KAAiBQ,EAAAA,EAAAA,ICpE4ClD,EAAAiL,cAAcxH,EAAME,IAAIqH,SAAM,KDsE7FrI,EAAG,GACF,KAAM,CAAC,WAEZA,EAAG,KCrEPP,EAAAA,EAAAA,IAsCkBzB,EAAA,CAtCD4C,MAAM,KAAKC,MAAM,OD0E7B,CCzEQhB,SAAOC,EAAAA,EAAAA,IAmCVgB,GAnCiB,EACvBrC,EAAAA,EAAAA,IAkCM,MAlCNzB,EAkCM,CAhCI8D,EAAME,IAAIqH,SAAWhL,EAAAkL,iBAAiBC,SAAW1H,EAAME,IAAIqH,SAAWhL,EAAAkL,iBAAiBE,QD0ExFlK,EAAAA,EAAAA,OC3EPK,EAAAA,EAAAA,IAOYf,EAAA,CDqEJd,IAAK,EC1EX2C,KAAK,UACLV,KAAK,QACJW,QAAKZ,GAAE1B,EAAAqL,cAAc5H,EAAME,IAAI2H,KD4EzB,CACD9I,SAASC,EAAAA,EAAAA,IC5EhB,IAEDxC,EAAA,KAAAA,EAAA,KD2EUyC,EAAAA,EAAAA,IC7ET,aD+EOC,EAAG,EACHC,GAAI,CAAC,IACJ,KAAM,CAAC,cACVT,EAAAA,EAAAA,IAAoB,IAAI,GC7EtBsB,EAAME,IAAIqH,SAAWhL,EAAAkL,iBAAiBK,SD+EvCrK,EAAAA,EAAAA,OChFPK,EAAAA,EAAAA,IAOYf,EAAA,CD0EJd,IAAK,EC/EX2C,KAAK,UACLV,KAAK,QACJW,QAAKZ,GAAE1B,EAAAwL,oBAAoB/H,EAAME,MDiF3B,CACDnB,SAASC,EAAAA,EAAAA,ICjFhB,IAEDxC,EAAA,KAAAA,EAAA,KDgFUyC,EAAAA,EAAAA,IClFT,aDoFOC,EAAG,EACHC,GAAI,CAAC,IACJ,KAAM,CAAC,cACVT,EAAAA,EAAAA,IAAoB,IAAI,ICnF9BC,EAAAA,EAAAA,IAMY5B,EAAA,CALV6B,KAAK,UACLV,KAAK,QACJW,QAAKZ,GAAE1B,EAAAyL,mBAAmBhI,EAAME,IAAImH,ODqFlC,CACDtI,SAASC,EAAAA,EAAAA,ICrFZ,IAEDxC,EAAA,KAAAA,EAAA,KDoFMyC,EAAAA,EAAAA,ICtFL,cDwFGC,EAAG,EACHC,GAAI,CAAC,IACJ,KAAM,CAAC,aCtFZR,EAAAA,EAAAA,IAMY5B,EAAA,CALV6B,KAAK,SACLV,KAAK,QACJW,QAAKZ,GAAE1B,EAAA0L,eAAejI,EAAME,IAAI2H,KDwF9B,CACD9I,SAASC,EAAAA,EAAAA,ICxFZ,IAEDxC,EAAA,KAAAA,EAAA,KDuFMyC,EAAAA,EAAAA,ICzFL,WD2FGC,EAAG,EACHC,GAAI,CAAC,IACJ,KAAM,CAAC,gBAGdD,EAAG,MAGPA,EAAG,GACF,EAAG,CAAC,YAGb,C,sBClFA,GAAegJ,EAAAA,EAAAA,IAAgB,CAC7B/H,KAAM,kBACNyB,MAAO,CAAC,gBACRC,KAAAA,CAAMH,GAAO,KAAEK,IAEb,MAAMiF,GAAY9E,EAAAA,EAAAA,IAAkB,IAG9BiG,EAAgBC,UACpB,IACE,MAAMC,QAAiBC,EAAAA,GAAOC,aAAaC,iBAC3CxB,EAAU9F,MAAQmH,EAAS1I,I,CAC3B,MAAO8I,GACPC,QAAQD,MAAM,YAAaA,GAC3B5C,EAAAA,GAAU4C,MAAM,W,GAId1B,EAAkBqB,UACtB,IACE,MAAMC,QAAiBC,EAAAA,GAAOC,aAAaI,oBACrCC,EAAYP,EAAS1I,KAEvBiJ,GAAaA,EAAU/K,OAAS,UAC5BgL,EAAaD,GACnB/C,EAAAA,GAAUC,QAAQ,QAAQ8C,EAAU/K,kB,CAEtC,MAAO4K,GACPC,QAAQD,MAAM,UAAWA,GACzB5C,EAAAA,GAAU4C,MAAM,S,GAIdI,EAAeT,UACnB,IACE,MAAMC,QAAiBC,EAAAA,GAAOC,aAAaO,eAAe,CAAEF,cACtDG,EAAWV,EAAS1I,KAY1B,OATAoJ,EAAS1D,QAAQ2D,IACf,MAAMC,EAAgBjC,EAAU9F,MAAMgI,UAAUC,GAAKA,EAAEtB,KAAOmB,EAAKnB,IAC/DoB,GAAiB,EACnBjC,EAAU9F,MAAM+H,GAAiBD,EAEjChC,EAAU9F,MAAMkI,KAAKJ,KAIlBD,C,CACP,MAAON,GAEP,MADAC,QAAQD,MAAM,UAAWA,GACnBA,C,GAIJb,EAAgBQ,UACpB,IAEE,MAAMY,EAAOhC,EAAU9F,MAAMmI,KAAKF,GAAKA,EAAEtB,KAAOyB,GAC5CN,IACFA,EAAKzB,OAASE,EAAAA,GAAiB8B,SAGjC,MAAMlB,QAAiBC,EAAAA,GAAOC,aAAaiB,gBAAgB,CACzDF,WAGIG,EAAcpB,EAAS1I,KAGvB+J,EAAQ1C,EAAU9F,MAAMgI,UAAUC,GAAKA,EAAEtB,KAAOyB,GAClDI,GAAS,IACX1C,EAAU9F,MAAMwI,GAASD,GAG3B5D,EAAAA,GAAUC,QAAQ,eAAe2D,EAAYjG,cAAc3F,QAAU,SAGjE4L,EAAYjG,cAAgBiG,EAAYjG,aAAa3F,OAAS,GAChEkE,EAAK,eAAgB0H,EAAaA,EAAYjG,a,CAEhD,MAAOiF,GACPC,QAAQD,MAAM,UAAWA,GACzB5C,EAAAA,GAAU4C,MAAM,UAGhB,MAAMO,EAAOhC,EAAU9F,MAAMmI,KAAKF,GAAKA,EAAEtB,KAAOyB,GAC5CN,IACFA,EAAKzB,OAASE,EAAAA,GAAiBE,M,GAK/BI,EAAuBiB,IACvBA,EAAKxF,cAAgBwF,EAAKxF,aAAa3F,OAAS,EAClDkE,EAAK,eAAgBiH,EAAMA,EAAKxF,cAEhCqC,EAAAA,GAAUE,QAAQ,cAIhBiC,EAAqBI,UACzB,UACQE,EAAAA,GAAOqB,SAASC,aAAaC,E,CACnC,MAAOpB,GACPC,QAAQD,MAAM,WAAYA,GAC1B5C,EAAAA,GAAU4C,MAAM,U,GAIdR,EAAiBG,UACrB,UAEQE,EAAAA,GAAOC,aAAauB,iBAAiB,CAAER,WAG7C,MAAMS,EAAY/C,EAAU9F,MAAMgI,UAAUC,GAAKA,EAAEtB,KAAOyB,GACtDS,GAAa,IACf/C,EAAU9F,MAAM8I,OAAOD,EAAW,GAClClE,EAAAA,GAAUC,QAAQ,W,CAEpB,MAAO2C,GACPC,QAAQD,MAAM,UAAWA,GACzB5C,EAAAA,GAAU4C,MAAM,S,GAIdxB,EAAoBmB,UACxB,UACQ6B,EAAAA,EAAaC,QAAQ,kBAAmB,KAAM,CAClDC,kBAAmB,KACnBC,iBAAkB,KAClBxL,KAAM,kBAIF0J,EAAAA,GAAOC,aAAa8B,mBAG1BrD,EAAU9F,MAAQ,GAClB2E,EAAAA,GAAUC,QAAQ,c,CAClB,MAAO2C,GACO,WAAVA,IACFC,QAAQD,MAAM,UAAWA,GACzB5C,EAAAA,GAAU4C,MAAM,U,GAMhBvB,EAAsBC,IAC1B,OAAQA,GACN,KAAKmD,EAAAA,GAAeC,MAAO,MAAO,UAClC,KAAKD,EAAAA,GAAeE,KAAM,MAAO,UACjC,KAAKF,EAAAA,GAAeG,IAAK,MAAO,UAChC,QAAS,MAAO,KAIdnD,EAAoBC,IACxB,OAAQA,GACN,KAAKE,EAAAA,GAAiBC,QAAS,MAAO,GACtC,KAAKD,EAAAA,GAAiB8B,QAAS,MAAO,UACtC,KAAK9B,EAAAA,GAAiBK,OAAQ,MAAO,UACrC,KAAKL,EAAAA,GAAiBE,MAAO,MAAO,SACpC,QAAS,MAAO,KAIdH,EAAiBD,IACrB,OAAQA,GACN,KAAKE,EAAAA,GAAiBC,QAAS,MAAO,MACtC,KAAKD,EAAAA,GAAiB8B,QAAS,MAAO,MACtC,KAAK9B,EAAAA,GAAiBK,OAAQ,MAAO,MACrC,KAAKL,EAAAA,GAAiBE,MAAO,MAAO,OACpC,QAAS,OAAOJ,IAKdmD,EAAmBA,KACvBvC,KAQF,OAJAwC,EAAAA,EAAAA,IAAU,KACRxC,MAGK,CACLnB,YACAD,kBACAa,gBACAG,sBACAC,qBACAC,iBACAhB,oBACAC,qBACAI,mBACAE,gBACAkD,mBAEAjD,iBAAgBA,EAAAA,GAEpB,IC3RF,MAAM,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAAS,GAAQ,CAAC,YAAY,qBAEzF,QToGA,GAAeS,EAAAA,EAAAA,IAAgB,CAC7B/H,KAAM,oBACNyK,WAAY,CACVC,iBAAgB,EAChBC,gBAAe,EACfjE,iBAAgB,EAChBkE,SAAQA,EAAAA,UAEVlJ,KAAAA,GAEE,MAAMmJ,GAAY9I,EAAAA,EAAAA,IAAmB,IAC/B+I,GAAa/I,EAAAA,EAAAA,IAAY,YACzBgJ,GAAmBhJ,EAAAA,EAAAA,IAAY,IAC/BiJ,GAAqBjJ,EAAAA,EAAAA,IAAY,IACjClE,GAAuBkE,EAAAA,EAAAA,IAAc,IACrC2H,GAAW3H,EAAAA,EAAAA,IAAY,IACvB3C,GAAY2C,EAAAA,EAAAA,IAAoB,IAChCjC,GAAkBiC,EAAAA,EAAAA,IAA+B,CAAC,GAClDkJ,GAAiBlJ,EAAAA,EAAAA,IAAY,IAC7B1C,GAAgB0C,EAAAA,EAAAA,IAAY,IAG5BmJ,GAAUnJ,EAAAA,EAAAA,KAAI,GACd5C,GAAa4C,EAAAA,EAAAA,KAAI,GAGjBtB,GAAqBsB,EAAAA,EAAAA,KAAI,GACzBrB,GAAmBqB,EAAAA,EAAAA,IAAuB,MAC1CpB,GAAsBoB,EAAAA,EAAAA,IAAmB,IACzCoJ,GAAmBpJ,EAAAA,EAAAA,IAA+B,CAAC,GAGnDqJ,GAAiBrJ,EAAAA,EAAAA,IAAiB,IAAI+B,KAGtChD,GAA4BiB,EAAAA,EAAAA,KAAI,GAChCf,GAA2Be,EAAAA,EAAAA,IAAY,IACvCsJ,GAA0BtJ,EAAAA,EAAAA,IAAY,IACtCb,GAAoBgB,EAAAA,EAAAA,IAAS,IAAM,UAAUmJ,EAAwBtK,SAGrEtD,GAAayE,EAAAA,EAAAA,IAAS,KAC1B,MAAMoJ,EAAc,IAAIxH,IAAI+G,EAAU9J,MAAMgD,IAAIwH,GAAKA,EAAEC,WACvD,OAAOtH,MAAMC,KAAKmH,GAAa7G,OAAOgH,GAAKA,KAGvCxN,GAAkBiE,EAAAA,EAAAA,IAAS,IACxBzE,EAAWsD,MAAMgD,IAAIyH,IAAO,CACjCzK,MAAOyK,EACP7L,MAAO6L,EACPE,SAAUb,EAAU9J,MACjB0D,OAAO8G,GAAKA,EAAEC,WAAaA,GAC3BzH,IAAI4H,IAAO,CACV5K,MAAO4K,EAASjE,GAChB/H,MAAOgM,EAAS3L,YAKlB4L,GAAoB1J,EAAAA,EAAAA,IAAS,IAC5B6I,EAAiBhK,MACf8J,EAAU9J,MAAM0D,OAAO8G,GAAKA,EAAEC,WAAaT,EAAiBhK,OAD/B,IAIhCxB,GAAoB2C,EAAAA,EAAAA,IAAS,KACjC,IAAK7C,EAAc0B,MACjB,OAAO3B,EAAU2B,MAEnB,MAAM8K,EAAUxM,EAAc0B,MAAM4D,cACpC,OAAOvF,EAAU2B,MAAM0D,OAAOqH,GAC5BA,EAAE9L,KAAK2E,cAAcC,SAASiH,MAI5BE,GAAsB7J,EAAAA,EAAAA,IAAS,IAAMkJ,EAAerK,MAAMhD,MAE1DiO,GAAoB9J,EAAAA,EAAAA,IAAS,IAAMkJ,EAAerK,MAAMhD,KAAO,GAG/DkO,EAAgBhE,UACpB,IACE,MAAMC,QAAiBC,EAAAA,GAAOC,aAAa8D,eAC3CrB,EAAU9J,MAAQmH,EAAS1I,KAGvB/B,EAAWsD,MAAMrD,OAAS,IAC5BqN,EAAiBhK,MAAQtD,EAAWsD,MAAM,GAC1CoL,I,CAEF,MAAO7D,GACPC,QAAQD,MAAM,UAAWA,E,GAIvB8D,EAAqBA,KAEzBhN,EAAU2B,MAAQ,GAClBjB,EAAgBiB,MAAQ,CAAC,EACzBkK,EAAelK,MAAQ,GACvBqK,EAAerK,MAAMsL,SAGjBF,EAAmBA,KACnBP,EAAkB7K,MAAMrD,OAAS,EACnCsN,EAAmBjK,MAAQ6K,EAAkB7K,MAAM,GAAG2G,GAEtDsD,EAAmBjK,MAAQ,IAIzBpC,EAAkBsJ,UACtB,IACE,MAAMC,QAAiBC,EAAAA,GAAOC,aAAakE,aAC3C,IAAKpE,EAAS1I,KACZ,OAIFwL,EAAmBjK,MAAQ,GAC3BgK,EAAiBhK,MAAQ,GACzBlD,EAAqBkD,MAAQ,GAE7B2I,EAAS3I,MAAQmH,EAAS1I,WAEpB+M,G,CACN,MAAOjE,GACPC,QAAQD,MAAM,UAAWA,E,GAIvBiE,EAAoBtE,UACxB,GAAKyB,EAAS3I,MAAd,CAEAmK,EAAQnK,OAAQ,EAChB,IACE,MAAMyL,EAAoC,CACxC1B,WAAY,OACZpB,SAAUA,EAAS3I,OAGfmH,QAAiBC,EAAAA,GAAOC,aAAaqE,UAAUD,GAC/CE,EAASxE,EAAS1I,KAExBJ,EAAU2B,MAAQ2L,EAAOtN,UACzB6L,EAAelK,MAAQ2L,EAAOzB,eAG9BnL,EAAgBiB,MAAQ,CAAC,EACzB2L,EAAOtN,UAAU8F,QAAQyH,IACvB7M,EAAgBiB,MAAM4L,EAAS3M,MAAQ2M,EAAS5L,OAAS,KAG3DqK,EAAerK,MAAMsL,QACrB3G,EAAAA,GAAUC,QAAQ,QAAQ+G,EAAOtN,UAAU1B,a,CAC3C,MAAO4K,GACPC,QAAQD,MAAM,UAAWA,GACzB5C,EAAAA,GAAU4C,MAAM,S,CAClB,QACE4C,EAAQnK,OAAQ,C,CA3BS,GA+BvB1C,EAAwB0C,IACxBA,GAA0B,IAAjBA,EAAMrD,SACjBqN,EAAiBhK,MAAQA,EAAM,GAC/BiK,EAAmBjK,MAAQA,EAAM,GACjC6L,MAIEC,EAA0BrB,GACvBX,EAAU9J,MAAM0D,OAAO8G,GAAKA,EAAEC,WAAaA,GAG9CoB,EAAuB3E,UAC3B,GAAK+C,EAAmBjK,MAAxB,CAEAmK,EAAQnK,OAAQ,EAChB,IACE,MAAMyL,EAAoC,CACxC1B,WAAY,WACZgC,WAAY9B,EAAmBjK,MAC/B2I,SAAU,IAGNxB,QAAiBC,EAAAA,GAAOC,aAAaqE,UAAUD,GAC/CE,EAASxE,EAAS1I,KAExBJ,EAAU2B,MAAQ2L,EAAOtN,UACzB6L,EAAelK,MAAQ2L,EAAOzB,eAG9BnL,EAAgBiB,MAAQ,CAAC,EACzB2L,EAAOtN,UAAU8F,QAAQyH,IACvB7M,EAAgBiB,MAAM4L,EAAS3M,MAAQ2M,EAAS5L,OAAS,KAG3DqK,EAAerK,MAAMsL,QACrB3G,EAAAA,GAAUC,QAAQ,UAAU+G,EAAOtN,UAAU1B,a,CAC7C,MAAO4K,GACPC,QAAQD,MAAM,UAAWA,GACzB5C,EAAAA,GAAU4C,MAAM,S,CAClB,QACE4C,EAAQnK,OAAQ,C,CA5BmB,GAgCjCd,EAAqB8M,IACzB3B,EAAerK,MAAMiM,IAAID,IAGrB9N,EAAgBgJ,UACpB9I,EAAW4B,OAAQ,EACnB,IACE,MAAMyL,EAA+B,CACnC1B,WAAYA,EAAW/J,MACvB+L,WAAiC,aAArBhC,EAAW/J,MAAuBiK,EAAmBjK,WAAQoB,EACzEuH,SAA+B,SAArBoB,EAAW/J,MAAmB2I,EAAS3I,MAAQ,GACzDjB,gBAAiBA,EAAgBiB,OAG7BmH,QAAiBC,EAAAA,GAAOC,aAAa6E,YAAYT,GACjDE,EAASxE,EAAS1I,KAExBkG,EAAAA,GAAUC,QAAQ,WAClByF,EAAerK,MAAMsL,cAEflE,EAAAA,GAAOqB,SAASC,aAAaiD,EAAOQ,e,CAE1C,MAAO5E,GACPC,QAAQD,MAAM,UAAWA,GACzB5C,EAAAA,GAAU4C,MAAM,S,CAClB,QACEnJ,EAAW4B,OAAQ,C,GAKjBP,EAAoBA,CAACqI,EAAkBrE,KAC3C9D,EAAiBK,MAAQ8H,EACzBlI,EAAoBI,MAAQyD,EAC5B/D,EAAmBM,OAAQ,GAGvBF,EAAqB2D,IACzBA,EAAOU,QAAQM,IAEb,MAAMmH,EAAWvN,EAAU2B,MAAMmI,KAAK4C,GAAKA,EAAE9L,KAAK2E,gBAAkBa,EAAMxF,KAAK2E,eAC3EgI,IAEF7M,EAAgBiB,MAAM4L,EAAS3M,MAAQwC,OAAOgD,EAAMzE,OAEpDoK,EAAiBpK,MAAM4L,EAAS3M,MAAQwF,EAAM2H,OAE9C/B,EAAerK,MAAMiM,IAAIL,EAAS3M,SAMtC0F,EAAAA,GAAUC,QAAQ,QAAQnB,EAAO9G,eAG7B4C,EAAsByM,GACnB5B,EAAiBpK,MAAMgM,GAI1B5M,EAAsBwM,IAC1B,MAAM5L,EAAQjB,EAAgBiB,MAAM4L,EAAS3M,MAC7C,IAAKe,EAAO,OAAO,EAGnB,MAAMqM,EAAerM,EAAMsM,OAC3B,GAAID,EAAaE,WAAW,MAAQF,EAAaxI,SAAS,KACxD,OAAO,EAIT,GAAIwI,EAAaE,WAAW,MAAQF,EAAaE,WAAW,KAC1D,IAEE,OADAjL,KAAKC,MAAM8K,IACJ,C,CACP,MAEA,OAAOA,EAAaxI,SAAS,MAAQwI,EAAaxI,SAAS,I,CAK/D,MAAMnG,EAAOkO,EAASlO,KAAKkG,cAC3B,OAAOlG,EAAKmG,SAAS,WAAanG,EAAKmG,SAAS,UAAYnG,EAAKmG,SAAS,OAItEvE,EAAwBsM,IAC5BtB,EAAwBtK,MAAQ4L,EAAS3M,KACzCgB,EAAyBD,MAAQjB,EAAgBiB,MAAM4L,EAAS3M,OAAS,GACzEc,EAA0BC,OAAQ,GAI9BK,EAA6BL,IAC7BsK,EAAwBtK,QAC1BjB,EAAgBiB,MAAMsK,EAAwBtK,OAASA,EACvDd,EAAkBoL,EAAwBtK,SAIxCwM,EAAerG,GACdA,GACEA,EAAKsG,MAAM,SAASC,OADT,GAId1G,EAAsBC,IAC1B,OAAQA,GACN,KAAKmD,EAAAA,GAAeC,MAAO,MAAO,UAClC,KAAKD,EAAAA,GAAeE,KAAM,MAAO,UACjC,KAAKF,EAAAA,GAAeG,IAAK,MAAO,UAChC,QAAS,MAAO,KAIdoD,EAAyB7E,IAE7B,IAAI8E,EAAQ,EAMZ,OALAC,OAAOC,KAAK1C,EAAiBpK,OAAOmE,QAAQ6H,IACtC5B,EAAiBpK,MAAMgM,KAAelE,EAAKvE,UAC7CqJ,MAGGA,GAaT,OAJAnD,EAAAA,EAAAA,IAAUvC,gBACFgE,MAGD,CACLpB,YACAC,aACAC,mBACAC,qBACAnN,uBACA6L,WACAtK,YACAU,kBACAmL,iBACA5L,gBACA6L,UACA/L,aACA1B,aACAQ,kBACA2N,oBACArM,oBACAwM,sBACAC,oBACAI,qBACAD,mBACAxN,kBACAN,uBACAwO,yBACAD,uBACA3M,oBACAhB,gBACAsO,cACAxG,qBACA2G,wBAEAjN,qBACAC,mBACAC,sBACAH,oBACAK,oBACAP,qBAEAH,qBACAE,uBACAe,4BACAN,4BACAE,2BACAE,oBAEA0J,SAAQ,WACRtD,iBAAgBA,EAAAA,GAEpB,IU9eF,MAAM,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASnL,GAAQ,CAAC,YAAY,qBAEzF,O", "sources": ["webpack://tab-kit-web/./src/views/ParameterToolView.vue?9169", "webpack://tab-kit-web/./src/views/ParameterToolView.vue", "webpack://tab-kit-web/./src/components/ParamValueViewer.vue?2066", "webpack://tab-kit-web/./src/components/ParamValueViewer.vue", "webpack://tab-kit-web/./src/components/ParamValueViewer.vue?ab66", "webpack://tab-kit-web/./src/components/ParamParseDialog.vue?64b6", "webpack://tab-kit-web/./src/components/ParamParseDialog.vue", "webpack://tab-kit-web/./src/components/ParamParseDialog.vue?7eb6", "webpack://tab-kit-web/./src/components/DataFileManager.vue?eea6", "webpack://tab-kit-web/./src/components/DataFileManager.vue", "webpack://tab-kit-web/./src/components/DataFileManager.vue?b969", "webpack://tab-kit-web/./src/views/ParameterToolView.vue?3f0a"], "sourcesContent": ["import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, createTextVNode as _createTextVNode, withCtx as _withCtx, createVNode as _createVNode, toDisplayString as _toDisplayString, createElementBlock as _createElementBlock } from \"vue\"\n\nconst _hoisted_1 = { class: \"parameter-tool\" }\nconst _hoisted_2 = { class: \"main-content\" }\nconst _hoisted_3 = { class: \"cin-parameters-section\" }\nconst _hoisted_4 = { class: \"cin-operations-content\" }\nconst _hoisted_5 = { class: \"operation-row\" }\nconst _hoisted_6 = {\n  key: 0,\n  class: \"search-bar\"\n}\nconst _hoisted_7 = { class: \"filter-info\" }\nconst _hoisted_8 = { class: \"parameters-table\" }\nconst _hoisted_9 = {\n  key: 0,\n  class: \"parameter-source\"\n}\nconst _hoisted_10 = {\n  key: 1,\n  class: \"no-source\"\n}\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  const _component_el_cascader = _resolveComponent(\"el-cascader\")!\n  const _component_el_button = _resolveComponent(\"el-button\")!\n  const _component_el_divider = _resolveComponent(\"el-divider\")!\n  const _component_el_input = _resolveComponent(\"el-input\")!\n  const _component_el_table_column = _resolveComponent(\"el-table-column\")!\n  const _component_document = _resolveComponent(\"document\")!\n  const _component_el_icon = _resolveComponent(\"el-icon\")!\n  const _component_el_table = _resolveComponent(\"el-table\")!\n  const _component_DataFileManager = _resolveComponent(\"DataFileManager\")!\n  const _component_ParamParseDialog = _resolveComponent(\"ParamParseDialog\")!\n  const _component_ParamValueViewer = _resolveComponent(\"ParamValueViewer\")!\n\n  return (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _createElementVNode(\"div\", _hoisted_2, [\n      _createElementVNode(\"div\", _hoisted_3, [\n        _cache[7] || (_cache[7] = _createElementVNode(\"div\", { class: \"section-header\" }, [\n          _createElementVNode(\"h4\", null, \"CIN 参数列表\"),\n          _createElementVNode(\"div\", { class: \"header-actions\" })\n        ], -1)),\n        _createElementVNode(\"div\", _hoisted_4, [\n          _createElementVNode(\"div\", _hoisted_5, [\n            (_ctx.categories.length > 0)\n              ? (_openBlock(), _createBlock(_component_el_cascader, {\n                  key: 0,\n                  modelValue: _ctx.selectedTemplatePath,\n                  \"onUpdate:modelValue\": _cache[0] || (_cache[0] = ($event: any) => ((_ctx.selectedTemplatePath) = $event)),\n                  size: \"small\",\n                  options: _ctx.cascaderOptions,\n                  placeholder: \"选择 CIN 模板\",\n                  style: {\"width\":\"260px\",\"margin-right\":\"16px\"},\n                  onChange: _ctx.handleTemplateChange,\n                  clearable: \"\"\n                }, null, 8, [\"modelValue\", \"options\", \"onChange\"]))\n              : _createCommentVNode(\"\", true),\n            _createVNode(_component_el_button, {\n              type: \"primary\",\n              size: \"small\",\n              onClick: _ctx.selectLocalFile\n            }, {\n              default: _withCtx(() => _cache[4] || (_cache[4] = [\n                _createTextVNode(\"选择本地 CIN 文件\")\n              ])),\n              _: 1,\n              __: [4]\n            }, 8, [\"onClick\"]),\n            _createVNode(_component_el_button, {\n              type: \"primary\",\n              size: \"small\",\n              onClick: _ctx.exportCinFile,\n              style: {\"margin-left\":\"auto\"},\n              loading: _ctx.processing\n            }, {\n              default: _withCtx(() => _cache[5] || (_cache[5] = [\n                _createTextVNode(\"导出 CIN\")\n              ])),\n              _: 1,\n              __: [5]\n            }, 8, [\"onClick\", \"loading\"])\n          ])\n        ]),\n        _createVNode(_component_el_divider, { style: {\"margin\":\"8px 0\"} }),\n        (_ctx.variables.length > 0)\n          ? (_openBlock(), _createElementBlock(\"div\", _hoisted_6, [\n              _createVNode(_component_el_input, {\n                modelValue: _ctx.searchKeyword,\n                \"onUpdate:modelValue\": _cache[1] || (_cache[1] = ($event: any) => ((_ctx.searchKeyword) = $event)),\n                size: \"small\",\n                placeholder: \"搜索参数名称...\",\n                clearable: \"\",\n                style: {\"width\":\"260px\"}\n              }, null, 8, [\"modelValue\"]),\n              _createElementVNode(\"div\", _hoisted_7, [\n                _createElementVNode(\"span\", null, \"共 \" + _toDisplayString(_ctx.filteredVariables.length) + \" / \" + _toDisplayString(_ctx.variables.length) + \" 个参数\", 1)\n              ])\n            ]))\n          : _createCommentVNode(\"\", true),\n        _createElementVNode(\"div\", _hoisted_8, [\n          _createVNode(_component_el_table, {\n            data: _ctx.filteredVariables,\n            border: \"\",\n            style: {\"width\":\"100%\",\"height\":\"100%\"},\n            size: \"small\"\n          }, {\n            default: _withCtx(() => [\n              _createVNode(_component_el_table_column, {\n                prop: \"name\",\n                label: \"参数名\",\n                width: \"200\",\n                \"show-overflow-tooltip\": \"\"\n              }),\n              _createVNode(_component_el_table_column, {\n                prop: \"type\",\n                label: \"数据类型 (CAPL)\",\n                width: \"200\",\n                \"show-overflow-tooltip\": \"\"\n              }),\n              _createVNode(_component_el_table_column, {\n                label: \"参数值\",\n                \"min-width\": \"200\"\n              }, {\n                default: _withCtx((scope) => [\n                  _createVNode(_component_el_input, {\n                    modelValue: _ctx.parameterValues[scope.row.name],\n                    \"onUpdate:modelValue\": ($event: any) => ((_ctx.parameterValues[scope.row.name]) = $event),\n                    placeholder: \"输入参数值\",\n                    size: \"small\",\n                    onChange: ($event: any) => (_ctx.onParameterChange(scope.row.name)),\n                    readonly: _ctx.isComplexParameter(scope.row)\n                  }, null, 8, [\"modelValue\", \"onUpdate:modelValue\", \"onChange\", \"readonly\"])\n                ]),\n                _: 1\n              }),\n              _createVNode(_component_el_table_column, {\n                label: \"操作\",\n                width: \"80\",\n                align: \"center\"\n              }, {\n                default: _withCtx((scope) => [\n                  _createVNode(_component_el_button, {\n                    type: \"text\",\n                    size: \"small\",\n                    onClick: ($event: any) => (_ctx.editComplexParameter(scope.row))\n                  }, {\n                    default: _withCtx(() => _cache[6] || (_cache[6] = [\n                      _createTextVNode(\" 编辑 \")\n                    ])),\n                    _: 2,\n                    __: [6]\n                  }, 1032, [\"onClick\"])\n                ]),\n                _: 1\n              }),\n              _createVNode(_component_el_table_column, {\n                label: \"参数值来源\",\n                width: \"150\",\n                \"show-overflow-tooltip\": \"\"\n              }, {\n                default: _withCtx((scope) => [\n                  (_ctx.getParameterSource(scope.row.name))\n                    ? (_openBlock(), _createElementBlock(\"div\", _hoisted_9, [\n                        _createVNode(_component_el_icon, null, {\n                          default: _withCtx(() => [\n                            _createVNode(_component_document)\n                          ]),\n                          _: 1\n                        }),\n                        _createElementVNode(\"span\", null, _toDisplayString(_ctx.getParameterSource(scope.row.name)), 1)\n                      ]))\n                    : (_openBlock(), _createElementBlock(\"span\", _hoisted_10, \"CIN 文件\"))\n                ]),\n                _: 1\n              })\n            ]),\n            _: 1\n          }, 8, [\"data\"])\n        ])\n      ]),\n      _createVNode(_component_DataFileManager, { onParseResult: _ctx.handleParseResult }, null, 8, [\"onParseResult\"])\n    ]),\n    _createVNode(_component_ParamParseDialog, {\n      modelValue: _ctx.parseDialogVisible,\n      \"onUpdate:modelValue\": _cache[2] || (_cache[2] = ($event: any) => ((_ctx.parseDialogVisible) = $event)),\n      \"source-file\": _ctx.currentParseFile,\n      \"parsed-params\": _ctx.currentParsedParams,\n      onApplyParams: _ctx.applyParsedParams\n    }, null, 8, [\"modelValue\", \"source-file\", \"parsed-params\", \"onApplyParams\"]),\n    _createVNode(_component_ParamValueViewer, {\n      modelValue: _ctx.complexParamDialogVisible,\n      \"onUpdate:modelValue\": _cache[3] || (_cache[3] = ($event: any) => ((_ctx.complexParamDialogVisible) = $event)),\n      value: _ctx.currentComplexParamValue,\n      readonly: false,\n      title: _ctx.complexParamTitle,\n      onConfirm: _ctx.handleComplexParamConfirm\n    }, null, 8, [\"modelValue\", \"value\", \"title\", \"onConfirm\"])\n  ]))\n}", "<template>\n  <div class=\"parameter-tool\">\n    <!-- 参数编辑主区域 -->\n    <div class=\"main-content\">\n      <!-- CIN 参数列表卡片 -->\n      <div class=\"cin-parameters-section\">\n        <div class=\"section-header\">\n          <h4>CIN 参数列表</h4>\n          <div class=\"header-actions\">\n          </div>\n        </div>\n\n        <div class=\"cin-operations-content\">\n          <div class=\"operation-row\">\n            <el-cascader v-model=\"selectedTemplatePath\" size=\"small\" :options=\"cascaderOptions\"\n              v-if=\"categories.length > 0\" placeholder=\"选择 CIN 模板\" style=\"width: 260px; margin-right: 16px;\"\n              @change=\"handleTemplateChange\" clearable />\n\n            <el-button type=\"primary\" size=\"small\" @click=\"selectLocalFile\">选择本地 CIN 文件</el-button>\n            <el-button type=\"primary\" size=\"small\" @click=\"exportCinFile\" style=\"margin-left: auto;\"\n              :loading=\"processing\">导出 CIN</el-button>\n          </div>\n        </div>\n\n        <el-divider style=\"margin: 8px 0\" />\n\n        <!-- 搜索和过滤 -->\n        <div class=\"search-bar\" v-if=\"variables.length > 0\">\n          <el-input v-model=\"searchKeyword\" size=\"small\" placeholder=\"搜索参数名称...\" clearable style=\"width: 260px;\" />\n          <div class=\"filter-info\">\n            <span>共 {{ filteredVariables.length }} / {{ variables.length }} 个参数</span>\n          </div>\n        </div>\n\n        <!-- 参数表格 -->\n        <div class=\"parameters-table\">\n          <el-table :data=\"filteredVariables\" border style=\"width: 100%; height: 100%;\" size=\"small\">\n            <el-table-column prop=\"name\" label=\"参数名\" width=\"200\" show-overflow-tooltip />\n            <el-table-column prop=\"type\" label=\"数据类型 (CAPL)\" width=\"200\" show-overflow-tooltip />\n            <el-table-column label=\"参数值\" min-width=\"200\">\n              <template #default=\"scope\">\n                <el-input\n                  v-model=\"parameterValues[scope.row.name]\"\n                  placeholder=\"输入参数值\"\n                  size=\"small\"\n                  @change=\"onParameterChange(scope.row.name)\"\n                  :readonly=\"isComplexParameter(scope.row)\"\n                />\n              </template>\n            </el-table-column>\n            <el-table-column label=\"操作\" width=\"80\" align=\"center\">\n              <template #default=\"scope\">\n                <el-button\n                  type=\"text\"\n                  size=\"small\"\n                  @click=\"editComplexParameter(scope.row)\"\n                >\n                  编辑\n                </el-button>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"参数值来源\" width=\"150\" show-overflow-tooltip>\n              <template #default=\"scope\">\n                <div v-if=\"getParameterSource(scope.row.name)\" class=\"parameter-source\">\n                  <el-icon>\n                    <document />\n                  </el-icon>\n                  <span>{{ getParameterSource(scope.row.name) }}</span>\n                </div>\n                <span v-else class=\"no-source\">CIN 文件</span>\n              </template>\n            </el-table-column>\n          </el-table>\n        </div>\n      </div>\n\n      <!-- 数据文件管理 -->\n      <DataFileManager @parse-result=\"handleParseResult\" />\n    </div>\n\n    <!-- 参数导入结果弹窗 -->\n    <ParamParseDialog v-model=\"parseDialogVisible\" :source-file=\"currentParseFile\" :parsed-params=\"currentParsedParams\"\n      @apply-params=\"applyParsedParams\" />\n\n    <!-- 复杂参数编辑弹窗 -->\n    <ParamValueViewer v-model=\"complexParamDialogVisible\" :value=\"currentComplexParamValue\" :readonly=\"false\"\n      :title=\"complexParamTitle\" @confirm=\"handleComplexParamConfirm\" />\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { defineComponent, ref, computed, onMounted } from \"vue\";\nimport {\n  appApi,\n  CinTemplate,\n  CaplVariable,\n  CinParameterParseRequest,\n  CinParameterRequest,\n  SourceFile,\n  SourceFileType,\n  SourceFileStatus,\n  ParsedParam\n} from \"@/api/appApi\";\nimport { ElMessage } from \"element-plus\";\nimport { Document } from '@element-plus/icons-vue';\nimport ParamParseDialog from \"@/components/ParamParseDialog.vue\";\nimport DataFileManager from \"@/components/DataFileManager.vue\";\nimport ParamValueViewer from \"@/components/ParamValueViewer.vue\";\n\nexport default defineComponent({\n  name: \"ParameterToolView\",\n  components: {\n    ParamParseDialog,\n    DataFileManager,\n    ParamValueViewer,\n    Document\n  },\n  setup() {\n    // 数据\n    const templates = ref<CinTemplate[]>([]);\n    const sourceType = ref<string>(\"template\");\n    const selectedCategory = ref<string>(\"\");\n    const selectedTemplateId = ref<string>(\"\");\n    const selectedTemplatePath = ref<string[]>([]);\n    const filePath = ref<string>(\"\");\n    const variables = ref<CaplVariable[]>([]);\n    const parameterValues = ref<{ [key: string]: string }>({});\n    const sourceFilePath = ref<string>(\"\");\n    const searchKeyword = ref<string>(\"\");\n\n    // 状态\n    const parsing = ref(false);\n    const processing = ref(false);\n\n    // 源文件管理相关\n    const parseDialogVisible = ref(false);\n    const currentParseFile = ref<SourceFile | null>(null);\n    const currentParsedParams = ref<ParsedParam[]>([]);\n    const parameterSources = ref<{ [key: string]: string }>({});\n\n    // 新增状态\n    const modifiedParams = ref<Set<string>>(new Set());\n\n    // 复杂参数编辑相关\n    const complexParamDialogVisible = ref(false);\n    const currentComplexParamValue = ref<string>('');\n    const currentComplexParamName = ref<string>('');\n    const complexParamTitle = computed(() => `编辑参数 - ${currentComplexParamName.value}`);\n\n    // 计算属性\n    const categories = computed(() => {\n      const categorySet = new Set(templates.value.map(t => t.category));\n      return Array.from(categorySet).filter(c => c);\n    });\n\n    const cascaderOptions = computed(() => {\n      return categories.value.map(category => ({\n        value: category,\n        label: category,\n        children: templates.value\n          .filter(t => t.category === category)\n          .map(template => ({\n            value: template.id,\n            label: template.name\n          }))\n      }));\n    });\n\n    const filteredTemplates = computed(() => {\n      if (!selectedCategory.value) return [];\n      return templates.value.filter(t => t.category === selectedCategory.value);\n    });\n\n    const filteredVariables = computed(() => {\n      if (!searchKeyword.value) {\n        return variables.value;\n      }\n      const keyword = searchKeyword.value.toLowerCase();\n      return variables.value.filter(v =>\n        v.name.toLowerCase().includes(keyword)\n      );\n    });\n\n    const modifiedParamsCount = computed(() => modifiedParams.value.size);\n\n    const hasUnsavedChanges = computed(() => modifiedParams.value.size > 0);\n\n    // 方法\n    const loadTemplates = async () => {\n      try {\n        const response = await appApi.cinParameter.getTemplates();\n        templates.value = response.data;\n\n        // 自动选择第一个类别和模板\n        if (categories.value.length > 0) {\n          selectedCategory.value = categories.value[0];\n          onCategoryChange();\n        }\n      } catch (error) {\n        console.error('加载模板失败:', error);\n      }\n    };\n\n    const onSourceTypeChange = () => {\n      // 清空之前的数据\n      variables.value = [];\n      parameterValues.value = {};\n      sourceFilePath.value = \"\";\n      modifiedParams.value.clear();\n    };\n\n    const onCategoryChange = () => {\n      if (filteredTemplates.value.length > 0) {\n        selectedTemplateId.value = filteredTemplates.value[0].id;\n      } else {\n        selectedTemplateId.value = \"\";\n      }\n    };\n\n    const selectLocalFile = async () => {\n      try {\n        const response = await appApi.cinParameter.selectFile();\n        if (!response.data) {\n          return;\n        }\n\n        // 清空之前的选择\n        selectedTemplateId.value = '';\n        selectedCategory.value = '';\n        selectedTemplatePath.value = [];\n\n        filePath.value = response.data;\n        // 自动解析选择的文件\n        await parseSelectedFile();\n      } catch (error) {\n        console.error('选择文件失败:', error);\n      }\n    };\n\n    const parseSelectedFile = async () => {\n      if (!filePath.value) return;\n\n      parsing.value = true;\n      try {\n        const request: CinParameterParseRequest = {\n          sourceType: \"file\",\n          filePath: filePath.value\n        };\n\n        const response = await appApi.cinParameter.parseFile(request);\n        const result = response.data;\n\n        variables.value = result.variables;\n        sourceFilePath.value = result.sourceFilePath;\n\n        // 初始化参数值\n        parameterValues.value = {};\n        result.variables.forEach(variable => {\n          parameterValues.value[variable.name] = variable.value || \"\";\n        });\n\n        modifiedParams.value.clear();\n        ElMessage.success(`成功解析 ${result.variables.length} 个参数`);\n      } catch (error) {\n        console.error('解析文件失败:', error);\n        ElMessage.error('解析文件失败');\n      } finally {\n        parsing.value = false;\n      }\n    };\n\n    const handleTemplateChange = (value: string[]) => {\n      if (value && value.length === 2) {\n        selectedCategory.value = value[0];\n        selectedTemplateId.value = value[1];\n        loadSelectedTemplate();\n      }\n    };\n\n    const getTemplatesByCategory = (category: string) => {\n      return templates.value.filter(t => t.category === category);\n    };\n\n    const loadSelectedTemplate = async () => {\n      if (!selectedTemplateId.value) return;\n\n      parsing.value = true;\n      try {\n        const request: CinParameterParseRequest = {\n          sourceType: \"template\",\n          templateId: selectedTemplateId.value,\n          filePath: \"\"\n        };\n\n        const response = await appApi.cinParameter.parseFile(request);\n        const result = response.data;\n\n        variables.value = result.variables;\n        sourceFilePath.value = result.sourceFilePath;\n\n        // 初始化参数值\n        parameterValues.value = {};\n        result.variables.forEach(variable => {\n          parameterValues.value[variable.name] = variable.value || \"\";\n        });\n\n        modifiedParams.value.clear();\n        ElMessage.success(`成功加载模板 ${result.variables.length} 个参数`);\n      } catch (error) {\n        console.error('加载模板失败:', error);\n        ElMessage.error('加载模板失败');\n      } finally {\n        parsing.value = false;\n      }\n    };\n\n    const onParameterChange = (paramName: string) => {\n      modifiedParams.value.add(paramName);\n    };\n\n    const exportCinFile = async () => {\n      processing.value = true;\n      try {\n        const request: CinParameterRequest = {\n          sourceType: sourceType.value,\n          templateId: sourceType.value === \"template\" ? selectedTemplateId.value : undefined,\n          filePath: sourceType.value === \"file\" ? filePath.value : \"\",\n          parameterValues: parameterValues.value\n        };\n\n        const response = await appApi.cinParameter.processFile(request);\n        const result = response.data;\n\n        ElMessage.success(`文件导出成功！`);\n        modifiedParams.value.clear();\n\n        await appApi.explorer.openExplorer(result.outputFilePath);\n\n      } catch (error) {\n        console.error('导出文件失败:', error);\n        ElMessage.error('导出文件失败');\n      } finally {\n        processing.value = false;\n      }\n    };\n\n    // 源文件管理相关方法\n    const handleParseResult = (file: SourceFile, params: ParsedParam[]) => {\n      currentParseFile.value = file;\n      currentParsedParams.value = params;\n      parseDialogVisible.value = true;\n    };\n\n    const applyParsedParams = (params: ParsedParam[]) => {\n      params.forEach(param => {\n        // 查找对应的变量（不区分大小写）\n        const variable = variables.value.find(v => v.name.toLowerCase() === param.name.toLowerCase());\n        if (variable) {\n          // 应用参数值\n          parameterValues.value[variable.name] = String(param.value);\n          // 记录参数来源\n          parameterSources.value[variable.name] = param.source;\n          // 标记为已修改\n          modifiedParams.value.add(variable.name);\n        }\n      });\n\n      // 注意：文件列表现在由DataFileManager组件管理\n\n      ElMessage.success(`成功应用 ${params.length} 个参数`);\n    };\n\n    const getParameterSource = (paramName: string) => {\n      return parameterSources.value[paramName];\n    };\n\n    // 判断参数是否为复杂类型\n    const isComplexParameter = (variable: CaplVariable) => {\n      const value = parameterValues.value[variable.name];\n      if (!value) return false;\n\n      // 检查是否是结构体类型（包含大括号）\n      const trimmedValue = value.trim();\n      if (trimmedValue.startsWith('{') && trimmedValue.includes(',')) {\n        return true;\n      }\n\n      // 检查是否是JSON格式\n      if (trimmedValue.startsWith('{') || trimmedValue.startsWith('[')) {\n        try {\n          JSON.parse(trimmedValue);\n          return true;\n        } catch {\n          // 不是有效的JSON，但可能是CIN格式的结构体\n          return trimmedValue.includes(',') || trimmedValue.includes('{');\n        }\n      }\n\n      // 检查变量类型是否为复杂类型\n      const type = variable.type.toLowerCase();\n      return type.includes('struct') || type.includes('array') || type.includes('[]');\n    };\n\n    // 编辑复杂参数\n    const editComplexParameter = (variable: CaplVariable) => {\n      currentComplexParamName.value = variable.name;\n      currentComplexParamValue.value = parameterValues.value[variable.name] || '';\n      complexParamDialogVisible.value = true;\n    };\n\n    // 处理复杂参数编辑确认\n    const handleComplexParamConfirm = (value: string) => {\n      if (currentComplexParamName.value) {\n        parameterValues.value[currentComplexParamName.value] = value;\n        onParameterChange(currentComplexParamName.value);\n      }\n    };\n\n    const getFileName = (path: string) => {\n      if (!path) return '';\n      return path.split(/[\\\\/]/).pop() || '';\n    };\n\n    const getFileTypeTagType = (fileType: SourceFileType) => {\n      switch (fileType) {\n        case SourceFileType.Arxml: return 'primary';\n        case SourceFileType.Sddb: return 'success';\n        case SourceFileType.Ldf: return 'warning';\n        default: return '';\n      }\n    };\n\n    const getAppliedParamsCount = (file: SourceFile) => {\n      // 计算该文件已应用的参数数量\n      let count = 0;\n      Object.keys(parameterSources.value).forEach(paramName => {\n        if (parameterSources.value[paramName] === file.fileName) {\n          count++;\n        }\n      });\n      return count;\n    };\n\n\n\n\n\n\n\n    onMounted(async () => {\n      await loadTemplates();\n    });\n\n    return {\n      templates,\n      sourceType,\n      selectedCategory,\n      selectedTemplateId,\n      selectedTemplatePath,\n      filePath,\n      variables,\n      parameterValues,\n      sourceFilePath,\n      searchKeyword,\n      parsing,\n      processing,\n      categories,\n      cascaderOptions,\n      filteredTemplates,\n      filteredVariables,\n      modifiedParamsCount,\n      hasUnsavedChanges,\n      onSourceTypeChange,\n      onCategoryChange,\n      selectLocalFile,\n      handleTemplateChange,\n      getTemplatesByCategory,\n      loadSelectedTemplate,\n      onParameterChange,\n      exportCinFile,\n      getFileName,\n      getFileTypeTagType,\n      getAppliedParamsCount,\n      // 源文件管理相关\n      parseDialogVisible,\n      currentParseFile,\n      currentParsedParams,\n      handleParseResult,\n      applyParsedParams,\n      getParameterSource,\n      // 复杂参数编辑相关\n      isComplexParameter,\n      editComplexParameter,\n      handleComplexParamConfirm,\n      complexParamDialogVisible,\n      currentComplexParamValue,\n      complexParamTitle,\n      // 图标和枚举\n      Document,\n      SourceFileStatus\n    };\n  },\n});\n</script>\n\n<style scoped>\n.parameter-tool {\n  height: 100vh;\n  display: flex;\n  flex-direction: column;\n  background-color: var(--el-bg-color-page);\n}\n\n.toolbar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 20px;\n  background: var(--el-bg-color);\n  border-bottom: 1px solid var(--el-border-color-base);\n  flex-shrink: 0;\n}\n\n.toolbar-left {\n  display: flex;\n  align-items: center;\n}\n\n.toolbar-right {\n  display: flex;\n  gap: 8px;\n}\n\n.current-file-info {\n  padding: 8px 20px;\n  background: var(--el-color-primary-light-9);\n  border-bottom: 1px solid var(--el-border-color-lighter);\n  flex-shrink: 0;\n}\n\n.file-info-content {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.file-icon {\n  color: var(--el-color-primary);\n}\n\n.file-name {\n  font-weight: 500;\n  color: var(--el-text-color-primary);\n}\n\n.main-content {\n  flex: 1;\n  overflow: hidden;\n  display: flex;\n  flex-direction: column;\n  padding: 20px;\n  min-height: 0;\n  /* 允许flex子元素收缩 */\n}\n\n.search-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.filter-info {\n  display: flex;\n  gap: 16px;\n  font-size: 14px;\n  color: var(--el-text-color-secondary);\n}\n\n.import-info {\n  color: var(--el-color-primary);\n}\n\n\n\n.current-value {\n  color: var(--el-text-color-regular);\n  font-style: italic;\n}\n\n.parameter-source {\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  font-size: 12px;\n  color: var(--el-color-primary);\n}\n\n\n\n.no-source {\n  color: var(--el-text-color-placeholder);\n  font-style: italic;\n}\n\n/* CIN 参数列表卡片样式 */\n.cin-parameters-section {\n  margin-bottom: 16px;\n  padding: 16px;\n  background: var(--el-bg-color);\n  border: 1px solid var(--el-border-color);\n  border-radius: 6px;\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  min-height: 0;\n  /* 允许flex子元素收缩 */\n}\n\n.cin-operations-content {\n  margin-top: 12px;\n  flex-shrink: 0;\n  /* 不允许收缩 */\n}\n\n.operation-row {\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n  gap: 8px;\n}\n\n.search-bar {\n  margin-bottom: 12px;\n  flex-shrink: 0;\n  /* 不允许收缩 */\n}\n\n.parameters-table {\n  flex: 1;\n  overflow: hidden;\n  min-height: 0;\n  /* 允许flex子元素收缩 */\n}\n\n\n\n.imported-files-section {\n  margin-top: 16px;\n  padding: 16px;\n  background: var(--el-bg-color);\n  border: 1px solid var(--el-border-color);\n  border-radius: 6px;\n}\n\n.imported-files-section h4 {\n  margin: 0 0 12px 0;\n  color: var(--el-text-color-primary);\n  font-size: 14px;\n  font-weight: 600;\n}\n\n.imported-files-list {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.imported-file-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 8px 12px;\n  background: var(--el-fill-color-light);\n  border-radius: 4px;\n}\n\n.imported-file-item .file-name {\n  font-weight: 500;\n}\n\n.param-count {\n  font-size: 12px;\n  color: var(--el-text-color-secondary);\n}\n\n.file-actions {\n  margin-left: auto;\n  display: flex;\n  gap: 8px;\n}\n\n.template-selection {\n  display: flex;\n}\n\n.category-list {\n  width: 200px;\n  border-right: 1px solid var(--el-border-color-lighter);\n  overflow-y: auto;\n}\n\n.category-item {\n  padding: 12px 16px;\n  cursor: pointer;\n  border-bottom: 1px solid var(--el-border-color-lighter);\n}\n\n.category-item:hover {\n  background: var(--el-fill-color-light);\n}\n\n.category-item.active {\n  background: var(--el-color-primary-light-9);\n  color: var(--el-color-primary);\n  font-weight: 500;\n}\n\n.template-list {\n  flex: 1;\n  overflow-y: auto;\n  padding: 8px;\n}\n\n.template-item {\n  padding: 12px;\n  margin-bottom: 8px;\n  border: 1px solid var(--el-border-color-lighter);\n  border-radius: 4px;\n  cursor: pointer;\n}\n\n.template-item:hover {\n  border-color: var(--el-color-primary);\n}\n\n.template-item.active {\n  border-color: var(--el-color-primary);\n  background: var(--el-color-primary-light-9);\n}\n\n.template-name {\n  font-weight: 500;\n  color: var(--el-text-color-primary);\n  margin-bottom: 4px;\n}\n\n.template-desc {\n  font-size: 12px;\n  color: var(--el-text-color-secondary);\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .toolbar {\n    flex-direction: column;\n    gap: 8px;\n    align-items: stretch;\n  }\n\n  .toolbar-left,\n  .toolbar-right {\n    justify-content: center;\n  }\n\n  .search-bar {\n    flex-direction: column;\n    gap: 8px;\n    align-items: stretch;\n  }\n\n  .template-selection {\n    flex-direction: column;\n    height: auto;\n  }\n\n  .category-list {\n    width: auto;\n    border-right: none;\n    border-bottom: 1px solid var(--el-border-color-lighter);\n    max-height: 150px;\n  }\n}\n</style>\n", "import { defineComponent as _defineComponent } from 'vue'\nimport { resolveComponent as _resolveComponent, createVNode as _createVNode, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, withCtx as _withCtx, createSlots as _createSlots, openBlock as _openBlock, createBlock as _createBlock } from \"vue\"\n\nconst _hoisted_1 = { class: \"param-value-viewer\" }\nconst _hoisted_2 = { class: \"dialog-footer\" }\n\nimport { ref, computed, watch } from 'vue';\n\n// Props\ninterface Props {\n  modelValue: boolean;\n  value: any;\n  readonly?: boolean;\n  title?: string;\n}\n\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'ParamValueViewer',\n  props: {\n    modelValue: { type: Boolean },\n    value: {},\n    readonly: { type: Boolean, default: false },\n    title: { default: '参数值' }\n  },\n  emits: [\"update:modelValue\", \"update:value\", \"confirm\"],\n  setup(__props: any, { emit: __emit }) {\n\nconst props = __props;\n\n// Emits\nconst emit = __emit;\n\n// 数据\nconst visible = ref(false);\nconst displayValue = ref('');\n\n// 计算属性\nconst formattedValue = computed(() => {\n  if (props.value === null || props.value === undefined) {\n    return '';\n  }\n  \n  if (typeof props.value === 'string') {\n    // 如果是字符串，尝试格式化JSON\n    try {\n      const parsed = JSON.parse(props.value);\n      return JSON.stringify(parsed, null, 2);\n    } catch {\n      // 如果不是有效的JSON，直接返回原值\n      return props.value;\n    }\n  } else {\n    // 如果是对象，序列化为JSON\n    try {\n      return JSON.stringify(props.value, null, 2);\n    } catch {\n      return String(props.value);\n    }\n  }\n});\n\n// 监听器\nwatch(() => props.modelValue, (newVal) => {\n  visible.value = newVal;\n  if (newVal) {\n    displayValue.value = formattedValue.value;\n  }\n});\n\nwatch(visible, (newVal) => {\n  emit('update:modelValue', newVal);\n});\n\nwatch(() => props.value, () => {\n  if (visible.value) {\n    displayValue.value = formattedValue.value;\n  }\n});\n\n// 方法\nconst handleClose = () => {\n  visible.value = false;\n};\n\nconst handleInput = (value: string) => {\n  if (!props.readonly) {\n    emit('update:value', value);\n  }\n};\n\nconst handleConfirm = () => {\n  if (!props.readonly) {\n    emit('confirm', displayValue.value);\n  }\n  handleClose();\n};\n\nreturn (_ctx: any,_cache: any) => {\n  const _component_el_input = _resolveComponent(\"el-input\")!\n  const _component_el_button = _resolveComponent(\"el-button\")!\n  const _component_el_dialog = _resolveComponent(\"el-dialog\")!\n\n  return (_openBlock(), _createBlock(_component_el_dialog, {\n    modelValue: visible.value,\n    \"onUpdate:modelValue\": _cache[1] || (_cache[1] = ($event: any) => ((visible).value = $event)),\n    title: _ctx.title,\n    width: \"60%\",\n    \"before-close\": handleClose,\n    \"destroy-on-close\": \"\"\n  }, _createSlots({\n    default: _withCtx(() => [\n      _createElementVNode(\"div\", _hoisted_1, [\n        _createVNode(_component_el_input, {\n          modelValue: displayValue.value,\n          \"onUpdate:modelValue\": _cache[0] || (_cache[0] = ($event: any) => ((displayValue).value = $event)),\n          type: \"textarea\",\n          rows: 15,\n          readonly: _ctx.readonly,\n          placeholder: _ctx.readonly ? '' : '请输入参数值...',\n          style: {\"font-family\":\"'Courier New', monospace\"},\n          onInput: handleInput\n        }, null, 8, [\"modelValue\", \"readonly\", \"placeholder\"])\n      ])\n    ]),\n    _: 2\n  }, [\n    (!_ctx.readonly)\n      ? {\n          name: \"footer\",\n          fn: _withCtx(() => [\n            _createElementVNode(\"div\", _hoisted_2, [\n              _createVNode(_component_el_button, { onClick: handleClose }, {\n                default: _withCtx(() => _cache[2] || (_cache[2] = [\n                  _createTextVNode(\"取消\")\n                ])),\n                _: 1,\n                __: [2]\n              }),\n              _createVNode(_component_el_button, {\n                type: \"primary\",\n                onClick: handleConfirm\n              }, {\n                default: _withCtx(() => _cache[3] || (_cache[3] = [\n                  _createTextVNode(\"确定\")\n                ])),\n                _: 1,\n                __: [3]\n              })\n            ])\n          ]),\n          key: \"0\"\n        }\n      : undefined\n  ]), 1032, [\"modelValue\", \"title\"]))\n}\n}\n\n})", "<template>\n  <el-dialog\n    v-model=\"visible\"\n    :title=\"title\"\n    width=\"60%\"\n    :before-close=\"handleClose\"\n    destroy-on-close\n  >\n    <div class=\"param-value-viewer\">\n      <el-input\n        v-model=\"displayValue\"\n        type=\"textarea\"\n        :rows=\"15\"\n        :readonly=\"readonly\"\n        :placeholder=\"readonly ? '' : '请输入参数值...'\"\n        style=\"font-family: 'Courier New', monospace;\"\n        @input=\"handleInput\"\n      />\n    </div>\n\n    <template #footer v-if=\"!readonly\">\n      <div class=\"dialog-footer\">\n        <el-button @click=\"handleClose\">取消</el-button>\n        <el-button type=\"primary\" @click=\"handleConfirm\">确定</el-button>\n      </div>\n    </template>\n  </el-dialog>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref, computed, watch } from 'vue';\n\n// Props\ninterface Props {\n  modelValue: boolean;\n  value: any;\n  readonly?: boolean;\n  title?: string;\n}\n\nconst props = withDefaults(defineProps<Props>(), {\n  readonly: false,\n  title: '参数值'\n});\n\n// Emits\nconst emit = defineEmits<{\n  'update:modelValue': [value: boolean];\n  'update:value': [value: string];\n  'confirm': [value: string];\n}>();\n\n// 数据\nconst visible = ref(false);\nconst displayValue = ref('');\n\n// 计算属性\nconst formattedValue = computed(() => {\n  if (props.value === null || props.value === undefined) {\n    return '';\n  }\n  \n  if (typeof props.value === 'string') {\n    // 如果是字符串，尝试格式化JSON\n    try {\n      const parsed = JSON.parse(props.value);\n      return JSON.stringify(parsed, null, 2);\n    } catch {\n      // 如果不是有效的JSON，直接返回原值\n      return props.value;\n    }\n  } else {\n    // 如果是对象，序列化为JSON\n    try {\n      return JSON.stringify(props.value, null, 2);\n    } catch {\n      return String(props.value);\n    }\n  }\n});\n\n// 监听器\nwatch(() => props.modelValue, (newVal) => {\n  visible.value = newVal;\n  if (newVal) {\n    displayValue.value = formattedValue.value;\n  }\n});\n\nwatch(visible, (newVal) => {\n  emit('update:modelValue', newVal);\n});\n\nwatch(() => props.value, () => {\n  if (visible.value) {\n    displayValue.value = formattedValue.value;\n  }\n});\n\n// 方法\nconst handleClose = () => {\n  visible.value = false;\n};\n\nconst handleInput = (value: string) => {\n  if (!props.readonly) {\n    emit('update:value', value);\n  }\n};\n\nconst handleConfirm = () => {\n  if (!props.readonly) {\n    emit('confirm', displayValue.value);\n  }\n  handleClose();\n};\n</script>\n\n<style scoped>\n.param-value-viewer {\n  min-height: 300px;\n}\n\n.dialog-footer {\n  display: flex;\n  justify-content: flex-end;\n  gap: 12px;\n}\n</style>\n", "import script from \"./ParamValueViewer.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./ParamValueViewer.vue?vue&type=script&setup=true&lang=ts\"\n\nimport \"./ParamValueViewer.vue?vue&type=style&index=0&id=089a4f20&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-089a4f20\"]])\n\nexport default __exports__", "import { defineComponent as _defineComponent } from 'vue'\nimport { renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, resolveComponent as _resolveComponent, createBlock as _createBlock, withCtx as _withCtx, createVNode as _createVNode, unref as _unref, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode } from \"vue\"\n\nconst _hoisted_1 = { class: \"dialog-content\" }\nconst _hoisted_2 = { class: \"filter-section\" }\nconst _hoisted_3 = { class: \"selection-info\" }\nconst _hoisted_4 = { class: \"param-list\" }\nconst _hoisted_5 = [\"title\"]\nconst _hoisted_6 = { class: \"dialog-footer\" }\n\nimport { ref, computed, watch, nextTick } from 'vue';\nimport { ElMessage } from 'element-plus';\nimport { Search } from '@element-plus/icons-vue';\nimport { SourceFile, ParsedParam, SourceFileType, ParamType } from '@/api/appApi';\nimport ParamValueViewer from './ParamValueViewer.vue';\n\n// Props\ninterface Props {\n  modelValue: boolean;\n  sourceFile: SourceFile | null;\n  parsedParams: ParsedParam[];\n}\n\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'ParamParseDialog',\n  props: {\n    modelValue: { type: Boolean },\n    sourceFile: {},\n    parsedParams: {}\n  },\n  emits: [\"update:modelValue\", \"apply-params\"],\n  setup(__props: any, { emit: __emit }) {\n\nconst props = __props;\n\n// Emits\nconst emit = __emit;\n\n// 数据\nconst visible = ref(false);\nconst searchText = ref('');\nconst selectedEcu = ref('');\nconst selectedParams = ref<ParsedParam[]>([]);\nconst paramValueDialogVisible = ref(false);\nconst currentParamValue = ref<any>('');\nconst paramTable = ref();\n\n// 计算属性\nconst ecuList = computed(() => {\n  const ecus = new Set(props.parsedParams.map(p => p.ecuName));\n  return Array.from(ecus).sort();\n});\n\nconst dialogTitle = computed(() => {\n  if (!props.sourceFile) {\n    return '参数解析结果';\n  }\n\n  const fileName = props.sourceFile.fileName;\n  return `参数解析结果 - ${fileName}`;\n});\n\nconst filteredParams = computed(() => {\n  let params = props.parsedParams;\n\n  // 按ECU筛选\n  if (selectedEcu.value) {\n    params = params.filter(p => p.ecuName === selectedEcu.value);\n  }\n\n  // 按名称搜索\n  if (searchText.value) {\n    const search = searchText.value.toLowerCase();\n    params = params.filter(p =>\n      p.name.toLowerCase().includes(search) ||\n      p.description.toLowerCase().includes(search)\n    );\n  }\n\n  return params;\n});\n\n// 监听器\nwatch(() => props.modelValue, (newVal) => {\n  visible.value = newVal;\n  if (newVal) {\n    // 重置状态\n    searchText.value = '';\n    selectedParams.value = [];\n    // 默认选择第一个ECU\n    if (ecuList.value.length > 0) {\n      selectedEcu.value = ecuList.value[0];\n      // 默认全选当前ECU的参数\n      nextTick(() => {\n        selectAllCurrentEcuParams();\n      });\n    }\n  }\n});\n\nwatch(visible, (newVal) => {\n  emit('update:modelValue', newVal);\n});\n\nwatch(selectedEcu, () => {\n  // ECU切换时默认全选当前ECU的参数\n  nextTick(() => {\n    selectAllCurrentEcuParams();\n  });\n});\n\n// 方法\nconst handleEcuChange = () => {\n  // 切换ECU时默认全选\n  selectAllCurrentEcuParams();\n};\n\nconst selectAllCurrentEcuParams = () => {\n  // 全选当前ECU的参数\n  if (paramTable.value) {\n    const currentEcuParams = filteredParams.value;\n    currentEcuParams.forEach(row => {\n      paramTable.value.toggleRowSelection(row, true);\n    });\n  }\n};\n\nconst handleClose = () => {\n  visible.value = false;\n};\n\nconst handleSelectionChange = (selection: ParsedParam[]) => {\n  selectedParams.value = selection;\n};\n\nconst getFileTypeTagType = (fileType?: SourceFileType) => {\n  switch (fileType) {\n    case SourceFileType.Arxml: return 'primary';\n    case SourceFileType.Sddb: return 'success';\n    case SourceFileType.Ldf: return 'warning';\n    default: return '';\n  }\n};\n\nconst formatValue = (value: any) => {\n  if (value === null || value === undefined) {\n    return '';\n  }\n\n  return String(value);\n};\n\nconst showParamValue = (param: ParsedParam) => {\n  currentParamValue.value = param.value;\n  paramValueDialogVisible.value = true;\n};\n\nconst applySelectedParams = () => {\n  if (selectedParams.value.length === 0) {\n    ElMessage.warning('请先选择要应用的参数');\n    return;\n  }\n\n  emit('apply-params', selectedParams.value);\n  ElMessage.success(`成功应用 ${selectedParams.value.length} 个参数`);\n  handleClose();\n};\n\nreturn (_ctx: any,_cache: any) => {\n  const _component_el_option = _resolveComponent(\"el-option\")!\n  const _component_el_select = _resolveComponent(\"el-select\")!\n  const _component_el_icon = _resolveComponent(\"el-icon\")!\n  const _component_el_input = _resolveComponent(\"el-input\")!\n  const _component_el_table_column = _resolveComponent(\"el-table-column\")!\n  const _component_el_button = _resolveComponent(\"el-button\")!\n  const _component_el_table = _resolveComponent(\"el-table\")!\n  const _component_el_dialog = _resolveComponent(\"el-dialog\")!\n\n  return (_openBlock(), _createBlock(_component_el_dialog, {\n    modelValue: visible.value,\n    \"onUpdate:modelValue\": _cache[3] || (_cache[3] = ($event: any) => ((visible).value = $event)),\n    title: dialogTitle.value,\n    width: \"80%\",\n    \"before-close\": handleClose,\n    \"destroy-on-close\": \"\"\n  }, {\n    footer: _withCtx(() => [\n      _createElementVNode(\"div\", _hoisted_6, [\n        _createVNode(_component_el_button, { onClick: handleClose }, {\n          default: _withCtx(() => _cache[5] || (_cache[5] = [\n            _createTextVNode(\"取消\")\n          ])),\n          _: 1,\n          __: [5]\n        }),\n        _createVNode(_component_el_button, {\n          type: \"primary\",\n          onClick: applySelectedParams,\n          disabled: selectedParams.value.length === 0\n        }, {\n          default: _withCtx(() => [\n            _createTextVNode(\" 应用参数 (\" + _toDisplayString(selectedParams.value.length) + \") \", 1)\n          ]),\n          _: 1\n        }, 8, [\"disabled\"])\n      ])\n    ]),\n    default: _withCtx(() => [\n      _createElementVNode(\"div\", _hoisted_1, [\n        _createElementVNode(\"div\", _hoisted_2, [\n          _createVNode(_component_el_select, {\n            modelValue: selectedEcu.value,\n            \"onUpdate:modelValue\": _cache[0] || (_cache[0] = ($event: any) => ((selectedEcu).value = $event)),\n            size: \"small\",\n            placeholder: \"选择 ECU\",\n            style: {\"width\":\"100px\"},\n            onChange: handleEcuChange\n          }, {\n            default: _withCtx(() => [\n              (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(ecuList.value, (ecu) => {\n                return (_openBlock(), _createBlock(_component_el_option, {\n                  key: ecu,\n                  label: ecu,\n                  value: ecu\n                }, null, 8, [\"label\", \"value\"]))\n              }), 128))\n            ]),\n            _: 1\n          }, 8, [\"modelValue\"]),\n          _createVNode(_component_el_input, {\n            modelValue: searchText.value,\n            \"onUpdate:modelValue\": _cache[1] || (_cache[1] = ($event: any) => ((searchText).value = $event)),\n            placeholder: \"搜索参数名称...\",\n            clearable: \"\",\n            size: \"small\",\n            style: {\"width\":\"260px\"}\n          }, {\n            prefix: _withCtx(() => [\n              _createVNode(_component_el_icon, null, {\n                default: _withCtx(() => [\n                  _createVNode(_unref(Search))\n                ]),\n                _: 1\n              })\n            ]),\n            _: 1\n          }, 8, [\"modelValue\"]),\n          _createElementVNode(\"div\", _hoisted_3, \" 已选择 \" + _toDisplayString(selectedParams.value.length) + \"/\" + _toDisplayString(filteredParams.value.length) + \" 个参数 \", 1)\n        ]),\n        _createElementVNode(\"div\", _hoisted_4, [\n          _createVNode(_component_el_table, {\n            ref_key: \"paramTable\",\n            ref: paramTable,\n            data: filteredParams.value,\n            height: \"400\",\n            onSelectionChange: handleSelectionChange,\n            \"row-key\": \"name\",\n            size: \"small\"\n          }, {\n            default: _withCtx(() => [\n              _createVNode(_component_el_table_column, {\n                type: \"selection\",\n                width: \"55\"\n              }),\n              _createVNode(_component_el_table_column, {\n                prop: \"name\",\n                label: \"参数名\",\n                width: \"200\",\n                \"show-overflow-tooltip\": \"\"\n              }),\n              _createVNode(_component_el_table_column, {\n                prop: \"paramType\",\n                label: \"数据类型\",\n                width: \"200\",\n                \"show-overflow-tooltip\": \"\"\n              }),\n              _createVNode(_component_el_table_column, {\n                prop: \"value\",\n                label: \"参数值\",\n                \"min-width\": \"200\"\n              }, {\n                default: _withCtx(({ row }) => [\n                  _createElementVNode(\"span\", {\n                    class: \"param-value-text\",\n                    title: formatValue(row.value)\n                  }, _toDisplayString(formatValue(row.value)), 9, _hoisted_5)\n                ]),\n                _: 1\n              }),\n              _createVNode(_component_el_table_column, {\n                label: \"操作\",\n                width: \"80\",\n                align: \"center\"\n              }, {\n                default: _withCtx(({ row }) => [\n                  _createVNode(_component_el_button, {\n                    type: \"text\",\n                    size: \"small\",\n                    onClick: ($event: any) => (showParamValue(row))\n                  }, {\n                    default: _withCtx(() => _cache[4] || (_cache[4] = [\n                      _createTextVNode(\" 查看 \")\n                    ])),\n                    _: 2,\n                    __: [4]\n                  }, 1032, [\"onClick\"])\n                ]),\n                _: 1\n              })\n            ]),\n            _: 1\n          }, 8, [\"data\"])\n        ])\n      ]),\n      _createVNode(ParamValueViewer, {\n        modelValue: paramValueDialogVisible.value,\n        \"onUpdate:modelValue\": _cache[2] || (_cache[2] = ($event: any) => ((paramValueDialogVisible).value = $event)),\n        value: currentParamValue.value,\n        readonly: true,\n        title: \"参数值\"\n      }, null, 8, [\"modelValue\", \"value\"])\n    ]),\n    _: 1\n  }, 8, [\"modelValue\", \"title\"]))\n}\n}\n\n})", "<template>\n  <el-dialog v-model=\"visible\" :title=\"dialogTitle\" width=\"80%\" :before-close=\"handleClose\" destroy-on-close>\n    <div class=\"dialog-content\">\n      <!-- ECU选择和搜索 -->\n      <div class=\"filter-section\">\n        <el-select v-model=\"selectedEcu\" size=\"small\" placeholder=\"选择 ECU\" style=\"width: 100px;\"\n          @change=\"handleEcuChange\">\n          <el-option v-for=\"ecu in ecuList\" :key=\"ecu\" :label=\"ecu\" :value=\"ecu\" />\n        </el-select>\n\n        <el-input v-model=\"searchText\" placeholder=\"搜索参数名称...\" clearable size=\"small\" style=\"width: 260px\">\n          <template #prefix>\n            <el-icon>\n              <Search />\n            </el-icon>\n          </template>\n        </el-input>\n\n        <div class=\"selection-info\">\n          已选择 {{ selectedParams.length }}/{{ filteredParams.length }} 个参数\n        </div>\n      </div>\n\n      <!-- 参数列表 -->\n      <div class=\"param-list\">\n        <el-table ref=\"paramTable\" :data=\"filteredParams\" height=\"400\" @selection-change=\"handleSelectionChange\"\n          row-key=\"name\" size=\"small\">\n          <el-table-column type=\"selection\" width=\"55\" />\n          <el-table-column prop=\"name\" label=\"参数名\" width=\"200\" show-overflow-tooltip />\n          <el-table-column prop=\"paramType\" label=\"数据类型\" width=\"200\" show-overflow-tooltip />\n          <el-table-column prop=\"value\" label=\"参数值\" min-width=\"200\">\n            <template #default=\"{ row }\">\n              <span class=\"param-value-text\" :title=\"formatValue(row.value)\">\n                {{ formatValue(row.value) }}\n              </span>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"操作\" width=\"80\" align=\"center\">\n            <template #default=\"{ row }\">\n              <el-button type=\"text\" size=\"small\" @click=\"showParamValue(row)\">\n                查看\n              </el-button>\n            </template>\n          </el-table-column>\n        </el-table>\n      </div>\n\n\n    </div>\n\n    <template #footer>\n      <div class=\"dialog-footer\">\n        <el-button @click=\"handleClose\">取消</el-button>\n        <el-button type=\"primary\" @click=\"applySelectedParams\" :disabled=\"selectedParams.length === 0\">\n          应用参数 ({{ selectedParams.length }})\n        </el-button>\n      </div>\n    </template>\n\n    <!-- 参数值查看弹窗 -->\n    <ParamValueViewer v-model=\"paramValueDialogVisible\" :value=\"currentParamValue\" :readonly=\"true\" title=\"参数值\" />\n  </el-dialog>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref, computed, watch, nextTick } from 'vue';\nimport { ElMessage } from 'element-plus';\nimport { Search } from '@element-plus/icons-vue';\nimport { SourceFile, ParsedParam, SourceFileType, ParamType } from '@/api/appApi';\nimport ParamValueViewer from './ParamValueViewer.vue';\n\n// Props\ninterface Props {\n  modelValue: boolean;\n  sourceFile: SourceFile | null;\n  parsedParams: ParsedParam[];\n}\n\nconst props = defineProps<Props>();\n\n// Emits\nconst emit = defineEmits<{\n  'update:modelValue': [value: boolean];\n  'apply-params': [params: ParsedParam[]];\n}>();\n\n// 数据\nconst visible = ref(false);\nconst searchText = ref('');\nconst selectedEcu = ref('');\nconst selectedParams = ref<ParsedParam[]>([]);\nconst paramValueDialogVisible = ref(false);\nconst currentParamValue = ref<any>('');\nconst paramTable = ref();\n\n// 计算属性\nconst ecuList = computed(() => {\n  const ecus = new Set(props.parsedParams.map(p => p.ecuName));\n  return Array.from(ecus).sort();\n});\n\nconst dialogTitle = computed(() => {\n  if (!props.sourceFile) {\n    return '参数解析结果';\n  }\n\n  const fileName = props.sourceFile.fileName;\n  return `参数解析结果 - ${fileName}`;\n});\n\nconst filteredParams = computed(() => {\n  let params = props.parsedParams;\n\n  // 按ECU筛选\n  if (selectedEcu.value) {\n    params = params.filter(p => p.ecuName === selectedEcu.value);\n  }\n\n  // 按名称搜索\n  if (searchText.value) {\n    const search = searchText.value.toLowerCase();\n    params = params.filter(p =>\n      p.name.toLowerCase().includes(search) ||\n      p.description.toLowerCase().includes(search)\n    );\n  }\n\n  return params;\n});\n\n// 监听器\nwatch(() => props.modelValue, (newVal) => {\n  visible.value = newVal;\n  if (newVal) {\n    // 重置状态\n    searchText.value = '';\n    selectedParams.value = [];\n    // 默认选择第一个ECU\n    if (ecuList.value.length > 0) {\n      selectedEcu.value = ecuList.value[0];\n      // 默认全选当前ECU的参数\n      nextTick(() => {\n        selectAllCurrentEcuParams();\n      });\n    }\n  }\n});\n\nwatch(visible, (newVal) => {\n  emit('update:modelValue', newVal);\n});\n\nwatch(selectedEcu, () => {\n  // ECU切换时默认全选当前ECU的参数\n  nextTick(() => {\n    selectAllCurrentEcuParams();\n  });\n});\n\n// 方法\nconst handleEcuChange = () => {\n  // 切换ECU时默认全选\n  selectAllCurrentEcuParams();\n};\n\nconst selectAllCurrentEcuParams = () => {\n  // 全选当前ECU的参数\n  if (paramTable.value) {\n    const currentEcuParams = filteredParams.value;\n    currentEcuParams.forEach(row => {\n      paramTable.value.toggleRowSelection(row, true);\n    });\n  }\n};\n\nconst handleClose = () => {\n  visible.value = false;\n};\n\nconst handleSelectionChange = (selection: ParsedParam[]) => {\n  selectedParams.value = selection;\n};\n\nconst getFileTypeTagType = (fileType?: SourceFileType) => {\n  switch (fileType) {\n    case SourceFileType.Arxml: return 'primary';\n    case SourceFileType.Sddb: return 'success';\n    case SourceFileType.Ldf: return 'warning';\n    default: return '';\n  }\n};\n\nconst formatValue = (value: any) => {\n  if (value === null || value === undefined) {\n    return '';\n  }\n\n  return String(value);\n};\n\nconst showParamValue = (param: ParsedParam) => {\n  currentParamValue.value = param.value;\n  paramValueDialogVisible.value = true;\n};\n\nconst applySelectedParams = () => {\n  if (selectedParams.value.length === 0) {\n    ElMessage.warning('请先选择要应用的参数');\n    return;\n  }\n\n  emit('apply-params', selectedParams.value);\n  ElMessage.success(`成功应用 ${selectedParams.value.length} 个参数`);\n  handleClose();\n};\n</script>\n\n<style scoped>\n.dialog-content {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n\n.filter-section {\n  display: flex;\n  gap: 16px;\n  align-items: center;\n}\n\n.param-list {\n  border: 1px solid var(--el-border-color-base);\n  border-radius: 6px;\n}\n\n.selection-info {\n  font-size: 12px;\n  color: var(--el-text-color-regular);\n  margin-left: auto;\n}\n\n.param-value-text {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.dialog-footer {\n  display: flex;\n  justify-content: flex-end;\n  gap: 8px;\n}\n</style>\n", "import script from \"./ParamParseDialog.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./ParamParseDialog.vue?vue&type=script&setup=true&lang=ts\"\n\nimport \"./ParamParseDialog.vue?vue&type=style&index=0&id=9102454e&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-9102454e\"]])\n\nexport default __exports__", "import { createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, createElementBlock as _createElementBlock } from \"vue\"\n\nconst _hoisted_1 = { class: \"data-files-section\" }\nconst _hoisted_2 = { class: \"section-header\" }\nconst _hoisted_3 = { class: \"header-actions\" }\nconst _hoisted_4 = { class: \"data-files-table\" }\nconst _hoisted_5 = [\"title\"]\nconst _hoisted_6 = { class: \"name\" }\nconst _hoisted_7 = { class: \"action-buttons\" }\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  const _component_el_button = _resolveComponent(\"el-button\")!\n  const _component_el_tag = _resolveComponent(\"el-tag\")!\n  const _component_el_table_column = _resolveComponent(\"el-table-column\")!\n  const _component_el_table = _resolveComponent(\"el-table\")!\n\n  return (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _createElementVNode(\"div\", _hoisted_2, [\n      _cache[2] || (_cache[2] = _createElementVNode(\"h4\", null, \"数据文件 (ARXML/SDDB/LDF)\", -1)),\n      _createElementVNode(\"div\", _hoisted_3, [\n        _createVNode(_component_el_button, {\n          size: \"small\",\n          type: \"primary\",\n          onClick: _ctx.selectDataFiles\n        }, {\n          default: _withCtx(() => _cache[0] || (_cache[0] = [\n            _createTextVNode(\"添加文件\")\n          ])),\n          _: 1,\n          __: [0]\n        }, 8, [\"onClick\"]),\n        (_ctx.dataFiles.length > 0)\n          ? (_openBlock(), _createBlock(_component_el_button, {\n              key: 0,\n              size: \"small\",\n              type: \"danger\",\n              onClick: _ctx.clearAllDataFiles\n            }, {\n              default: _withCtx(() => _cache[1] || (_cache[1] = [\n                _createTextVNode(\"清空\")\n              ])),\n              _: 1,\n              __: [1]\n            }, 8, [\"onClick\"]))\n          : _createCommentVNode(\"\", true)\n      ])\n    ]),\n    _createElementVNode(\"div\", _hoisted_4, [\n      _createVNode(_component_el_table, {\n        data: _ctx.dataFiles,\n        border: \"\",\n        style: {\"width\":\"100%\"},\n        size: \"small\"\n      }, {\n        default: _withCtx(() => [\n          _createVNode(_component_el_table_column, {\n            label: \"类型\",\n            width: \"80\"\n          }, {\n            default: _withCtx((scope) => [\n              _createVNode(_component_el_tag, {\n                type: _ctx.getFileTypeTagType(scope.row.fileType),\n                size: \"small\"\n              }, {\n                default: _withCtx(() => [\n                  _createTextVNode(_toDisplayString(scope.row.fileType.toUpperCase()), 1)\n                ]),\n                _: 2\n              }, 1032, [\"type\"])\n            ]),\n            _: 1\n          }),\n          _createVNode(_component_el_table_column, {\n            label: \"文件名\",\n            \"min-width\": \"200\"\n          }, {\n            default: _withCtx((scope) => [\n              _createElementVNode(\"div\", {\n                class: \"file-name\",\n                title: scope.row.path\n              }, [\n                _createElementVNode(\"span\", _hoisted_6, _toDisplayString(scope.row.fileName), 1)\n              ], 8, _hoisted_5)\n            ]),\n            _: 1\n          }),\n          _createVNode(_component_el_table_column, {\n            label: \"状态\",\n            width: \"100\"\n          }, {\n            default: _withCtx((scope) => [\n              _createVNode(_component_el_tag, {\n                type: _ctx.getStatusTagType(scope.row.status),\n                size: \"small\"\n              }, {\n                default: _withCtx(() => [\n                  _createTextVNode(_toDisplayString(_ctx.getStatusText(scope.row.status)), 1)\n                ]),\n                _: 2\n              }, 1032, [\"type\"])\n            ]),\n            _: 1\n          }),\n          _createVNode(_component_el_table_column, {\n            label: \"操作\",\n            width: \"300\"\n          }, {\n            default: _withCtx((scope) => [\n              _createElementVNode(\"div\", _hoisted_7, [\n                (scope.row.status === _ctx.SourceFileStatus.Pending || scope.row.status === _ctx.SourceFileStatus.Error)\n                  ? (_openBlock(), _createBlock(_component_el_button, {\n                      key: 0,\n                      type: \"primary\",\n                      size: \"small\",\n                      onClick: ($event: any) => (_ctx.parseDataFile(scope.row.id))\n                    }, {\n                      default: _withCtx(() => _cache[3] || (_cache[3] = [\n                        _createTextVNode(\" 解析参数 \")\n                      ])),\n                      _: 2,\n                      __: [3]\n                    }, 1032, [\"onClick\"]))\n                  : _createCommentVNode(\"\", true),\n                (scope.row.status === _ctx.SourceFileStatus.Parsed)\n                  ? (_openBlock(), _createBlock(_component_el_button, {\n                      key: 1,\n                      type: \"success\",\n                      size: \"small\",\n                      onClick: ($event: any) => (_ctx.viewDataFileDetails(scope.row))\n                    }, {\n                      default: _withCtx(() => _cache[4] || (_cache[4] = [\n                        _createTextVNode(\" 查看结果 \")\n                      ])),\n                      _: 2,\n                      __: [4]\n                    }, 1032, [\"onClick\"]))\n                  : _createCommentVNode(\"\", true),\n                _createVNode(_component_el_button, {\n                  type: \"warning\",\n                  size: \"small\",\n                  onClick: ($event: any) => (_ctx.openDataFileFolder(scope.row.path))\n                }, {\n                  default: _withCtx(() => _cache[5] || (_cache[5] = [\n                    _createTextVNode(\" 打开文件夹 \")\n                  ])),\n                  _: 2,\n                  __: [5]\n                }, 1032, [\"onClick\"]),\n                _createVNode(_component_el_button, {\n                  type: \"danger\",\n                  size: \"small\",\n                  onClick: ($event: any) => (_ctx.removeDataFile(scope.row.id))\n                }, {\n                  default: _withCtx(() => _cache[6] || (_cache[6] = [\n                    _createTextVNode(\" 移除 \")\n                  ])),\n                  _: 2,\n                  __: [6]\n                }, 1032, [\"onClick\"])\n              ])\n            ]),\n            _: 1\n          })\n        ]),\n        _: 1\n      }, 8, [\"data\"])\n    ])\n  ]))\n}", "<template>\n  <div class=\"data-files-section\">\n    <div class=\"section-header\">\n      <h4>数据文件 (ARXML/SDDB/LDF)</h4>\n      <div class=\"header-actions\">\n        <el-button size=\"small\" type=\"primary\" @click=\"selectDataFiles\">添加文件</el-button>\n        <el-button size=\"small\" type=\"danger\" @click=\"clearAllDataFiles\" v-if=\"dataFiles.length > 0\">清空</el-button>\n      </div>\n    </div>\n    \n    <div class=\"data-files-table\">\n      <el-table :data=\"dataFiles\" border style=\"width: 100%\" size=\"small\">\n        <el-table-column label=\"类型\" width=\"80\">\n          <template #default=\"scope\">\n            <el-tag :type=\"getFileTypeTagType(scope.row.fileType)\" size=\"small\">{{ scope.row.fileType.toUpperCase() }}</el-tag>\n          </template>\n        </el-table-column>\n        \n        <el-table-column label=\"文件名\" min-width=\"200\">\n          <template #default=\"scope\">\n            <div class=\"file-name\" :title=\"scope.row.path\">\n              <span class=\"name\">{{ scope.row.fileName }}</span>\n            </div>\n          </template>\n        </el-table-column>\n        \n        <el-table-column label=\"状态\" width=\"100\">\n          <template #default=\"scope\">\n            <el-tag :type=\"getStatusTagType(scope.row.status)\" size=\"small\">{{ getStatusText(scope.row.status) }}</el-tag>\n          </template>\n        </el-table-column>\n        \n        <el-table-column label=\"操作\" width=\"300\">\n          <template #default=\"scope\">\n            <div class=\"action-buttons\">\n              <el-button \n                v-if=\"scope.row.status === SourceFileStatus.Pending || scope.row.status === SourceFileStatus.Error\"\n                type=\"primary\" \n                size=\"small\"\n                @click=\"parseDataFile(scope.row.id)\"\n              >\n                解析参数\n              </el-button>\n              \n              <el-button \n                v-if=\"scope.row.status === SourceFileStatus.Parsed\"\n                type=\"success\" \n                size=\"small\"\n                @click=\"viewDataFileDetails(scope.row)\"\n              >\n                查看结果\n              </el-button>\n              \n              <el-button\n                type=\"warning\"\n                size=\"small\"\n                @click=\"openDataFileFolder(scope.row.path)\"\n              >\n                打开文件夹\n              </el-button>\n              \n              <el-button \n                type=\"danger\" \n                size=\"small\"\n                @click=\"removeDataFile(scope.row.id)\"\n              >\n                移除\n              </el-button>\n            </div>\n          </template>\n        </el-table-column>\n      </el-table>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { defineComponent, ref, onMounted } from \"vue\";\nimport {\n  appApi,\n  SourceFile,\n  SourceFileType,\n  SourceFileStatus\n} from \"@/api/appApi\";\nimport { ElMessage, ElMessageBox } from \"element-plus\";\n\nexport default defineComponent({\n  name: \"DataFileManager\",\n  emits: ['parse-result'],\n  setup(props, { emit }) {\n    // 数据\n    const dataFiles = ref<SourceFile[]>([]);\n\n    // 方法\n    const loadDataFiles = async () => {\n      try {\n        const response = await appApi.cinParameter.getSourceFiles();\n        dataFiles.value = response.data;\n      } catch (error) {\n        console.error('加载数据文件失败:', error);\n        ElMessage.error('加载数据文件失败');\n      }\n    };\n\n    const selectDataFiles = async () => {\n      try {\n        const response = await appApi.cinParameter.selectSourceFiles();\n        const filePaths = response.data;\n        \n        if (filePaths && filePaths.length > 0) {\n          await addDataFiles(filePaths);\n          ElMessage.success(`成功添加 ${filePaths.length} 个参数数据文件`);\n        }\n      } catch (error) {\n        console.error('选择文件失败:', error);\n        ElMessage.error('选择文件失败');\n      }\n    };\n\n    const addDataFiles = async (filePaths: string[]) => {\n      try {\n        const response = await appApi.cinParameter.addSourceFiles({ filePaths });\n        const newFiles = response.data;\n        \n        // 更新导入文件列表\n        newFiles.forEach(file => {\n          const existingIndex = dataFiles.value.findIndex(f => f.id === file.id);\n          if (existingIndex >= 0) {\n            dataFiles.value[existingIndex] = file;\n          } else {\n            dataFiles.value.push(file);\n          }\n        });\n        \n        return newFiles;\n      } catch (error) {\n        console.error('添加文件失败:', error);\n        throw error;\n      }\n    };\n\n    const parseDataFile = async (fileId: string) => {\n      try {\n        // 先更新本地状态\n        const file = dataFiles.value.find(f => f.id === fileId);\n        if (file) {\n          file.status = SourceFileStatus.Parsing;\n        }\n\n        const response = await appApi.cinParameter.parseSourceFile({\n          fileId\n        });\n        \n        const updatedFile = response.data;\n        \n        // 更新文件列表中的对应项\n        const index = dataFiles.value.findIndex(f => f.id === fileId);\n        if (index >= 0) {\n          dataFiles.value[index] = updatedFile;\n        }\n        \n        ElMessage.success(`参数解析完成，共解析出 ${updatedFile.parsedParams?.length || 0} 个参数`);\n        \n        // 发送解析结果事件给父组件\n        if (updatedFile.parsedParams && updatedFile.parsedParams.length > 0) {\n          emit('parse-result', updatedFile, updatedFile.parsedParams);\n        }\n      } catch (error) {\n        console.error('解析文件失败:', error);\n        ElMessage.error('参数解析失败');\n        \n        // 恢复状态\n        const file = dataFiles.value.find(f => f.id === fileId);\n        if (file) {\n          file.status = SourceFileStatus.Error;\n        }\n      }\n    };\n\n    const viewDataFileDetails = (file: SourceFile) => {\n      if (file.parsedParams && file.parsedParams.length > 0) {\n        emit('parse-result', file, file.parsedParams);\n      } else {\n        ElMessage.warning('该文件暂无解析结果');\n      }\n    };\n\n    const openDataFileFolder = async (filePath: string) => {\n      try {\n        await appApi.explorer.openExplorer(filePath);\n      } catch (error) {\n        console.error('打开文件夹失败:', error);\n        ElMessage.error('打开文件夹失败');\n      }\n    };\n\n    const removeDataFile = async (fileId: string) => {\n      try {\n        // 调用服务端接口移除文件\n        await appApi.cinParameter.removeSourceFile({ fileId });\n        \n        // 从本地列表中移除文件\n        const fileIndex = dataFiles.value.findIndex(f => f.id === fileId);\n        if (fileIndex >= 0) {\n          dataFiles.value.splice(fileIndex, 1);\n          ElMessage.success('已移除数据文件');\n        }\n      } catch (error) {\n        console.error('移除文件失败:', error);\n        ElMessage.error('移除文件失败');\n      }\n    };\n\n    const clearAllDataFiles = async () => {\n      try {\n        await ElMessageBox.confirm('确定要清空所有参数数据文件吗？', '确认', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        });\n\n        // 调用服务端接口清空所有文件\n        await appApi.cinParameter.clearSourceFiles();\n        \n        // 清空本地文件列表\n        dataFiles.value = [];\n        ElMessage.success('已清空所有参数数据文件');\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('清空文件失败:', error);\n          ElMessage.error('清空文件失败');\n        }\n      }\n    };\n\n    // 辅助方法\n    const getFileTypeTagType = (fileType: SourceFileType) => {\n      switch (fileType) {\n        case SourceFileType.Arxml: return 'primary';\n        case SourceFileType.Sddb: return 'success';\n        case SourceFileType.Ldf: return 'warning';\n        default: return '';\n      }\n    };\n\n    const getStatusTagType = (status: SourceFileStatus) => {\n      switch (status) {\n        case SourceFileStatus.Pending: return '';\n        case SourceFileStatus.Parsing: return 'warning';\n        case SourceFileStatus.Parsed: return 'success';\n        case SourceFileStatus.Error: return 'danger';\n        default: return '';\n      }\n    };\n\n    const getStatusText = (status: SourceFileStatus) => {\n      switch (status) {\n        case SourceFileStatus.Pending: return '待解析';\n        case SourceFileStatus.Parsing: return '解析中';\n        case SourceFileStatus.Parsed: return '已解析';\n        case SourceFileStatus.Error: return '解析失败';\n        default: return status;\n      }\n    };\n\n    // 暴露给父组件的方法\n    const refreshDataFiles = () => {\n      loadDataFiles();\n    };\n\n    // 生命周期\n    onMounted(() => {\n      loadDataFiles();\n    });\n\n    return {\n      dataFiles,\n      selectDataFiles,\n      parseDataFile,\n      viewDataFileDetails,\n      openDataFileFolder,\n      removeDataFile,\n      clearAllDataFiles,\n      getFileTypeTagType,\n      getStatusTagType,\n      getStatusText,\n      refreshDataFiles,\n      // 枚举\n      SourceFileStatus\n    };\n  },\n});\n</script>\n\n<style scoped>\n/* 参数数据文件样式 */\n.data-files-section {\n  padding: 16px;\n  background: var(--el-bg-color);\n  border: 1px solid var(--el-border-color);\n  border-radius: 6px;\n  flex-shrink: 0;\n  max-height: 300px;\n  overflow-y: auto;\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n}\n\n.section-header h4 {\n  margin: 0;\n  color: var(--el-text-color-primary);\n  font-size: 14px;\n  font-weight: 600;\n}\n\n.header-actions {\n  display: flex;\n  gap: 8px;\n}\n\n.data-files-table {\n  margin-top: 12px;\n}\n\n.data-files-table .file-name .name {\n  font-weight: 500;\n}\n\n.action-buttons {\n  display: flex;\n  gap: 4px;\n  flex-wrap: wrap;\n}\n</style>\n", "import { render } from \"./DataFileManager.vue?vue&type=template&id=3b908206&scoped=true&ts=true\"\nimport script from \"./DataFileManager.vue?vue&type=script&lang=ts\"\nexport * from \"./DataFileManager.vue?vue&type=script&lang=ts\"\n\nimport \"./DataFileManager.vue?vue&type=style&index=0&id=3b908206&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-3b908206\"]])\n\nexport default __exports__", "import { render } from \"./ParameterToolView.vue?vue&type=template&id=38b01a87&scoped=true&ts=true\"\nimport script from \"./ParameterToolView.vue?vue&type=script&lang=ts\"\nexport * from \"./ParameterToolView.vue?vue&type=script&lang=ts\"\n\nimport \"./ParameterToolView.vue?vue&type=style&index=0&id=38b01a87&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-38b01a87\"]])\n\nexport default __exports__"], "names": ["_hoisted_1", "class", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_hoisted_6", "key", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_hoisted_10", "render", "_ctx", "_cache", "$props", "$setup", "$data", "$options", "_component_el_cascader", "_resolveComponent", "_component_el_button", "_component_el_divider", "_component_el_input", "_component_el_table_column", "_component_document", "_component_el_icon", "_component_el_table", "_component_DataFileManager", "_component_ParamParseDialog", "_component_ParamValueViewer", "_openBlock", "_createElementBlock", "_createElementVNode", "categories", "length", "_createBlock", "modelValue", "selectedTemplatePath", "$event", "size", "options", "cascaderOptions", "placeholder", "style", "onChange", "handleTemplateChange", "clearable", "_createCommentVNode", "_createVNode", "type", "onClick", "selectLocalFile", "default", "_withCtx", "_createTextVNode", "_", "__", "exportCinFile", "loading", "processing", "variables", "searchKeyword", "_toDisplayString", "filteredVariables", "data", "border", "prop", "label", "width", "scope", "parameterValues", "row", "name", "onParameterChange", "readonly", "isComplexParameter", "align", "editComplexParameter", "getParameterSource", "onParseResult", "handleParseResult", "parseDialogVisible", "currentParseFile", "currentParsedParams", "onApplyParams", "applyParsedParams", "complexParamDialogVisible", "value", "currentComplexParamValue", "title", "complexParamTitle", "onConfirm", "handleComplexParamConfirm", "_defineComponent", "__name", "props", "Boolean", "emits", "setup", "__props", "emit", "__emit", "visible", "ref", "displayValue", "formattedValue", "computed", "undefined", "parsed", "JSON", "parse", "stringify", "String", "watch", "newVal", "handleClose", "handleInput", "handleConfirm", "_component_el_dialog", "_createSlots", "rows", "onInput", "fn", "__exports__", "sourceFile", "parsedParams", "searchText", "<PERSON><PERSON><PERSON>", "selectedPara<PERSON>", "paramValueDialogVisible", "currentParamValue", "paramTable", "ecuList", "ecus", "Set", "map", "p", "ecuName", "Array", "from", "sort", "dialogTitle", "fileName", "filteredParams", "params", "filter", "search", "toLowerCase", "includes", "description", "nextTick", "selectAllCurrentEcuParams", "handleEcuChange", "currentEcuParams", "for<PERSON>ach", "toggleRowSelection", "handleSelectionChange", "selection", "formatValue", "showParamValue", "param", "applySelectedParams", "ElMessage", "success", "warning", "_component_el_option", "_component_el_select", "footer", "disabled", "_Fragment", "_renderList", "ecu", "prefix", "_unref", "Search", "ref_key", "height", "onSelectionChange", "ParamValueViewer", "_component_el_tag", "selectDataFiles", "dataFiles", "clearAllDataFiles", "getFileTypeTagType", "fileType", "toUpperCase", "path", "getStatusTagType", "status", "getStatusText", "SourceFileStatus", "Pending", "Error", "parseDataFile", "id", "Parsed", "viewDataFileDetails", "openDataFileFolder", "removeDataFile", "defineComponent", "loadDataFiles", "async", "response", "appApi", "cinParameter", "getSourceFiles", "error", "console", "selectSourceFiles", "filePaths", "addDataFiles", "addSourceFiles", "newFiles", "file", "existingIndex", "findIndex", "f", "push", "find", "fileId", "Parsing", "parseSourceFile", "updatedFile", "index", "explorer", "openExplorer", "filePath", "removeSourceFile", "fileIndex", "splice", "ElMessageBox", "confirm", "confirmButtonText", "cancelButtonText", "clearSourceFiles", "SourceFileType", "Arxml", "Sddb", "Ldf", "refreshDataFiles", "onMounted", "components", "ParamParseDialog", "DataFileManager", "Document", "templates", "sourceType", "selectedCate<PERSON><PERSON>", "selectedTemplateId", "sourceFilePath", "parsing", "parameterSources", "modifiedParams", "currentComplexParamName", "categorySet", "t", "category", "c", "children", "template", "filteredTemplates", "keyword", "v", "modifiedParamsCount", "hasUnsavedChanges", "loadTemplates", "getTemplates", "onCategoryChange", "onSourceTypeChange", "clear", "selectFile", "parseSelectedFile", "request", "parseFile", "result", "variable", "loadSelectedTemplate", "getTemplatesByCategory", "templateId", "paramName", "add", "processFile", "outputFilePath", "source", "trimmedValue", "trim", "startsWith", "getFileName", "split", "pop", "getAppliedParamsCount", "count", "Object", "keys"], "sourceRoot": ""}