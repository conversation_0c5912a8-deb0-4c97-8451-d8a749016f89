﻿using Alsi.Tab.Kit.Web.Mapping;
using AutoMapper;

namespace Alsi.Tab.Kit.Web
{
    public static class MapperEnv
    {
        public static IMapper Mapper { get; private set; }

        static MapperEnv()
        {
            InitializeMapper();
        }

        private static void InitializeMapper()
        {
            var config = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile<MappingProfile>();
            });

            Mapper = config.CreateMapper();
        }
    }
}
