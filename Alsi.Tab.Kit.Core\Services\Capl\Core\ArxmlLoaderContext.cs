﻿using ArxmlModel = AUTOSAR;

namespace Alsi.Tab.Kit.Core.Services.Capl.Core
{
    public class ArxmlLoaderContext
    {
        public ArxmlLoaderContext(ArxmlModel arxmlModel, string source, IParamCollection collection, int order, string[] arxmlEcuNodes)
        {
            ArxmlModel = arxmlModel;
            Source = source;
            Collection = collection;
            Order = order;
            ArxmlEcuNodes = arxmlEcuNodes;
        }

        public ArxmlModel ArxmlModel { get; set; }
        public string Source { get; set; }
        public IParamCollection Collection { get; set; }
        public int Order { get; set; }
        public string[] ArxmlEcuNodes { get; set; }
    }
}
