using System;

namespace Alsi.Tab.Kit.Core.Models
{
    /// <summary>
    /// 外部应用程序模型
    /// </summary>
    public class ExternalApp
    {
        /// <summary>
        /// 应用程序唯一标识（运行时计算）
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 可执行文件相对路径（相对于索引文件）
        /// </summary>
        public string ExePath { get; set; } = string.Empty;

        /// <summary>
        /// 应用程序名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 应用程序描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 图标文件名
        /// </summary>
        public string Icon { get; set; } = string.Empty;

        /// <summary>
        /// 标签列表
        /// </summary>
        public string[] Tags { get; set; } = Array.Empty<string>();

        /// <summary>
        /// 可执行文件完整路径（运行时计算）
        /// </summary>
        public string FullExePath { get; set; } = string.Empty;

        /// <summary>
        /// 图标文件完整路径（运行时计算）
        /// </summary>
        public string FullIconPath { get; set; } = string.Empty;

        /// <summary>
        /// 可执行文件是否存在
        /// </summary>
        public bool ExeExists { get; set; }

        /// <summary>
        /// 图标文件是否存在
        /// </summary>
        public bool IconExists { get; set; }

        /// <summary>
        /// 工作目录（exe文件所在目录）
        /// </summary>
        public string WorkingDirectory { get; set; } = string.Empty;
    }

    /// <summary>
    /// 外部应用程序索引文件
    /// </summary>
    public class ExternalAppsIndex
    {
        public ExternalApp[] Apps { get; set; } = Array.Empty<ExternalApp>();
    }
}
