<template>
  <div class="parameter-tool">
    <!-- 参数编辑主区域 -->
    <div class="main-content">
      <!-- CIN 参数列表卡片 -->
      <div class="cin-parameters-section">
        <div class="section-header">
          <h4>CIN 参数列表</h4>
          <div class="header-actions">
          </div>
        </div>

        <div class="cin-operations-content">
          <div class="operation-row">
            <el-cascader v-model="selectedTemplatePath" size="small" :options="cascaderOptions"
              v-if="categories.length > 0" placeholder="选择 CIN 模板" style="width: 260px; margin-right: 16px;"
              @change="handleTemplateChange" clearable />

            <el-button type="primary" size="small" @click="selectLocalFile">选择本地 CIN 文件</el-button>
            <el-button type="primary" size="small" @click="exportCinFile" style="margin-left: auto;"
              :loading="processing">导出 CIN</el-button>
          </div>
        </div>

        <el-divider style="margin: 8px 0" />

        <!-- 搜索和过滤 -->
        <div class="search-bar" v-if="variables.length > 0">
          <el-input v-model="searchKeyword" size="small" placeholder="搜索参数名称..." clearable style="width: 260px;" />
          <div class="filter-info">
            <span>共 {{ filteredVariables.length }} / {{ variables.length }} 个参数</span>
          </div>
        </div>

        <!-- 参数表格 -->
        <div class="parameters-table">
          <el-table :data="filteredVariables" border style="width: 100%; height: 100%;" size="small">
            <el-table-column prop="name" label="参数名" width="200" show-overflow-tooltip />
            <el-table-column prop="type" label="类型" width="200" show-overflow-tooltip />
            <el-table-column label="参数值" min-width="200">
              <template #default="scope">
                <div class="param-value-cell">
                  <el-button v-if="isComplexParameter(scope.row)" type="text" size="small"
                    @click="editComplexParameter(scope.row)" class="edit-button">
                    编辑
                  </el-button>
                  <el-input v-model="parameterValues[scope.row.name]" placeholder="输入参数值" size="small"
                    @change="onParameterChange(scope.row.name)" class="param-value-input" />
                </div>
              </template>
            </el-table-column>
            <el-table-column label="参数值来源" width="150" show-overflow-tooltip>
              <template #default="scope">
                <div v-if="getParameterSource(scope.row.name)" class="parameter-source">
                  <el-icon>
                    <document />
                  </el-icon>
                  <span>{{ getParameterSource(scope.row.name) }}</span>
                </div>
                <span v-else class="no-source">CIN 文件</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 数据文件管理 -->
      <DataFileManager @parse-result="handleParseResult" />
    </div>

    <!-- 参数导入结果弹窗 -->
    <ParamParseDialog v-model="parseDialogVisible" :source-file="currentParseFile" :parsed-params="currentParsedParams"
      @apply-params="applyParsedParams" />

    <!-- 复杂参数编辑弹窗 -->
    <ParamValueViewer v-model="complexParamDialogVisible" :value="currentComplexParamValue" :readonly="false"
      :title="complexParamTitle" @confirm="handleComplexParamConfirm" />
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, onMounted } from "vue";
import {
  appApi,
  CinTemplate,
  CaplVariable,
  CinParameterParseRequest,
  CinParameterRequest,
  SourceFile,
  SourceFileType,
  SourceFileStatus,
  ParsedParam
} from "@/api/appApi";
import { ElMessage } from "element-plus";
import { Document } from '@element-plus/icons-vue';
import ParamParseDialog from "@/components/ParamParseDialog.vue";
import DataFileManager from "@/components/DataFileManager.vue";
import ParamValueViewer from "@/components/ParamValueViewer.vue";

export default defineComponent({
  name: "ParameterToolView",
  components: {
    ParamParseDialog,
    DataFileManager,
    ParamValueViewer,
    Document
  },
  setup() {
    // 数据
    const templates = ref<CinTemplate[]>([]);
    const sourceType = ref<string>("template");
    const selectedCategory = ref<string>("");
    const selectedTemplateId = ref<string>("");
    const selectedTemplatePath = ref<string[]>([]);
    const filePath = ref<string>("");
    const variables = ref<CaplVariable[]>([]);
    const parameterValues = ref<{ [key: string]: string }>({});
    const sourceFilePath = ref<string>("");
    const searchKeyword = ref<string>("");

    // 状态
    const parsing = ref(false);
    const processing = ref(false);

    // 源文件管理相关
    const parseDialogVisible = ref(false);
    const currentParseFile = ref<SourceFile | null>(null);
    const currentParsedParams = ref<ParsedParam[]>([]);
    const parameterSources = ref<{ [key: string]: string }>({});

    // 新增状态
    const modifiedParams = ref<Set<string>>(new Set());

    // 复杂参数编辑相关
    const complexParamDialogVisible = ref(false);
    const currentComplexParamValue = ref<string>('');
    const currentComplexParamName = ref<string>('');
    const complexParamTitle = computed(() => `编辑参数 - ${currentComplexParamName.value}`);

    // 计算属性
    const categories = computed(() => {
      const categorySet = new Set(templates.value.map(t => t.category));
      return Array.from(categorySet).filter(c => c);
    });

    const cascaderOptions = computed(() => {
      return categories.value.map(category => ({
        value: category,
        label: category,
        children: templates.value
          .filter(t => t.category === category)
          .map(template => ({
            value: template.id,
            label: template.name
          }))
      }));
    });

    const filteredTemplates = computed(() => {
      if (!selectedCategory.value) return [];
      return templates.value.filter(t => t.category === selectedCategory.value);
    });

    const filteredVariables = computed(() => {
      if (!searchKeyword.value) {
        return variables.value;
      }
      const keyword = searchKeyword.value.toLowerCase();
      return variables.value.filter(v =>
        v.name.toLowerCase().includes(keyword)
      );
    });

    const modifiedParamsCount = computed(() => modifiedParams.value.size);

    const hasUnsavedChanges = computed(() => modifiedParams.value.size > 0);

    // 方法
    const loadTemplates = async () => {
      try {
        const response = await appApi.cinParameter.getTemplates();
        templates.value = response.data;

        // 自动选择第一个类别和模板
        if (categories.value.length > 0) {
          selectedCategory.value = categories.value[0];
          onCategoryChange();
        }
      } catch (error) {
        console.error('加载模板失败:', error);
      }
    };

    const onSourceTypeChange = () => {
      // 清空之前的数据
      variables.value = [];
      parameterValues.value = {};
      sourceFilePath.value = "";
      modifiedParams.value.clear();
    };

    const onCategoryChange = () => {
      if (filteredTemplates.value.length > 0) {
        selectedTemplateId.value = filteredTemplates.value[0].id;
      } else {
        selectedTemplateId.value = "";
      }
    };

    const selectLocalFile = async () => {
      try {
        const response = await appApi.cinParameter.selectFile();
        if (!response.data) {
          return;
        }

        // 清空之前的选择
        selectedTemplateId.value = '';
        selectedCategory.value = '';
        selectedTemplatePath.value = [];

        filePath.value = response.data;
        // 自动解析选择的文件
        await parseSelectedFile();
      } catch (error) {
        console.error('选择文件失败:', error);
      }
    };

    const parseSelectedFile = async () => {
      if (!filePath.value) return;

      parsing.value = true;
      try {
        const request: CinParameterParseRequest = {
          sourceType: "file",
          filePath: filePath.value
        };

        const response = await appApi.cinParameter.parseFile(request);
        const result = response.data;

        variables.value = result.variables;
        sourceFilePath.value = result.sourceFilePath;

        // 初始化参数值
        parameterValues.value = {};
        result.variables.forEach(variable => {
          parameterValues.value[variable.name] = variable.value || "";
        });

        modifiedParams.value.clear();
        ElMessage.success(`成功解析 ${result.variables.length} 个参数`);
      } catch (error) {
        console.error('解析文件失败:', error);
        ElMessage.error('解析文件失败');
      } finally {
        parsing.value = false;
      }
    };

    const handleTemplateChange = (value: string[]) => {
      if (value && value.length === 2) {
        selectedCategory.value = value[0];
        selectedTemplateId.value = value[1];
        loadSelectedTemplate();
      }
    };

    const getTemplatesByCategory = (category: string) => {
      return templates.value.filter(t => t.category === category);
    };

    const loadSelectedTemplate = async () => {
      if (!selectedTemplateId.value) return;

      parsing.value = true;
      try {
        const request: CinParameterParseRequest = {
          sourceType: "template",
          templateId: selectedTemplateId.value,
          filePath: ""
        };

        const response = await appApi.cinParameter.parseFile(request);
        const result = response.data;

        variables.value = result.variables;
        sourceFilePath.value = result.sourceFilePath;

        // 初始化参数值
        parameterValues.value = {};
        result.variables.forEach(variable => {
          parameterValues.value[variable.name] = variable.value || "";
        });

        modifiedParams.value.clear();
        ElMessage.success(`成功加载模板 ${result.variables.length} 个参数`);
      } catch (error) {
        console.error('加载模板失败:', error);
        ElMessage.error('加载模板失败');
      } finally {
        parsing.value = false;
      }
    };

    const onParameterChange = (paramName: string) => {
      modifiedParams.value.add(paramName);
    };

    const exportCinFile = async () => {
      processing.value = true;
      try {
        const request: CinParameterRequest = {
          sourceType: sourceType.value,
          templateId: sourceType.value === "template" ? selectedTemplateId.value : undefined,
          filePath: sourceType.value === "file" ? filePath.value : "",
          parameterValues: parameterValues.value
        };

        const response = await appApi.cinParameter.processFile(request);
        const result = response.data;

        ElMessage.success(`文件导出成功！`);
        modifiedParams.value.clear();

        await appApi.explorer.openExplorer(result.outputFilePath);

      } catch (error) {
        console.error('导出文件失败:', error);
        ElMessage.error('导出文件失败');
      } finally {
        processing.value = false;
      }
    };

    // 源文件管理相关方法
    const handleParseResult = (file: SourceFile, params: ParsedParam[]) => {
      currentParseFile.value = file;
      currentParsedParams.value = params;
      parseDialogVisible.value = true;
    };

    const applyParsedParams = (params: ParsedParam[]) => {
      params.forEach(param => {
        // 查找对应的变量（不区分大小写）
        const variable = variables.value.find(v => v.name.toLowerCase() === param.name.toLowerCase());
        if (variable) {
          // 应用参数值
          parameterValues.value[variable.name] = String(param.value);
          // 记录参数来源
          parameterSources.value[variable.name] = param.source;
          // 标记为已修改
          modifiedParams.value.add(variable.name);
        }
      });

      // 注意：文件列表现在由DataFileManager组件管理

      ElMessage.success(`成功应用 ${params.length} 个参数`);
    };

    const getParameterSource = (paramName: string) => {
      return parameterSources.value[paramName];
    };

    // 判断参数是否为复杂类型
    const isComplexParameter = (variable: CaplVariable) => {
      const value = parameterValues.value[variable.name];
      if (!value) return false;

      // 检查是否是结构体类型（包含大括号）
      const trimmedValue = value.trim();
      if (trimmedValue.startsWith('{') && trimmedValue.includes(',')) {
        return true;
      }

      // 检查是否是JSON格式
      if (trimmedValue.startsWith('{') || trimmedValue.startsWith('[')) {
        try {
          JSON.parse(trimmedValue);
          return true;
        } catch {
          // 不是有效的JSON，但可能是CIN格式的结构体
          return trimmedValue.includes(',') || trimmedValue.includes('{');
        }
      }

      // 检查变量类型是否为复杂类型
      const type = variable.type.toLowerCase();
      return type.includes('struct') || type.includes('array') || type.includes('[]');
    };

    // 编辑复杂参数
    const editComplexParameter = (variable: CaplVariable) => {
      currentComplexParamName.value = variable.name;
      currentComplexParamValue.value = parameterValues.value[variable.name] || '';
      complexParamDialogVisible.value = true;
    };

    // 处理复杂参数编辑确认
    const handleComplexParamConfirm = (value: string) => {
      if (currentComplexParamName.value) {
        parameterValues.value[currentComplexParamName.value] = value;
        onParameterChange(currentComplexParamName.value);
      }
    };

    const getFileName = (path: string) => {
      if (!path) return '';
      return path.split(/[\\/]/).pop() || '';
    };

    const getFileTypeTagType = (fileType: SourceFileType) => {
      switch (fileType) {
        case SourceFileType.Arxml: return 'primary';
        case SourceFileType.Sddb: return 'success';
        case SourceFileType.Ldf: return 'warning';
        default: return '';
      }
    };

    const getAppliedParamsCount = (file: SourceFile) => {
      // 计算该文件已应用的参数数量
      let count = 0;
      Object.keys(parameterSources.value).forEach(paramName => {
        if (parameterSources.value[paramName] === file.fileName) {
          count++;
        }
      });
      return count;
    };







    onMounted(async () => {
      await loadTemplates();
    });

    return {
      templates,
      sourceType,
      selectedCategory,
      selectedTemplateId,
      selectedTemplatePath,
      filePath,
      variables,
      parameterValues,
      sourceFilePath,
      searchKeyword,
      parsing,
      processing,
      categories,
      cascaderOptions,
      filteredTemplates,
      filteredVariables,
      modifiedParamsCount,
      hasUnsavedChanges,
      onSourceTypeChange,
      onCategoryChange,
      selectLocalFile,
      handleTemplateChange,
      getTemplatesByCategory,
      loadSelectedTemplate,
      onParameterChange,
      exportCinFile,
      getFileName,
      getFileTypeTagType,
      getAppliedParamsCount,
      // 源文件管理相关
      parseDialogVisible,
      currentParseFile,
      currentParsedParams,
      handleParseResult,
      applyParsedParams,
      getParameterSource,
      // 复杂参数编辑相关
      isComplexParameter,
      editComplexParameter,
      handleComplexParamConfirm,
      complexParamDialogVisible,
      currentComplexParamValue,
      complexParamTitle,
      // 图标和枚举
      Document,
      SourceFileStatus
    };
  },
});
</script>

<style scoped>
.parameter-tool {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--el-bg-color-page);
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-base);
  flex-shrink: 0;
}

.toolbar-left {
  display: flex;
  align-items: center;
}

.toolbar-right {
  display: flex;
  gap: 8px;
}

.current-file-info {
  padding: 8px 20px;
  background: var(--el-color-primary-light-9);
  border-bottom: 1px solid var(--el-border-color-lighter);
  flex-shrink: 0;
}

.file-info-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-icon {
  color: var(--el-color-primary);
}

.file-name {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.main-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  padding: 20px;
  min-height: 0;
  /* 允许flex子元素收缩 */
}

.search-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-info {
  display: flex;
  gap: 16px;
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

.import-info {
  color: var(--el-color-primary);
}



.current-value {
  color: var(--el-text-color-regular);
  font-style: italic;
}

.parameter-source {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: var(--el-color-primary);
}

.param-value-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.param-value-input {
  flex: 1;
}

.edit-button {
  flex-shrink: 0;
  padding: 0 4px;
}

.no-source {
  color: var(--el-text-color-placeholder);
  font-style: italic;
}

/* CIN 参数列表卡片样式 */
.cin-parameters-section {
  margin-bottom: 16px;
  padding: 16px;
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color);
  border-radius: 6px;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  /* 允许flex子元素收缩 */
}

.cin-operations-content {
  margin-top: 12px;
  flex-shrink: 0;
  /* 不允许收缩 */
}

.operation-row {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.search-bar {
  margin-bottom: 12px;
  flex-shrink: 0;
  /* 不允许收缩 */
}

.parameters-table {
  flex: 1;
  overflow: hidden;
  min-height: 0;
  /* 允许flex子元素收缩 */
}



.imported-files-section {
  margin-top: 16px;
  padding: 16px;
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color);
  border-radius: 6px;
}

.imported-files-section h4 {
  margin: 0 0 12px 0;
  color: var(--el-text-color-primary);
  font-size: 14px;
  font-weight: 600;
}

.imported-files-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.imported-file-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: var(--el-fill-color-light);
  border-radius: 4px;
}

.imported-file-item .file-name {
  font-weight: 500;
}

.param-count {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.file-actions {
  margin-left: auto;
  display: flex;
  gap: 8px;
}

.template-selection {
  display: flex;
}

.category-list {
  width: 200px;
  border-right: 1px solid var(--el-border-color-lighter);
  overflow-y: auto;
}

.category-item {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.category-item:hover {
  background: var(--el-fill-color-light);
}

.category-item.active {
  background: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
  font-weight: 500;
}

.template-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.template-item {
  padding: 12px;
  margin-bottom: 8px;
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 4px;
  cursor: pointer;
}

.template-item:hover {
  border-color: var(--el-color-primary);
}

.template-item.active {
  border-color: var(--el-color-primary);
  background: var(--el-color-primary-light-9);
}

.template-name {
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.template-desc {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .toolbar {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .toolbar-left,
  .toolbar-right {
    justify-content: center;
  }

  .search-bar {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .template-selection {
    flex-direction: column;
    height: auto;
  }

  .category-list {
    width: auto;
    border-right: none;
    border-bottom: 1px solid var(--el-border-color-lighter);
    max-height: 150px;
  }
}
</style>
