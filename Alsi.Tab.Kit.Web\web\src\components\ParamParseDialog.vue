<template>
  <el-dialog
    v-model="visible"
    title="参数解析结果"
    width="80%"
    :before-close="handleClose"
    destroy-on-close
  >
    <div class="dialog-content">
      <!-- ECU选择和搜索 -->
      <div class="filter-section">
        <el-select 
          v-model="selectedEcu" 
          placeholder="选择 ECU" 
          style="width: 200px;"
          @change="handleEcuChange"
        >
          <el-option
            v-for="ecu in ecuList"
            :key="ecu"
            :label="ecu"
            :value="ecu"
          />
        </el-select>
        
        <el-input
          v-model="searchText"
          placeholder="搜索参数名称..."
          clearable
          style="width: 300px"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>

      <!-- 参数列表 -->
      <div class="param-list">
        <el-table
          :data="filteredParams"
          height="400"
          @selection-change="handleSelectionChange"
          row-key="name"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="name" label="参数名" width="200" show-overflow-tooltip />
          <el-table-column prop="paramType" label="类型" width="100">
            <template #default="{ row }">
              <el-tag :type="getParamTypeTagType(row.paramType)" size="small">
                {{ row.paramType }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="value" label="值" min-width="200" show-overflow-tooltip>
            <template #default="{ row }">
              <div class="param-value">
                <span v-if="row.paramType !== 'Json'">{{ formatValue(row.value) }}</span>
                <el-button 
                  v-else 
                  type="text" 
                  size="small"
                  @click="showJsonValue(row)"
                >
                  查看JSON
                </el-button>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="description" label="描述" min-width="150" show-overflow-tooltip />
        </el-table>
      </div>

      <!-- 批量操作 -->
      <div class="batch-actions" v-if="selectedParams.length > 0">
        <el-alert 
          :title="`已选择 ${selectedParams.length} 个参数`" 
          type="info" 
          :closable="false"
          show-icon
        />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          type="primary" 
          @click="applySelectedParams"
          :disabled="selectedParams.length === 0"
        >
          应用参数 ({{ selectedParams.length }})
        </el-button>
      </div>
    </template>

    <!-- JSON 查看弹窗 -->
    <el-dialog
      v-model="jsonDialogVisible"
      title="JSON 参数值"
      width="60%"
      append-to-body
    >
      <el-input
        v-model="jsonValue"
        type="textarea"
        :rows="15"
        readonly
        style="font-family: 'Courier New', monospace;"
      />
    </el-dialog>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import { SourceFile, ParsedParam, SourceFileType, ParamType } from '@/api/appApi';

// Props
interface Props {
  modelValue: boolean;
  sourceFile: SourceFile | null;
  parsedParams: ParsedParam[];
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean];
  'apply-params': [params: ParsedParam[]];
}>();

// 数据
const visible = ref(false);
const searchText = ref('');
const selectedEcu = ref('');
const selectedParams = ref<ParsedParam[]>([]);
const jsonDialogVisible = ref(false);
const jsonValue = ref('');

// 计算属性
const ecuList = computed(() => {
  const ecus = new Set(props.parsedParams.map(p => p.ecuName));
  return Array.from(ecus).sort();
});

const filteredParams = computed(() => {
  let params = props.parsedParams;

  // 按ECU筛选
  if (selectedEcu.value) {
    params = params.filter(p => p.ecuName === selectedEcu.value);
  }

  // 按名称搜索
  if (searchText.value) {
    const search = searchText.value.toLowerCase();
    params = params.filter(p => 
      p.name.toLowerCase().includes(search) ||
      p.description.toLowerCase().includes(search)
    );
  }

  return params;
});

// 监听器
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal;
  if (newVal) {
    // 重置状态
    searchText.value = '';
    selectedParams.value = [];
    // 默认选择第一个ECU
    if (ecuList.value.length > 0) {
      selectedEcu.value = ecuList.value[0];
      // 默认全选当前ECU的参数
      selectAllCurrentEcuParams();
    }
  }
});

watch(visible, (newVal) => {
  emit('update:modelValue', newVal);
});

// 方法
const handleEcuChange = () => {
  // 切换ECU时默认全选
  selectAllCurrentEcuParams();
};

const selectAllCurrentEcuParams = () => {
  selectedParams.value = [...filteredParams.value];
};

const handleClose = () => {
  visible.value = false;
};

const handleSelectionChange = (selection: ParsedParam[]) => {
  selectedParams.value = selection;
};

const getFileTypeTagType = (fileType?: SourceFileType) => {
  switch (fileType) {
    case SourceFileType.Arxml: return 'primary';
    case SourceFileType.Sddb: return 'success';
    case SourceFileType.Ldf: return 'warning';
    default: return '';
  }
};

const getParamTypeTagType = (paramType: ParamType) => {
  switch (paramType) {
    case ParamType.String: return '';
    case ParamType.Integer: return 'success';
    case ParamType.Double: return 'success';
    case ParamType.Boolean: return 'warning';
    case ParamType.Json: return 'primary';
    case ParamType.Array: return 'info';
    case ParamType.Object: return 'info';
    default: return '';
  }
};

const formatValue = (value: any) => {
  if (value === null || value === undefined) {
    return '';
  }
  
  if (typeof value === 'string') {
    return value.length > 50 ? value.substring(0, 50) + '...' : value;
  }
  
  return String(value);
};

const showJsonValue = (param: ParsedParam) => {
  try {
    if (typeof param.value === 'string') {
      // 尝试格式化JSON字符串
      const parsed = JSON.parse(param.value);
      jsonValue.value = JSON.stringify(parsed, null, 2);
    } else {
      // 直接序列化对象
      jsonValue.value = JSON.stringify(param.value, null, 2);
    }
  } catch {
    // 如果不是有效的JSON，直接显示原值
    jsonValue.value = String(param.value);
  }
  
  jsonDialogVisible.value = true;
};

const applySelectedParams = () => {
  if (selectedParams.value.length === 0) {
    ElMessage.warning('请先选择要应用的参数');
    return;
  }

  emit('apply-params', selectedParams.value);
  ElMessage.success(`成功应用 ${selectedParams.value.length} 个参数`);
  handleClose();
};
</script>

<style scoped>
.dialog-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.filter-section {
  display: flex;
  gap: 16px;
  align-items: center;
}

.param-list {
  border: 1px solid var(--el-border-color-base);
  border-radius: 6px;
}

.param-value {
  max-width: 200px;
  word-break: break-all;
}

.batch-actions {
  margin-top: 8px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}
</style>
