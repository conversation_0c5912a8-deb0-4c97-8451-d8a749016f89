<template>
  <el-dialog v-model="visible" :title="dialogTitle" width="80%" :before-close="handleClose" destroy-on-close>
    <div class="dialog-content">
      <!-- ECU选择和搜索 -->
      <div class="filter-section">
        <el-select v-model="selectedEcu" size="small" placeholder="选择 ECU" style="width: 100px;"
          @change="handleEcuChange">
          <el-option v-for="ecu in ecuList" :key="ecu" :label="ecu" :value="ecu" />
        </el-select>

        <el-input v-model="searchText" placeholder="搜索参数名称..." clearable size="small" style="width: 260px">
          <template #prefix>
            <el-icon>
              <Search />
            </el-icon>
          </template>
        </el-input>

        <div class="selection-info">
          已选择 {{ selectedParams.length }}/{{ filteredParams.length }} 个参数
        </div>
      </div>

      <!-- 参数列表 -->
      <div class="param-list">
        <el-table ref="paramTable" :data="filteredParams" height="400" @selection-change="handleSelectionChange"
          row-key="name" size="small">
          <el-table-column type="selection" width="55" />
          <el-table-column prop="name" label="参数名" width="200" show-overflow-tooltip />
          <el-table-column prop="paramType" label="数据类型" width="200" show-overflow-tooltip />
          <el-table-column prop="value" label="参数值" min-width="200">
            <template #default="{ row }">
              <div class="param-value-cell">
                <el-button type="text" size="small" @click="showParamValue(row)" class="view-button">
                  查看
                </el-button>
                <span class="param-value-text" :title="formatValue(row.value)">
                  {{ formatValue(row.value) }}
                </span>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>


    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="applySelectedParams" :disabled="selectedParams.length === 0">
          应用参数 ({{ selectedParams.length }})
        </el-button>
      </div>
    </template>

    <!-- 参数值查看弹窗 -->
    <ParamValueViewer v-model="paramValueDialogVisible" :value="currentParamValue" :readonly="true" title="参数值" />
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import { SourceFile, ParsedParam, SourceFileType, ParamType } from '@/api/appApi';
import ParamValueViewer from './ParamValueViewer.vue';

// Props
interface Props {
  modelValue: boolean;
  sourceFile: SourceFile | null;
  parsedParams: ParsedParam[];
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean];
  'apply-params': [params: ParsedParam[]];
}>();

// 数据
const visible = ref(false);
const searchText = ref('');
const selectedEcu = ref('');
const selectedParams = ref<ParsedParam[]>([]);
const paramValueDialogVisible = ref(false);
const currentParamValue = ref<any>('');
const paramTable = ref();

// 计算属性
const ecuList = computed(() => {
  const ecus = new Set(props.parsedParams.map(p => p.ecuName));
  return Array.from(ecus).sort();
});

const dialogTitle = computed(() => {
  if (!props.sourceFile) {
    return '参数解析结果';
  }

  const fileName = props.sourceFile.fileName;
  return `参数解析结果 - ${fileName}`;
});

const filteredParams = computed(() => {
  let params = props.parsedParams;

  // 按ECU筛选
  if (selectedEcu.value) {
    params = params.filter(p => p.ecuName === selectedEcu.value);
  }

  // 按名称搜索
  if (searchText.value) {
    const search = searchText.value.toLowerCase();
    params = params.filter(p =>
      p.name.toLowerCase().includes(search) ||
      p.description.toLowerCase().includes(search)
    );
  }

  return params;
});

// 监听器
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal;
  if (newVal) {
    // 重置状态
    searchText.value = '';
    selectedParams.value = [];
    // 默认选择第一个ECU
    if (ecuList.value.length > 0) {
      selectedEcu.value = ecuList.value[0];
      // 默认全选当前ECU的参数
      nextTick(() => {
        selectAllCurrentEcuParams();
      });
    }
  }
});

watch(visible, (newVal) => {
  emit('update:modelValue', newVal);
});

watch(selectedEcu, () => {
  // ECU切换时默认全选当前ECU的参数
  nextTick(() => {
    selectAllCurrentEcuParams();
  });
});

// 方法
const handleEcuChange = () => {
  // 切换ECU时默认全选
  selectAllCurrentEcuParams();
};

const selectAllCurrentEcuParams = () => {
  // 全选当前ECU的参数
  if (paramTable.value) {
    const currentEcuParams = filteredParams.value;
    currentEcuParams.forEach(row => {
      paramTable.value.toggleRowSelection(row, true);
    });
  }
};

const handleClose = () => {
  visible.value = false;
};

const handleSelectionChange = (selection: ParsedParam[]) => {
  selectedParams.value = selection;
};

const getFileTypeTagType = (fileType?: SourceFileType) => {
  switch (fileType) {
    case SourceFileType.Arxml: return 'primary';
    case SourceFileType.Sddb: return 'success';
    case SourceFileType.Ldf: return 'warning';
    default: return '';
  }
};

const formatValue = (value: any) => {
  if (value === null || value === undefined) {
    return '';
  }

  return String(value);
};

const showParamValue = (param: ParsedParam) => {
  currentParamValue.value = param.value;
  paramValueDialogVisible.value = true;
};

const applySelectedParams = () => {
  if (selectedParams.value.length === 0) {
    ElMessage.warning('请先选择要应用的参数');
    return;
  }

  emit('apply-params', selectedParams.value);
  ElMessage.success(`成功应用 ${selectedParams.value.length} 个参数`);
  handleClose();
};
</script>

<style scoped>
.dialog-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.filter-section {
  display: flex;
  gap: 16px;
  align-items: center;
}

.param-list {
  border: 1px solid var(--el-border-color-base);
  border-radius: 6px;
}

.selection-info {
  font-size: 12px;
  color: var(--el-text-color-regular);
  margin-left: auto;
}

.param-value-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.param-value-text {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.view-button {
  flex-shrink: 0;
  padding: 0 4px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}
</style>
