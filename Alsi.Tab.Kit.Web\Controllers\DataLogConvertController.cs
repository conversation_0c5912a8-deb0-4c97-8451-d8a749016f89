﻿using Alsi.App.Desktop.Utils;
using Alsi.Common.Utils;
using Alsi.Tab.Kit.Core.Models;
using Alsi.Tab.Kit.Core.Services;
using System;
using System.Web.Http;

namespace Alsi.Tab.Kit.Web.Controllers
{
    public class DataLogConvertController : WebControllerBase
    {
        private readonly DataLogConvertService _dataLogConvertService;

        public DataLogConvertController()
        {
            _dataLogConvertService = new DataLogConvertService();
        }

        [HttpPost]
        [ActionName("select-file")]
        public IHttpActionResult SelectFile()
        {
            if (!UiUtils.SelectFile(out var filePath, "Log File", "*.blf;*.asc"))
            {
                throw new AppException("Please select a log file.");
            }

            return Ok(filePath);
        }

        [HttpPost]
        [ActionName("preview")]
        public IHttpActionResult Preview([FromBody] DataLogProcessRequest request)
        {
            var preview = DataLogConvertService.CalcFileProgresses(request);
            return Ok(preview);
        }

        [HttpPost]
        [ActionName("start")]
        public IHttpActionResult Start([FromBody] DataLogProcessRequest request)
        {
            var result = _dataLogConvertService.StartProcessAsync(request);
            return Ok(result);
        }

        [HttpGet]
        [ActionName("progress")]
        public IHttpActionResult Progress(Guid taskId)
        {
            var progress = _dataLogConvertService.GetProgress(taskId);
            if (progress == null)
            {
                throw new AppException($"找不到任务ID为 {taskId} 的进度信息");
            }

            return Ok(progress);
        }

        [HttpPost]
        [ActionName("cancel")]
        public IHttpActionResult Cancel(Guid taskId)
        {
            var result = _dataLogConvertService.CancelProcess(taskId);
            return Ok(new { Success = result });
        }
    }
}