<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{EEFBA350-C69E-4E95-AFF2-F5637E80DE37}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Alsi.Tab.Kit.Core</RootNamespace>
    <AssemblyName>Alsi.Tab.Kit.Core</AssemblyName>
    <TargetFrameworkVersion>v4.6.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\x64\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <OutputPath>bin\x64\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Numerics" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Models\CinParameter.cs" />
    <Compile Include="Models\CinTemplate.cs" />
    <Compile Include="Models\DataLogModels.cs" />
    <Compile Include="Models\ExternalApp.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Services\Capl\CaplParamConsts.cs" />
    <Compile Include="Services\Capl\CaplParamLoader.cs" />
    <Compile Include="Services\Capl\CaplParamTypes.cs" />
    <Compile Include="Services\Capl\Cin\CinPropAttribute.cs" />
    <Compile Include="Services\Capl\Cin\CinTypes.cs" />
    <Compile Include="Services\Capl\Cin\CinTypeUtils.cs" />
    <Compile Include="Services\Capl\Loaders\ArxmlLoader.cs" />
    <Compile Include="Services\Capl\Core\ArxmlLoaderBase.cs" />
    <Compile Include="Services\Capl\Core\ArxmlLoaderContext.cs" />
    <Compile Include="Services\Capl\Loaders\BusTypeLoader.cs" />
    <Compile Include="Services\Capl\Loaders\BusTypeMapLoader.cs" />
    <Compile Include="Services\Capl\Loaders\CarConfigFrameIdLoader.cs" />
    <Compile Include="Services\Capl\IParamCollection.cs" />
    <Compile Include="Services\Capl\CaplUtils.cs" />
    <Compile Include="Services\Capl\Loaders\CarConfigParamsLoader.cs" />
    <Compile Include="Services\Capl\Loaders\ClusterDiagFramesLoader.cs" />
    <Compile Include="Services\Capl\Loaders\DiagRoutingLoader.cs" />
    <Compile Include="Services\Capl\Loaders\Dids0x2ELoader.cs" />
    <Compile Include="Services\Capl\Loaders\DidsLoader.cs" />
    <Compile Include="Services\Capl\Loaders\DtcExtLoader.cs" />
    <Compile Include="Services\Capl\Loaders\DtcsLoader.cs" />
    <Compile Include="Services\Capl\Loaders\E2ECanFramesLoader.cs" />
    <Compile Include="Services\Capl\Loaders\E2ECarUsageModeInfoLoader.cs" />
    <Compile Include="Services\Capl\Loaders\E2EFrameExtension.cs" />
    <Compile Include="Services\Capl\Loaders\E2ELinFrameLoader.cs" />
    <Compile Include="Services\Capl\Loaders\E2EUbatSimulationFrameLoader.cs" />
    <Compile Include="Services\Capl\Loaders\E2EVehicleSpeedFrameLoader.cs" />
    <Compile Include="Services\Capl\Loaders\LdfLoader.cs" />
    <Compile Include="Services\Capl\Loaders\LoaderUtils.cs" />
    <Compile Include="Services\Capl\Loaders\P4ServerTimeLoader.cs" />
    <Compile Include="Services\Capl\Loaders\PncInfoLoader.cs" />
    <Compile Include="Services\Capl\Loaders\SecurityLevelsLoader.cs" />
    <Compile Include="Services\Capl\Loaders\SignalRoutingLoader.cs" />
    <Compile Include="Services\Capl\Loaders\SwAppTimeParamsLoader.cs" />
    <Compile Include="Services\Capl\Loaders\SwPblTimeParamsLoader.cs" />
    <Compile Include="Services\Capl\Loaders\SwsLoader.cs" />
    <Compile Include="Services\Capl\Loaders\VfcInfoLoader.cs" />
    <Compile Include="Services\Capl\Loaders\VoltageRelatedDtcLoader.cs" />
    <Compile Include="Services\Capl\CaplParamAttribute.cs" />
    <Compile Include="Services\Capl\ParamCollection.cs" />
    <Compile Include="Services\CinParameterService.cs" />
    <Compile Include="Services\CinTemplatesService.cs" />
    <Compile Include="Services\DataLogConvertService.cs" />
    <Compile Include="Services\ExternalAppsService.cs" />
    <Compile Include="Services\SourceFileParamService.cs" />
    <Compile Include="Services\SourceFileService.cs" />
    <Compile Include="Services\UserConfigService.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\Alsi.Common\Alsi.App\Alsi.App.csproj">
      <Project>{2bf46d86-9704-494a-8998-a478b601df80}</Project>
      <Name>Alsi.App</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Alsi.Common\Alsi.Common.Log\Alsi.Common.Log.csproj">
      <Project>{5F059B1D-FA34-4752-8DAE-0B78E6DDF378}</Project>
      <Name>Alsi.Common.Log</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Alsi.Common\Alsi.Common.Parsers\Alsi.Common.Parsers.csproj">
      <Project>{2306abe4-156f-48bf-a890-c67ef1c28c67}</Project>
      <Name>Alsi.Common.Parsers</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Alsi.Common\Alsi.Common.Utils\Alsi.Common.Utils.csproj">
      <Project>{d45049c2-92f3-a823-ad5f-89ee4256d61b}</Project>
      <Name>Alsi.Common.Utils</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="System.Memory">
      <Version>4.5.5</Version>
    </PackageReference>
    <PackageReference Include="System.Runtime.CompilerServices.Unsafe">
      <Version>6.1.1</Version>
    </PackageReference>
    <PackageReference Include="System.Text.Encoding.CodePages">
      <Version>5.0.0</Version>
    </PackageReference>
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>