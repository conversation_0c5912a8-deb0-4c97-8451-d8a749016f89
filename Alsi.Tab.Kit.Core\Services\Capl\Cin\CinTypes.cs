﻿using System;

namespace Alsi.Tab.Kit.Core.Services.Capl.Cin
{
    public static class CinTypes
    {
        //诊断支持的服务结构体
        public class STRU_SID_SUBID
        {
            public byte sessionMask;
            public byte sid;
            public byte subIDcnt;
            public byte[] subID = Array.Empty<byte>();
        };

        //22服务支持的DID结构体
        public class STRU_DID
        {
            public byte sessionMask;

            [CinProp(isHex: true)]
            public int did;
            public int valueLength;
            public byte[] value = Array.Empty<byte>();
        };

        //31服务Parameter的数据结构体
        public class STRU_routine
        {
            public byte subId;
            public int reqLength;
            public int resLength;
            public byte[] reqBytes = Array.Empty<byte>();
        };

        //31服务支持的RID结构体
        public class STRU_RID
        {
            public byte sessionMask;
            public int rid;
            public byte routineType;
            public byte[] securityLevels = Array.Empty<byte>();
            public STRU_routine[] struRoutineList = Array.Empty<STRU_routine>();
        };

        //2F服务IOID的Parameter结构体
        public class STRU_ioControlParameter
        {
            public int ioControlParameter;
            public int reqLength;
            public int resLength;
            public byte[] reqBytes = Array.Empty<byte>();
        };

        //2F服务支持的IOID结构体
        public class STRU_IOID
        {
            public int ioid;
            public STRU_ioControlParameter[] struIOcontrolParamList = Array.Empty<STRU_ioControlParameter>();
        };

        //仿真报文帧结构体
        public class FrameFormat
        {
            public int frameID;
            public byte cycle;
            public byte dataLen;
        };

        //仿真信号结构体
        public class E2EsignalList
        {
            public int startBit;
            public byte length;
            public byte sigType;

        };

        //仿真报文结构体
        public class E2EFrame
        {
            public FrameFormat frameFormat;
            public int dataId;
            public E2EsignalList[] signalList = Array.Empty<E2EsignalList>();
        };

        //19服务snapshot对应DID结构体
        public class STRU_SNAPSHOT_DID
        {
            public int did;
            public byte didLen;
        };

        //19服务snapshot结构体
        public class SNAPSHOT_INFO
        {
            public byte snapshotID;
            public byte snapshotNum;
            public STRU_SNAPSHOT_DID[] snapshotdidList = Array.Empty<STRU_SNAPSHOT_DID>();
        };

        //19服务Extended结构体
        public class STRU_DTC_EXTDATA
        {
            public byte ExtDataRecordNum;
            public byte ExtDataLen;
        };
    }
}
