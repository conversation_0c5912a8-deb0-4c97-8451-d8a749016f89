﻿using Alsi.Common.Parsers.Sddb;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using Alsi.Common.Utils;

namespace Alsi.Tab.Kit.Core.Services.Capl.Params
{
    internal class DtcExtLoader
    {
        public void Load(string ecuNode, string source, SW[] sws, IParamCollection collection, int order)
        {
            var sid = 0x19;
            var targetSubFunctionId = 6;
            var list = new List<STRU_DTC_EXTDATA>();
            foreach (var sw in sws)
            {
                var service = sw.Services.Service
                    .FirstOrDefault(x => byte.TryParse(x.ID, NumberStyles.HexNumber, null, out var serviceId) && serviceId == sid);
                if (service == null)
                {
                    continue;
                }

                foreach (var dtc in service.DTCS.DTC)
                {
                    var id = dtc.ID;
                    if (id.StartsWith("0x"))
                    {
                        id = id.Substring(2);
                    }

                    if (uint.TryParse(id, NumberStyles.HexNumber, null, out var dtcId) && (dtcId % 0x100 == 0x88))
                    {
                        collection.AddValue(ecuNode, CaplParamConsts.BUS_OFF_DTC_CODE, dtcId.ToString(), source, order);
                        break;
                    }
                }

                var subFunction = service.Subfunctions.Subfunction
                    .FirstOrDefault(x => byte.TryParse(x.ID, NumberStyles.HexNumber, null, out var subFunctionId) && subFunctionId == targetSubFunctionId);
                if (subFunction == null)
                {
                    continue;
                }


                foreach (var dataParameter in subFunction.DataParameters.DataParameter)
                {
                    foreach (var innerDataParameter in dataParameter.DataParameters.DataParameter)
                    {
                        if (!byte.TryParse(innerDataParameter.Size, out var size)
                            || !byte.TryParse(innerDataParameter.ID, NumberStyles.HexNumber, null, out var id))
                        {
                            continue;
                        }

                        if (!list.Any(x => x.extDataRecordNum == id))
                        {
                            list.Add(new STRU_DTC_EXTDATA(id, size));
                        }
                    }
                }
            }

            var array = list.OrderBy(x => x.extDataRecordNum).ToArray();

            // 脚本中检查的长度包含 DTCExtDataRecordNumber，需要加上 1
            var result = new List<STRU_DTC_EXTDATA>();
            foreach (var item in array)
            {
                var resultItem = new STRU_DTC_EXTDATA(item.extDataRecordNum, item.extDataLen);
                if (item.extDataRecordNum != 0xFF)
                {
                    resultItem.extDataLen++;
                }
                result.Add(resultItem);
            }

            // 脚本中检查的 Num=0xFF 长度是其它 Num 的长度的和
            var sumLen = result.Where(x => x.extDataRecordNum != 0xFF).Sum(x => x.extDataLen);
            foreach (var item in result)
            {
                if (item.extDataRecordNum == 0xFF)
                {
                    item.extDataLen = (byte)sumLen;
                }
            }

            var value = JsonUtils.Serialize(result.ToArray());
            collection.AddValue(ecuNode, CaplParamConsts.DtcExtData, value, source, order);
        }
    }
}
